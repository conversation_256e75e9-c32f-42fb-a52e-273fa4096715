import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CalendarCheck, Video } from 'lucide-react';
import { parseISO, differenceInSeconds, format, isBefore, isAfter, addMinutes } from 'date-fns';
import { arSA } from 'date-fns/locale';

const SessionReminderModal = ({ session, booking, userType, isOpen, onOpenChange, onJoinSession }) => {
  const [timeRemaining, setTimeRemaining] = useState('');
  const [isJoinable, setIsJoinable] = useState(false);

  useEffect(() => {
    if (!session || !isOpen) return;

    const sessionTime = parseISO(`${session.date}T${session.time}`);

    const updateTimer = () => {
      const now = new Date();
      
      const tenMinutesBefore = new Date(sessionTime.getTime() - 10 * 60 * 1000);
      const sessionEndTime = addMinutes(sessionTime, 90);
      
      const canJoin = isAfter(now, tenMinutesBefore) && isBefore(now, sessionEndTime);
      setIsJoinable(canJoin);

      if (isBefore(now, sessionTime)) {
        const diffSeconds = differenceInSeconds(sessionTime, now);
        const days = Math.floor(diffSeconds / (3600 * 24));
        const hours = Math.floor((diffSeconds % (3600 * 24)) / 3600);
        const minutes = Math.floor((diffSeconds % 3600) / 60);
        const seconds = diffSeconds % 60;
        
        let remainingStr = '';
        if(days > 0) remainingStr += `${days} يوم و `;
        if(hours > 0) remainingStr += `${hours} ساعة و `;
        if(minutes > 0) remainingStr += `${minutes} دقيقة و `;
        remainingStr += `${seconds} ثانية`;
        setTimeRemaining(remainingStr);
      } else {
        setTimeRemaining('بدأت الحصة!');
        if (timerId) clearInterval(timerId);
      }
    };

    updateTimer();
    const timerId = setInterval(updateTimer, 1000);

    return () => clearInterval(timerId);

  }, [session, isOpen]);

  if (!session || !booking) return null;

  const handleJoinClick = () => {
    if (onJoinSession) {
      onJoinSession(booking, session);
    }
  };

  const sessionDateTime = parseISO(`${session.date}T${session.time}`);
  const formattedDate = format(sessionDateTime, "eeee, d MMMM yyyy", { locale: arSA });
  const formattedTime = format(sessionDateTime, "p", { locale: arSA });
  
  const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;
  const relevantParty = userType === 'student' ? `مع المعلم: ${booking.teacherName}` : `مع الطالب/الطلاب: ${studentNames}`;

  const isButtonDisabled = userType === 'student' ? !isJoinable : !booking.zoomLink;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="bg-secondary border-border text-white">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-primary">
            <CalendarCheck />
            تذكير بالدرس القادم
          </DialogTitle>
          <DialogDescription>
            حصتك التالية على وشك البدء. كن مستعداً!
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 text-center space-y-4">
            <h3 className="text-xl font-bold text-white">{booking.subjects.map(s => s.subject).join(', ')}</h3>
            <p className="text-white/80">{relevantParty}</p>
            <p className="text-white/80">{formattedDate} - الساعة {formattedTime}</p>
            <div className="bg-primary/10 border border-primary/30 rounded-lg p-4">
                <p className="text-white/70 text-sm mb-1">الوقت المتبقي</p>
                <p className="text-sm font-bold text-primary tracking-wider" style={{ fontFamily: 'Cairo, sans-serif', fontSize: '14px' }}>{timeRemaining}</p>
            </div>
        </div>
        <div className="flex justify-between items-center mt-4">
          <Button 
            onClick={handleJoinClick} 
            disabled={isButtonDisabled}
            className="bg-green-600 hover:bg-green-700 text-white disabled:bg-gray-500"
          >
            <Video className="ml-2 h-4 w-4" />
            الانضمام للدرس
          </Button>
          <Button variant="outline" onClick={() => onOpenChange(false)}>إغلاق</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
};

export default SessionReminderModal;