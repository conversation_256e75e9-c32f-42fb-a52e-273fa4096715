import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useExam } from '@/contexts/ExamContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Helmet } from 'react-helmet';
import { CheckCircle, XCircle, ArrowRight } from 'lucide-react';

const AdminExamsResults = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const { getExamById, results } = useExam();
  const { students } = useAuth();

  const exam = getExamById(examId);
  const examResults = results.filter(r => r.examId === examId);

  const getStudentName = (email) => {
    const student = students.find(s => s.email === email);
    return student ? student.name : email;
  };

  if (!exam) {
    return <div className="gradient-bg min-h-screen flex items-center justify-center text-white">جاري تحميل النتائج...</div>;
  }

  return (
    <>
      <Helmet>
        <title>نتائج اختبار: {exam.title}</title>
      </Helmet>
      <div className="gradient-bg min-h-screen p-8">
        <div className="glass-effect rounded-xl p-8 max-w-4xl mx-auto text-white">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold mb-2">نتائج اختبار: {exam.title}</h1>
              <p className="text-white/70">{examResults.length} طالب أكملوا الاختبار.</p>
            </div>
            <Button variant="outline" className="border-border text-white hover:bg-secondary" onClick={() => navigate('/admin')}>
                العودة للوحة التحكم <ArrowRight className="h-4 w-4 mr-2" />
            </Button>
          </div>
          
          <div className="space-y-4">
            {examResults.length > 0 ? (
              examResults.map(result => (
                <div key={result.id} className="bg-secondary/50 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <p className="font-bold">{getStudentName(result.studentEmail)}</p>
                    <p className="text-lg font-bold text-primary">{result.score} <span className="text-sm text-white/70">/ {result.totalPoints}</span></p>
                  </div>
                  <div className="text-xs text-white/60 mt-1">
                    تاريخ التسليم: {new Date(result.submittedAt).toLocaleString('ar-SA')}
                  </div>
                  <details className="mt-2 text-sm">
                    <summary className="cursor-pointer text-white/80 hover:text-white">عرض التفاصيل</summary>
                    <div className="space-y-2 mt-2 pt-2 border-t border-white/10">
                        {exam.questions.map((q, qIndex) => (
                           <div key={qIndex} className={`p-2 rounded ${result.answers[qIndex] === q.correctAnswerIndex ? 'bg-green-500/10' : 'bg-red-500/10'}`}>
                                <p><strong>سؤال {qIndex+1}:</strong> {result.answers[qIndex] === q.correctAnswerIndex ? <CheckCircle className="inline h-4 w-4 text-green-400" /> : <XCircle className="inline h-4 w-4 text-red-400" />}</p>
                                <p>إجابة الطالب: {q.options[result.answers[qIndex]].text}</p>
                           </div>
                        ))}
                    </div>
                  </details>
                </div>
              ))
            ) : (
              <p className="text-center text-white/70 py-8">لم يقم أي طالب بإكمال هذا الاختبار بعد.</p>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminExamsResults;