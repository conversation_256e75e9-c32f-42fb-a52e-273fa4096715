#!/usr/bin/env node

// Database Setup Script
// سكريبت إعداد قاعدة البيانات

import { createTables, testConnection, closeConnection } from '../src/lib/database.js';

async function setupDatabase() {
  console.log('🚀 بدء إعداد قاعدة البيانات...');
  console.log('=====================================');
  
  try {
    // اختبار الاتصال أولاً
    console.log('🔍 اختبار الاتصال بقاعدة البيانات...');
    const connectionTest = await testConnection();
    
    if (!connectionTest) {
      console.error('❌ فشل في الاتصال بقاعدة البيانات');
      process.exit(1);
    }
    
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    console.log('');
    
    // إنشاء الجداول
    console.log('📋 إنشاء الجداول والبيانات الأولية...');
    await createTables();
    
    console.log('');
    console.log('🎉 تم إعداد قاعدة البيانات بنجاح!');
    console.log('=====================================');
    console.log('');
    console.log('📊 الجداول المُنشأة:');
    console.log('  ✅ users - جدول المستخدمين');
    console.log('  ✅ user_devices - جدول أجهزة المستخدمين');
    console.log('  ✅ login_attempts - جدول محاولات تسجيل الدخول');
    console.log('  ✅ system_settings - جدول إعدادات النظام');
    console.log('');
    console.log('👥 المستخدمين الافتراضيين:');
    console.log('  🔑 <EMAIL> (كلمة المرور: Gulf2025)');
    console.log('  👨‍🏫 <EMAIL> (كلمة المرور: password)');
    console.log('  👩‍🏫 <EMAIL> (كلمة المرور: password)');
    console.log('  🎓 <EMAIL> (كلمة المرور: password)');
    console.log('  🎓 <EMAIL> (كلمة المرور: password)');
    console.log('');
    console.log('🔧 يمكنك الآن تشغيل المشروع باستخدام: npm run dev');
    
  } catch (error) {
    console.error('❌ خطأ في إعداد قاعدة البيانات:', error.message);
    console.error('');
    console.error('💡 تأكد من:');
    console.error('  - صحة بيانات الاتصال في ملف .env');
    console.error('  - أن قاعدة البيانات متاحة ويمكن الوصول إليها');
    console.error('  - أن المستخدم لديه صلاحيات إنشاء الجداول');
    process.exit(1);
  } finally {
    // إغلاق الاتصال
    await closeConnection();
  }
}

// تشغيل السكريبت
setupDatabase();
