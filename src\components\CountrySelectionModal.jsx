import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useCountry } from '@/contexts/CountryContext';

const countries = [
  { code: 'SA', name: 'المملكة العربية السعودية', flag: '🇸🇦', currency: 'SAR' },
  { code: 'QA', name: 'قطر', flag: '🇶🇦', currency: 'QAR' },
  { code: 'KW', name: 'الكويت', flag: '🇰🇼', currency: 'KWD' },
  { code: 'OM', name: 'عُمان', flag: '🇴🇲', currency: 'OMR' },
];

const CountrySelectionModal = ({ isOpen, onOpenChange }) => {
  const { selectCountry } = useCountry();

  const handleSelect = (country) => {
    selectCountry(country);
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center text-2xl">أهلاً بك في أكاديمية الخليج</DialogTitle>
          <DialogDescription className="text-center">
            الرجاء اختيار دولتك للاستمتاع بتجربة مخصصة.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {countries.map((country) => (
            <Button
              key={country.code}
              variant="outline"
              size="lg"
              className="w-full justify-start text-lg"
              onClick={() => handleSelect(country)}
            >
              <span className="text-2xl ml-4">{country.flag}</span>
              {country.name}
            </Button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CountrySelectionModal;