// MySQL Database Configuration and Connection
// تكوين قاعدة بيانات MySQL والاتصال

// ملاحظة: هذا الملف يحتوي على إعدادات MySQL للاستخدام المستقبلي
// حالياً يتم استخدام localStorage كقاعدة بيانات مؤقتة

const MYSQL_CONFIG = {
  host: process.env.MYSQL_HOST || 'localhost',
  port: process.env.MYSQL_PORT || 3306,
  user: process.env.MYSQL_USER || 'gulf_academy',
  password: process.env.MYSQL_PASSWORD || '',
  database: process.env.MYSQL_DATABASE || 'gulf_academy_db',
  charset: 'utf8mb4',
  timezone: '+03:00' // توقيت الخليج العربي
};

// SQL Scripts لإنشاء الجداول المطلوبة
export const CREATE_TABLES_SQL = `
-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  gender ENUM('male', 'female'),
  birth_date DATE,
  user_type ENUM('admin', 'teacher', 'student') DEFAULT 'student',
  status ENUM('نشط', 'غير نشط', 'محظور') DEFAULT 'نشط',
  device_registered BOOLEAN DEFAULT FALSE,
  device_id VARCHAR(32),
  registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_user_type (user_type),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بيانات الأجهزة
CREATE TABLE IF NOT EXISTS user_devices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_email VARCHAR(255) NOT NULL,
  device_id VARCHAR(32) NOT NULL,
  device_fingerprint VARCHAR(64) NOT NULL,
  browser_name VARCHAR(50),
  browser_version VARCHAR(20),
  browser_user_agent TEXT,
  system_platform VARCHAR(100),
  system_language VARCHAR(10),
  system_languages TEXT,
  system_timezone VARCHAR(50),
  system_timezone_offset INT,
  screen_width INT,
  screen_height INT,
  screen_color_depth INT,
  screen_pixel_ratio DECIMAL(3,2),
  hardware_cores INT,
  hardware_memory INT,
  hardware_max_touch_points INT,
  registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (user_email) REFERENCES users(email) ON DELETE CASCADE,
  UNIQUE KEY unique_user_device (user_email, device_id),
  INDEX idx_user_email (user_email),
  INDEX idx_device_id (device_id),
  INDEX idx_fingerprint (device_fingerprint),
  INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS login_attempts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_email VARCHAR(255),
  device_fingerprint VARCHAR(64),
  attempt_type ENUM('success', 'failed_password', 'failed_device', 'blocked') NOT NULL,
  ip_address VARCHAR(45), -- للاستخدام المستقبلي إذا لزم الأمر
  user_agent TEXT,
  attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  success BOOLEAN DEFAULT FALSE,
  failure_reason TEXT,
  INDEX idx_user_email (user_email),
  INDEX idx_attempt_time (attempt_time),
  INDEX idx_attempt_type (attempt_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('device_security_enabled', 'true', 'boolean', 'تفعيل نظام أمان الأجهزة'),
('max_devices_per_user', '1', 'number', 'الحد الأقصى للأجهزة المسموحة لكل مستخدم'),
('device_verification_required', 'true', 'boolean', 'إجبارية التحقق من الجهاز عند تسجيل الدخول'),
('failed_login_attempts_limit', '5', 'number', 'عدد محاولات تسجيل الدخول الفاشلة المسموحة'),
('device_session_timeout', '24', 'number', 'مدة انتهاء جلسة الجهاز بالساعات');
`;

// دوال للتعامل مع قاعدة البيانات (للاستخدام المستقبلي)
export class MySQLDeviceController {
  constructor(connection) {
    this.db = connection;
  }

  // حفظ بيانات الجهاز
  async saveDeviceInfo(userEmail, deviceInfo) {
    const query = `
      INSERT INTO user_devices (
        user_email, device_id, device_fingerprint, browser_name, browser_version,
        browser_user_agent, system_platform, system_language, system_languages,
        system_timezone, system_timezone_offset, screen_width, screen_height,
        screen_color_depth, screen_pixel_ratio, hardware_cores, hardware_memory,
        hardware_max_touch_points
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        device_fingerprint = VALUES(device_fingerprint),
        browser_name = VALUES(browser_name),
        browser_version = VALUES(browser_version),
        browser_user_agent = VALUES(browser_user_agent),
        last_seen = CURRENT_TIMESTAMP
    `;

    const values = [
      userEmail,
      deviceInfo.deviceId,
      deviceInfo.fingerprint,
      deviceInfo.browser?.name,
      deviceInfo.browser?.version,
      deviceInfo.browser?.userAgent,
      deviceInfo.system?.platform,
      deviceInfo.system?.language,
      deviceInfo.system?.languages,
      deviceInfo.system?.timezone,
      deviceInfo.system?.timezoneOffset,
      deviceInfo.screen?.width,
      deviceInfo.screen?.height,
      deviceInfo.screen?.colorDepth,
      deviceInfo.screen?.pixelRatio,
      deviceInfo.hardware?.cores,
      deviceInfo.hardware?.memory,
      deviceInfo.hardware?.maxTouchPoints
    ];

    return await this.db.execute(query, values);
  }

  // التحقق من الجهاز
  async verifyDevice(userEmail, deviceFingerprint) {
    const query = `
      SELECT * FROM user_devices 
      WHERE user_email = ? AND device_fingerprint = ? AND is_active = TRUE
    `;
    const [rows] = await this.db.execute(query, [userEmail, deviceFingerprint]);
    return rows.length > 0;
  }

  // جلب أجهزة المستخدم
  async getUserDevices(userEmail) {
    const query = `
      SELECT * FROM user_devices 
      WHERE user_email = ? AND is_active = TRUE
      ORDER BY last_seen DESC
    `;
    const [rows] = await this.db.execute(query, [userEmail]);
    return rows;
  }

  // جلب جميع الأجهزة (للمدير)
  async getAllDevices() {
    const query = `
      SELECT ud.*, u.name as user_name 
      FROM user_devices ud
      JOIN users u ON ud.user_email = u.email
      WHERE ud.is_active = TRUE
      ORDER BY ud.last_seen DESC
    `;
    const [rows] = await this.db.execute(query);
    return rows;
  }

  // تسجيل محاولة تسجيل دخول
  async logLoginAttempt(userEmail, deviceFingerprint, attemptType, success, failureReason = null) {
    const query = `
      INSERT INTO login_attempts (
        user_email, device_fingerprint, attempt_type, user_agent, 
        success, failure_reason
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      userEmail,
      deviceFingerprint,
      attemptType,
      navigator.userAgent,
      success,
      failureReason
    ];

    return await this.db.execute(query, values);
  }

  // حذف جهاز
  async removeDevice(userEmail, deviceId) {
    const query = `
      UPDATE user_devices 
      SET is_active = FALSE 
      WHERE user_email = ? AND device_id = ?
    `;
    return await this.db.execute(query, [userEmail, deviceId]);
  }

  // تحديث آخر ظهور للجهاز
  async updateDeviceLastSeen(userEmail, deviceFingerprint) {
    const query = `
      UPDATE user_devices 
      SET last_seen = CURRENT_TIMESTAMP 
      WHERE user_email = ? AND device_fingerprint = ?
    `;
    return await this.db.execute(query, [userEmail, deviceFingerprint]);
  }
}

// دالة للحصول على إعدادات النظام
export async function getSystemSetting(db, settingKey, defaultValue = null) {
  try {
    const query = 'SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?';
    const [rows] = await db.execute(query, [settingKey]);
    
    if (rows.length === 0) {
      return defaultValue;
    }

    const { setting_value, setting_type } = rows[0];
    
    switch (setting_type) {
      case 'boolean':
        return setting_value === 'true';
      case 'number':
        return parseFloat(setting_value);
      case 'json':
        return JSON.parse(setting_value);
      default:
        return setting_value;
    }
  } catch (error) {
    console.error('خطأ في جلب إعدادات النظام:', error);
    return defaultValue;
  }
}

// دالة لتحديث إعدادات النظام
export async function updateSystemSetting(db, settingKey, settingValue, settingType = 'string', updatedBy = null) {
  try {
    const query = `
      INSERT INTO system_settings (setting_key, setting_value, setting_type, updated_by)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value),
        setting_type = VALUES(setting_type),
        updated_by = VALUES(updated_by),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const valueToStore = settingType === 'json' ? JSON.stringify(settingValue) : String(settingValue);
    return await db.execute(query, [settingKey, valueToStore, settingType, updatedBy]);
  } catch (error) {
    console.error('خطأ في تحديث إعدادات النظام:', error);
    throw error;
  }
}

export default MYSQL_CONFIG;
