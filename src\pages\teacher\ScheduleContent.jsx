import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Calendar as CalendarIcon, Save, Clock } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

const daysOfWeek = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

const generateTimeSlots = () => {
    const slots = [];
    for (let i = 6; i <= 23; i++) {
        slots.push(`${String(i).padStart(2, '0')}:00`);
        slots.push(`${String(i).padStart(2, '0')}:30`);
    }
    return slots;
};
const timeSlots = generateTimeSlots();

const DaySchedule = ({ day, schedule, onScheduleChange }) => {
    const daySchedule = schedule[day] || { startTime: '', endTime: '' };

    return (
        <div className="flex-1 min-w-[200px] glass-effect p-4 rounded-lg space-y-3">
            <h4 className="text-center font-bold text-white border-b border-white/20 pb-2">{day}</h4>
            <div className="space-y-2">
                <div>
                    <Label htmlFor={`${day}-start`} className="text-sm text-white/70">من الساعة</Label>
                    <Select value={daySchedule.startTime} onValueChange={val => onScheduleChange(day, 'startTime', val)}>
                        <SelectTrigger id={`${day}-start`} className="bg-white/10"><SelectValue placeholder="غير متاح" /></SelectTrigger>
                        <SelectContent className="bg-secondary text-white max-h-60">{timeSlots.map(t => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
                    </Select>
                </div>
                <div>
                    <Label htmlFor={`${day}-end`} className="text-sm text-white/70">إلى الساعة</Label>
                    <Select value={daySchedule.endTime} onValueChange={val => onScheduleChange(day, 'endTime', val)} disabled={!daySchedule.startTime}>
                        <SelectTrigger id={`${day}-end`} className="bg-white/10"><SelectValue placeholder="غير متاح" /></SelectTrigger>
                        <SelectContent className="bg-secondary text-white max-h-60">{timeSlots.map(t => <SelectItem key={t} value={t} disabled={t <= daySchedule.startTime}>{t}</SelectItem>)}</SelectContent>
                    </Select>
                </div>
            </div>
        </div>
    );
};

const ScheduleContent = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const { availability, updateTeacherAvailability } = useBooking();
  
  const [localAvailability, setLocalAvailability] = useState({});

  useEffect(() => {
    setLocalAvailability(availability[user.email] || {});
  }, [availability, user.email]);

  const handleScheduleChange = (day, field, value) => {
    setLocalAvailability(prev => {
        const newDaySchedule = { ...prev[day], [field]: value };
        // If start time is cleared, clear end time as well
        if (field === 'startTime' && !value) {
            newDaySchedule.endTime = '';
        }
        // If new start time is after end time, clear end time
        if (field === 'startTime' && value && newDaySchedule.endTime && value >= newDaySchedule.endTime) {
            newDaySchedule.endTime = '';
        }

        return {
            ...prev,
            [day]: newDaySchedule
        };
    });
  };

  const handleSave = () => {
    updateTeacherAvailability(user.email, localAvailability);
    toast({
      title: "✅ تم حفظ التغييرات",
      description: "تم تحديث جدول أوقاتك المتاحة بنجاح.",
    });
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white flex items-center gap-3"><CalendarIcon className="h-6 w-6" />حدد جدول عملك</h3>
          <Button onClick={handleSave} className="bg-primary hover:bg-primary/90">
            <Save className="h-4 w-4 ml-2" />
            حفظ التغييرات
          </Button>
        </div>
        
        <p className="text-white/70 mb-6">حدد أوقات عملك لكل يوم من أيام الأسبوع. ستكون هذه الأوقات متاحة للطلاب للحجز بفاصل زمني نصف ساعة.</p>

        <div className="flex gap-4 overflow-x-auto pb-4">
            {daysOfWeek.map(day => (
                <DaySchedule key={day} day={day} schedule={localAvailability} onScheduleChange={handleScheduleChange} />
            ))}
        </div>
      </div>
    </div>
  );
};

export default ScheduleContent;