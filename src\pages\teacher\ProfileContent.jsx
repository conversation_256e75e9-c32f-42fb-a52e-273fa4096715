import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { User, Mail, Phone, Lock, Eye, EyeOff, BookOpen, DollarSign, X } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

const ProfileContent = () => {
  const { user, updateUser } = useAuth();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: user.name || '',
    email: user.email || '',
    phone: user.phone || '',
    bio: user.bio || '',
    subjects: user.subjects || [],
    pricePerHour: user.pricePerHour || '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [subjectInput, setSubjectInput] = useState('');

  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleAddSubject = (e) => {
    if (e.key === 'Enter' && subjectInput.trim() !== '') {
      e.preventDefault();
      if (!formData.subjects.includes(subjectInput.trim())) {
        setFormData(prev => ({ ...prev, subjects: [...prev.subjects, subjectInput.trim()] }));
      }
      setSubjectInput('');
    }
  };

  const handleRemoveSubject = (subjectToRemove) => {
    setFormData(prev => ({ ...prev, subjects: prev.subjects.filter(s => s !== subjectToRemove) }));
  };

  const handleUpdateProfile = (e) => {
    e.preventDefault();
    const { name, phone, bio, subjects, pricePerHour } = formData;
    updateUser(user.id, { name, phone, bio, subjects, pricePerHour: parseFloat(pricePerHour) || 0 });
    toast({ title: "✅ تم تحديث الملف الشخصي بنجاح!" });
  };

  const handleUpdatePassword = (e) => {
    e.preventDefault();
    const { currentPassword, newPassword, confirmPassword } = formData;
    if (newPassword !== confirmPassword) {
      toast({ title: "❌ خطأ", description: "كلمة المرور الجديدة غير متطابقة.", variant: "destructive" });
      return;
    }
    if (newPassword.length < 6) {
      toast({ title: "❌ خطأ", description: "يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل.", variant: "destructive" });
      return;
    }
    console.log("Updating password (simulation)...", { currentPassword, newPassword });
    toast({ title: "✅ تم تحديث كلمة المرور بنجاح!" });
    setFormData(prev => ({ ...prev, currentPassword: '', newPassword: '', confirmPassword: '' }));
  };

  return (
    <div className="space-y-8">
      <Card className="glass-effect border-white/10 text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><User /> الملف الشخصي</CardTitle>
          <CardDescription>تحديث معلوماتك الشخصية والمهنية.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdateProfile} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">الاسم الكامل</Label>
                <Input id="name" value={formData.name} onChange={handleInputChange} className="bg-white/10 border-white/20" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input id="email" type="email" value={formData.email} disabled className="bg-white/20 border-white/30" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">رقم الهاتف (مع رمز الدولة)</Label>
                <Input id="phone" type="tel" value={formData.phone} onChange={handleInputChange} placeholder="+97412345678" className="bg-white/10 border-white/20" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="pricePerHour">السعر/ساعة (بالريال)</Label>
                <Input id="pricePerHour" type="number" value={formData.pricePerHour} onChange={handleInputChange} className="bg-white/10 border-white/20" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="bio">نبذة تعريفية</Label>
              <Textarea id="bio" value={formData.bio} onChange={handleInputChange} className="bg-white/10 border-white/20" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subjects">المواد التي تدرسها</Label>
              <Input id="subjects" value={subjectInput} onChange={(e) => setSubjectInput(e.target.value)} onKeyDown={handleAddSubject} placeholder="اكتب المادة واضغط Enter" className="bg-white/10 border-white/20" />
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.subjects.map(subject => (
                  <Badge key={subject} variant="secondary" className="flex items-center gap-2">
                    {subject}
                    <button type="button" onClick={() => handleRemoveSubject(subject)} className="text-red-400 hover:text-red-600">
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
            <Button type="submit" className="w-full bg-primary hover:bg-primary/90">حفظ التغييرات</Button>
          </form>
        </CardContent>
      </Card>

      <Card className="glass-effect border-white/10 text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2"><Lock /> تغيير كلمة المرور</CardTitle>
          <CardDescription>اختر كلمة مرور قوية للحفاظ على أمان حسابك.</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpdatePassword} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">كلمة المرور الحالية</Label>
              <div className="relative">
                <Input id="currentPassword" type={showCurrentPassword ? 'text' : 'password'} value={formData.currentPassword} onChange={handleInputChange} className="pr-10 bg-white/10 border-white/20" />
                <button type="button" onClick={() => setShowCurrentPassword(!showCurrentPassword)} className="absolute right-3 top-1/2 -translate-y-1/2">
                  {showCurrentPassword ? <EyeOff className="h-5 w-5 text-white/50" /> : <Eye className="h-5 w-5 text-white/50" />}
                </button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="newPassword">كلمة المرور الجديدة</Label>
              <div className="relative">
                <Input id="newPassword" type={showNewPassword ? 'text' : 'password'} value={formData.newPassword} onChange={handleInputChange} className="pr-10 bg-white/10 border-white/20" />
                <button type="button" onClick={() => setShowNewPassword(!showNewPassword)} className="absolute right-3 top-1/2 -translate-y-1/2">
                  {showNewPassword ? <EyeOff className="h-5 w-5 text-white/50" /> : <Eye className="h-5 w-5 text-white/50" />}
                </button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">تأكيد كلمة المرور الجديدة</Label>
              <Input id="confirmPassword" type={showNewPassword ? 'text' : 'password'} value={formData.confirmPassword} onChange={handleInputChange} className="bg-white/10 border-white/20" />
            </div>
            <Button type="submit" className="w-full bg-primary hover:bg-primary/90">تحديث كلمة المرور</Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileContent;