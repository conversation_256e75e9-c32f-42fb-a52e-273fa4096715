import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Book, Hash, Link as LinkIcon, AlertTriangle, UserX, Trash2, Users } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';

const BookingDetailsModal = ({ booking, isOpen, onClose }) => {
  if (!booking) return null;

  const statusMap = {
    pending: { label: 'قيد المراجعة', variant: 'secondary' },
    approved: { label: 'تمت الموافقة', variant: 'success' },
    rejected: { label: 'تم الرفض', variant: 'destructive' },
    completed: { label: 'مكتمل', variant: 'default' },
  };
  
  const getRequestInfo = (req, icon, title) => {
    if (req?.status === 'pending') {
      return (
        <div className="p-3 bg-white/5 rounded-lg">
          <h4 className="font-bold text-sm text-primary flex items-center gap-2">{icon}{title}</h4>
          <p className="text-xs mt-1 text-white/80">السبب: {req.reason}</p>
        </div>
      );
    }
    return null;
  };

  const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-secondary border-border text-white max-w-2xl">
        <DialogHeader>
          <DialogTitle>تفاصيل الحجز</DialogTitle>
          <DialogDescription>
            عرض جميع تفاصيل الحجز.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 space-y-4 max-h-[60vh] overflow-y-auto pr-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md col-span-full"><Users className="h-4 w-4 text-primary"/><span>الطلاب:</span><span className="font-semibold text-white">{studentNames}</span></div>
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md"><User className="h-4 w-4 text-primary"/><span>المعلم:</span><span className="font-semibold text-white">{booking.teacherName || 'لم يحدد'}</span></div>
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md"><Book className="h-4 w-4 text-primary"/><span>المواد:</span><span className="font-semibold text-white">{booking.subjects.map(s => s.subject).join(', ')}</span></div>
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md"><Hash className="h-4 w-4 text-primary"/><span>الباقة:</span><span className="font-semibold text-white">{booking.totalHours} ساعات</span></div>
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md"><Calendar className="h-4 w-4 text-primary"/><span>تاريخ الطلب:</span><span className="font-semibold text-white">{format(parseISO(booking.createdAt), 'd MMMM yyyy', { locale: arSA })}</span></div>
                <div className="flex items-center gap-2 p-2 bg-white/5 rounded-md"><Clock className="h-4 w-4 text-primary"/><span>الحالة:</span><Badge variant={statusMap[booking.status]?.variant || 'default'}>{statusMap[booking.status]?.label}</Badge></div>
                 <div className="col-span-full flex items-center gap-2 p-2 bg-white/5 rounded-md">
                    <LinkIcon className="h-4 w-4 text-primary"/>
                    <span>رابط Zoom:</span>
                    <a href={booking.zoomLink} target="_blank" rel="noopener noreferrer" className="font-semibold text-blue-400 hover:underline truncate">{booking.zoomLink || 'لم يتم إنشاؤه بعد'}</a>
                </div>
                {booking.studyMaterialUrl && (
                    <div className="col-span-full flex items-center gap-2 p-2 bg-white/5 rounded-md">
                        <LinkIcon className="h-4 w-4 text-primary"/>
                        <span>المادة الدراسية:</span>
                        <a href={booking.studyMaterialUrl} target="_blank" rel="noopener noreferrer" className="font-semibold text-blue-400 hover:underline truncate">عرض الملف المرفق</a>
                    </div>
                )}
            </div>

            <h4 className="font-bold text-white pt-4 border-t border-white/10">الأوقات المفضلة</h4>
            <div className="space-y-2">
                {booking.schedule?.map((item, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-white/5 rounded-md text-sm">
                        <Calendar className="h-4 w-4 text-primary"/>
                        <span className="font-semibold text-white">{format(parseISO(item.date), 'EEEE, d MMMM yyyy', { locale: arSA })}</span>
                        <span>- الساعة</span>
                        <span className="font-semibold text-white">{format(parseISO(`${item.date}T${item.time}`), 'p', { locale: arSA })}</span>
                    </div>
                ))}
            </div>

            <h4 className="font-bold text-white pt-4 border-t border-white/10">الطلبات المعلقة</h4>
            <div className="space-y-2">
                {getRequestInfo(booking.linkProblemRequest, <AlertTriangle className="h-4 w-4"/>, "طلب رابط جديد")}
                {getRequestInfo(booking.postponementRequest, <Clock className="h-4 w-4"/>, "طلب تأجيل")}
                {getRequestInfo(booking.teacherChangeRequest, <UserX className="h-4 w-4"/>, "طلب تغيير معلم")}
                {getRequestInfo(booking.deletionRequest, <Trash2 className="h-4 w-4"/>, "طلب إلغاء اشتراك")}
                {!booking.linkProblemRequest?.status && !booking.postponementRequest?.status && !booking.teacherChangeRequest?.status && !booking.deletionRequest?.status &&
                    <p className="text-sm text-white/70 text-center py-4">لا توجد طلبات معلقة حالياً.</p>
                }
            </div>
        </div>
        <DialogFooter>
          <DialogClose asChild><Button variant="outline">إغلاق</Button></DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BookingDetailsModal;