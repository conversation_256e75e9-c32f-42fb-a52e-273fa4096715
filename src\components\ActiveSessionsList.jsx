import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Button } from '@/components/ui/button';
import { Link as LinkIcon, VideoOff, AlertTriangle } from 'lucide-react';
import { parseISO, isAfter, isBefore, addHours } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';

const ActiveSessionsList = () => {
    const { user } = useAuth();
    const { bookings, trackSessionJoin, trackSessionLeave, requestNewLink } = useBooking();
    const { toast } = useToast();
    const [currentTime, setCurrentTime] = useState(new Date());

    useEffect(() => {
        const timer = setInterval(() => setCurrentTime(new Date()), 60000); // Update every minute
        return () => clearInterval(timer);
    }, []);

    const handleJoin = (booking, session) => {
        if (booking.zoomLink && session) {
            trackSessionJoin(booking.id, session.sessionId, user.userType);
            const newWindow = window.open(booking.zoomLink, '_blank', 'noopener,noreferrer');
            
            const checkWindowClosed = setInterval(() => {
                if (!newWindow || newWindow.closed) {
                    trackSessionLeave(booking.id, session.sessionId, user.userType);
                    clearInterval(checkWindowClosed);
                }
            }, 1000);

        } else {
            toast({ title: "خطأ", description: "رابط الحصة غير متوفر.", variant: "destructive" });
        }
    };
    
    const handleReportProblem = (bookingId) => {
        const reason = user.userType === 'student' ? "أبلغ الطالب عن مشكلة في الرابط." : "أبلغ المعلم عن مشكلة في الرابط.";
        requestNewLink(bookingId, reason);
        toast({ title: "✅ تم إرسال البلاغ للإدارة.", description: "سيتم تزويدكم برابط جديد قريباً." });
    };

    const userBookings = user.userType === 'student' 
        ? bookings.filter(b => b.studentEmail === user.email && b.status === 'approved')
        : bookings.filter(b => b.teacherEmail === user.email && b.status === 'approved');

    const activeSessions = userBookings.flatMap(booking => 
        (booking.sessions || []).map(session => ({session, booking}))
    ).filter(item => {
        try {
            const sessionStartTime = parseISO(`${item.session.date}T${item.session.time}`);
            const sessionEndTime = addHours(sessionStartTime, 1); // Assuming 1-hour sessions for "active" window
            return isAfter(currentTime, sessionStartTime) && isBefore(currentTime, sessionEndTime) && item.session.status === 'scheduled';
        } catch(e) {
            return false;
        }
    });

    if (activeSessions.length === 0) {
        return (
            <div className="text-center py-6 text-white/70">
                <VideoOff className="mx-auto h-10 w-10 mb-2" />
                <p>لا توجد حصص جارية حالياً.</p>
            </div>
        );
    }
    
    return (
        <div className="space-y-3">
            {activeSessions.map(({ session, booking }) => (
                 <div key={session.sessionId} className="bg-secondary/50 rounded-lg p-3 flex items-center justify-between gap-2 flex-wrap">
                     <div className="flex-1 min-w-[200px]">
                         <p className="font-medium text-white">
                             {user.userType === 'student' ? `حصة ${booking.subjects.map(s=>s.subject).join(', ')} مع ${booking.teacherName}` : `حصة مع ${booking.studentName}`}
                         </p>
                         <p className="text-sm text-white/70">
                             مجدولة في الساعة {session.time}
                         </p>
                     </div>
                     <div className="flex items-center gap-2">
                         <Button 
                            variant="ghost" 
                            className="bg-green-600 hover:bg-green-700 text-white" 
                            onClick={() => handleJoin(booking, session)}>
                             <LinkIcon className="h-4 w-4 ml-2" />
                             انضم الآن
                         </Button>
                         <Button
                            variant="outline"
                            size="sm"
                            className="border-orange-500 text-orange-400 hover:bg-orange-500/10 hover:text-orange-300"
                            onClick={() => handleReportProblem(booking.id)}
                         >
                            <AlertTriangle className="h-4 w-4 ml-2" />
                            إبلاغ عن مشكلة
                         </Button>
                     </div>
                 </div>
            ))}
        </div>
    );
};

export default ActiveSessionsList;