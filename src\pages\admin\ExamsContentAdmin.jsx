import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Trash2, Edit, BarChart2, ImagePlus, X } from 'lucide-react';
import { useExam } from '@/contexts/ExamContext';
import { useAuth } from '@/contexts/AuthContext';
import { useGoogleLogin } from '@react-oauth/google';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Checkbox } from '@/components/ui/checkbox';
import { useNavigate } from 'react-router-dom';

const ExamsContentAdmin = () => {
  const { toast } = useToast();
  const { user, students, teachers } = useAuth();
  const { exams, createExam, updateExam, deleteExam } = useExam();
  const navigate = useNavigate();

  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [examToAssign, setExamToAssign] = useState(null);
  const [selectedStudents, setSelectedStudents] = useState([]);
  
  const [examTitle, setExamTitle] = useState('');
  const [questions, setQuestions] = useState([{
    questionText: '', 
    options: [ { text: '', image: null }, { text: '', image: null }, { text: '', image: null }, { text: '', image: null }], 
    correctAnswerIndex: null, 
    points: 1, 
    image: null 
  }]);
  
  const [tokenClient, setTokenClient] = useState(null);
  const fileInputRef = useRef(null);
  const [uploadingTarget, setUploadingTarget] = useState(null);

  const login = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      setTokenClient(tokenResponse);
      toast({ title: '✅ تم الاتصال بـ Google Drive' });
      if (fileInputRef.current) fileInputRef.current.click();
    },
    onError: () => toast({ title: '❌ خطأ في الاتصال', variant: 'destructive' }),
    scope: 'https://www.googleapis.com/auth/drive.file',
    flow: 'implicit',
  });

  const resetForm = () => {
    setExamTitle('');
    setQuestions([{ questionText: '', options: [ { text: '', image: null }, { text: '', image: null }, { text: '', image: null }, { text: '', image: null }], correctAnswerIndex: null, points: 1, image: null }]);
  };

  const handleAddQuestion = () => {
    setQuestions([...questions, { questionText: '', options: [ { text: '', image: null }, { text: '', image: null }, { text: '', image: null }, { text: '', image: null }], correctAnswerIndex: null, points: 1, image: null }]);
  };

  const handleRemoveQuestion = (index) => {
    const newQuestions = questions.filter((_, i) => i !== index);
    setQuestions(newQuestions);
  };

  const handleQuestionChange = (index, field, value) => {
    const newQuestions = [...questions];
    newQuestions[index][field] = value;
    setQuestions(newQuestions);
  };

  const handleOptionChange = (qIndex, oIndex, field, value) => {
    const newQuestions = [...questions];
    newQuestions[qIndex].options[oIndex][field] = value;
    setQuestions(newQuestions);
  };
  
  const handleCorrectAnswerChange = (qIndex, oIndex) => {
      const newQuestions = [...questions];
      newQuestions[qIndex].correctAnswerIndex = oIndex;
      setQuestions(newQuestions);
  };

  const handleImageUploadClick = (target) => {
    setUploadingTarget(target);
    if (!tokenClient) login();
    else fileInputRef.current.click();
  };

  const handleFileChange = async (event) => {
    if (event.target.files.length === 0 || !uploadingTarget) return;
    const file = event.target.files[0];
    
    toast({ title: 'جاري رفع الصورة...' });
    const metadata = { name: file.name, mimeType: file.type };
    const form = new FormData();
    form.append("metadata", new Blob([JSON.stringify(metadata)], { type: "application/json" }));
    form.append("file", file);

    try {
      const response = await fetch("https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart", {
        method: "POST",
        headers: { Authorization: "Bearer " + tokenClient.access_token },
        body: form,
      });
      const result = await response.json();
      if (result.error) throw new Error(result.error.message);
      
      const fileId = result.id;
      await fetch(`https://www.googleapis.com/drive/v3/files/${fileId}/permissions`, {
        method: 'POST',
        headers: { 'Authorization': 'Bearer ' + tokenClient.access_token, 'Content-Type': 'application/json' },
        body: JSON.stringify({ 'role': 'reader', 'type': 'anyone' })
      });

      const imageUrl = `https://lh3.googleusercontent.com/d/${fileId}`;

      if (uploadingTarget.type === 'question') {
        handleQuestionChange(uploadingTarget.qIndex, 'image', imageUrl);
      } else if (uploadingTarget.type === 'option') {
        handleOptionChange(uploadingTarget.qIndex, uploadingTarget.oIndex, 'image', imageUrl);
      }
      toast({ title: '✅ تم إرفاق الصورة بنجاح' });
    } catch (error) {
      toast({ title: '❌ فشل الرفع', description: `حدث خطأ: ${error.message}`, variant: 'destructive' });
    } finally {
      setUploadingTarget(null);
      fileInputRef.current.value = "";
    }
  };

  const handleSaveExam = () => {
    if (!examTitle || questions.some(q => !q.questionText || q.options.some(o => !o.text) || q.correctAnswerIndex === null)) {
      toast({ title: "بيانات غير مكتملة", description: "يرجى ملء جميع الحقول وتحديد إجابة صحيحة لكل سؤال.", variant: "destructive" });
      return;
    }
    createExam({ title: examTitle, questions, teacherId: user.email });
    toast({ title: '✅ تم إنشاء الاختبار بنجاح' });
    resetForm();
    setIsCreateModalOpen(false);
  };

  const openAssignModal = (exam) => {
    setExamToAssign(exam);
    setSelectedStudents(exam.assignedStudents || []);
    setIsAssignModalOpen(true);
  };

  const handleAssignExam = () => {
    if (!examToAssign || selectedStudents.length === 0) {
      toast({ title: "لم يتم تحديد طلاب", variant: "destructive" });
      return;
    }
    const updatedExam = { ...examToAssign, assignedStudents: selectedStudents };
    updateExam(updatedExam);
    toast({ title: `✅ تم تخصيص الاختبار لـ ${selectedStudents.length} طالب` });
    setIsAssignModalOpen(false);
    setExamToAssign(null);
    setSelectedStudents([]);
  };

  const handleStudentSelection = (studentEmail) => {
    setSelectedStudents(
      selectedStudents.includes(studentEmail)
        ? selectedStudents.filter(s => s !== studentEmail)
        : [...selectedStudents, studentEmail]
    );
  };
  
  const getTeacherName = (teacherId) => {
      const teacher = teachers.find(t => t.email === teacherId);
      return teacher ? teacher.name : 'غير معروف';
  };

  return (
    <div className="space-y-8">
      <input type="file" accept="image/*" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">إدارة الاختبارات</h3>
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => setIsCreateModalOpen(true)}>
            <Plus className="h-4 w-4 ml-2" /> إنشاء اختبار جديد
          </Button>
        </div>
        <div className="space-y-4">
          {exams.map(exam => (
            <div key={exam.id} className="bg-secondary/50 rounded-lg p-4 flex items-center justify-between">
              <div>
                <p className="font-bold text-white">{exam.title}</p>
                <p className="text-muted-foreground text-sm">أنشأه: {getTeacherName(exam.teacherId)} - {exam.questions.length} أسئلة - مخصص لـ {exam.assignedStudents?.length || 0} طالب</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="ghost" className="text-white/70 hover:text-white" onClick={() => navigate(`/admin/exam-results/${exam.id}`)}><BarChart2 className="h-4 w-4" /></Button>
                <Button size="sm" variant="ghost" className="text-white/70 hover:text-white"><Edit className="h-4 w-4" /></Button>
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => openAssignModal(exam)}>تخصيص</Button>
                <Button size="sm" variant="destructive" onClick={() => deleteExam(exam.id)}><Trash2 className="h-4 w-4" /></Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-4xl">
          <DialogHeader>
            <DialogTitle>إنشاء اختبار جديد</DialogTitle>
            <DialogDescription>أضف الأسئلة والخيارات وحدد الإجابة الصحيحة.</DialogDescription>
          </DialogHeader>
          <div className="max-h-[70vh] overflow-y-auto p-4 space-y-6">
            <div>
              <Label htmlFor="examTitle" className="text-white mb-2 block">عنوان الاختبار</Label>
              <Input id="examTitle" value={examTitle} onChange={(e) => setExamTitle(e.target.value)} placeholder="مثال: اختبار منتصف الفصل في الجبر" className="bg-white/10 border-border text-white" />
            </div>
            {questions.map((q, qIndex) => (
              <div key={qIndex} className="bg-white/5 rounded-lg p-6 space-y-4 relative">
                <div className="flex items-center justify-between">
                  <Label htmlFor={`q${qIndex}`} className="text-white font-medium">السؤال {qIndex + 1}</Label>
                  <Button variant="ghost" size="icon" className="text-red-400 hover:bg-red-500/20 absolute top-2 left-2" onClick={() => handleRemoveQuestion(qIndex)}><Trash2 className="h-4 w-4" /></Button>
                </div>
                <Input id={`q${qIndex}`} value={q.questionText} onChange={(e) => handleQuestionChange(qIndex, 'questionText', e.target.value)} placeholder="نص السؤال" className="bg-white/10 border-border text-white" />
                
                {q.image ? (
                  <div className="relative w-48 h-32">
                    <img src={q.image} alt="صورة السؤال" className="rounded-md w-full h-full object-cover" />
                    <Button size="icon" variant="destructive" className="absolute -top-2 -right-2 h-6 w-6 rounded-full" onClick={() => handleQuestionChange(qIndex, 'image', null)}><X className="h-4 w-4" /></Button>
                  </div>
                ) : (
                  <Button variant="outline" size="sm" className="border-border text-white hover:bg-white/10" onClick={() => handleImageUploadClick({type: 'question', qIndex})}>
                    <ImagePlus className="h-4 w-4 ml-2" /> إرفاق صورة للسؤال
                  </Button>
                )}

                <div className="space-y-4">
                  {q.options.map((opt, oIndex) => (
                    <div key={oIndex} className="flex items-center gap-3 bg-white/5 p-3 rounded-md">
                      <Checkbox id={`q${qIndex}o${oIndex}`} checked={q.correctAnswerIndex === oIndex} onCheckedChange={() => handleCorrectAnswerChange(qIndex, oIndex)} />
                      <div className="flex-1 space-y-2">
                        <Input value={opt.text} onChange={(e) => handleOptionChange(qIndex, oIndex, 'text', e.target.value)} placeholder={`نص الخيار ${oIndex + 1}`} className="bg-white/10 border-border text-white" />
                         {opt.image ? (
                           <div className="relative w-32 h-20">
                            <img src={opt.image} alt="صورة الخيار" className="rounded-md w-full h-full object-cover" />
                            <Button size="icon" variant="destructive" className="absolute -top-2 -right-2 h-5 w-5 rounded-full" onClick={() => handleOptionChange(qIndex, oIndex, 'image', null)}><X className="h-3 w-3" /></Button>
                           </div>
                         ) : (
                          <Button variant="outline" size="xs" className="border-border text-white/70 hover:bg-white/10 text-xs" onClick={() => handleImageUploadClick({type: 'option', qIndex, oIndex})}>
                            <ImagePlus className="h-3 w-3 ml-1" /> صورة للخيار
                          </Button>
                         )}
                      </div>
                    </div>
                  ))}
                </div>
                <div>
                  <Label htmlFor={`points${qIndex}`} className="text-white/80 text-sm mb-1 block">النقاط</Label>
                  <Input type="number" id={`points${qIndex}`} value={q.points} onChange={(e) => handleQuestionChange(qIndex, 'points', parseInt(e.target.value) || 1)} className="bg-white/10 border-border text-white w-24" />
                </div>
              </div>
            ))}
            <Button variant="outline" className="border-border text-white hover:bg-white/10" onClick={handleAddQuestion}><Plus className="h-4 w-4 mr-2" />إضافة سؤال</Button>
          </div>
          <DialogFooter>
            <Button onClick={handleSaveExam} className="bg-primary text-primary-foreground hover:bg-primary/90">حفظ الاختبار</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isAssignModalOpen} onOpenChange={setIsAssignModalOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>تخصيص اختبار "{examToAssign?.title}"</DialogTitle>
            <DialogDescription>اختر الطلاب الذين سيتم تخصيص هذا الاختبار لهم.</DialogDescription>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto p-4 space-y-2">
            {students.map(student => (
              <div key={student.email} className="flex items-center p-2 hover:bg-primary/20 rounded-md cursor-pointer" onClick={() => handleStudentSelection(student.email)}>
                <Checkbox checked={selectedStudents.includes(student.email)} readOnly className="form-checkbox h-4 w-4 text-primary bg-secondary border-border rounded focus:ring-primary ml-3" />
                <span>{student.name} ({student.email})</span>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button onClick={handleAssignExam} className="bg-primary text-primary-foreground hover:bg-primary/90">حفظ التخصيص</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ExamsContentAdmin;