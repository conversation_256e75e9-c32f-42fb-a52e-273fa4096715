import React, { createContext, useContext, useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from './AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { useNotifications } from './NotificationsContext';

const ChatContext = createContext();

export const useChat = () => useContext(ChatContext);

const LIVE_CHAT_DB_KEY = 'gulfAcademyLiveChat';
const INTERNAL_CHAT_DB_KEY = 'gulfAcademyInternalChat';
const ANNOUNCEMENTS_DB_KEY = 'gulfAcademyAnnouncements';
const SESSION_ID_KEY = 'gulfAcademyChatSessionId';
const CHAT_GROUPS_DB_KEY = 'gulfAcademyChatGroups';

export const ChatProvider = ({ children }) => {
  const { user, users, students, teachers } = useAuth();
  const { toast } = useToast();
  const { createNotification } = useNotifications();
  const [messages, setMessages] = useState({});
  const [internalMessages, setInternalMessages] = useState({});
  const [announcements, setAnnouncements] = useState([]);
  const [sessionId, setSessionId] = useState('');
  const [chatGroups, setChatGroups] = useState([]);

  useEffect(() => {
    let storedSessionId = localStorage.getItem(SESSION_ID_KEY);
    if (!storedSessionId) {
      storedSessionId = uuidv4();
      localStorage.setItem(SESSION_ID_KEY, storedSessionId);
    }
    setSessionId(storedSessionId);

    const storedMessages = JSON.parse(localStorage.getItem(LIVE_CHAT_DB_KEY) || '{}');
    setMessages(storedMessages);

    const storedInternalMessages = JSON.parse(localStorage.getItem(INTERNAL_CHAT_DB_KEY) || '{}');
    setInternalMessages(storedInternalMessages);

    const storedAnnouncements = JSON.parse(localStorage.getItem(ANNOUNCEMENTS_DB_KEY) || '[]');
    setAnnouncements(storedAnnouncements);
    
    const storedChatGroups = JSON.parse(localStorage.getItem(CHAT_GROUPS_DB_KEY) || '[]');
    setChatGroups(storedChatGroups);
  }, []);

  const getSessionId = () => {
    if (user) return user.email;
    return sessionId;
  };

  const sendMessage = (chatId, messagePayload) => {
    const newMessage = {
      id: uuidv4(),
      text: messagePayload.text,
      sender: messagePayload.sender,
      fileLink: messagePayload.fileLink || null,
      timestamp: new Date().toISOString(),
    };

    const updatedMessages = { ...messages };
    if (!updatedMessages[chatId]) {
      updatedMessages[chatId] = [];
    }
    updatedMessages[chatId].push(newMessage);

    setMessages(updatedMessages);
    localStorage.setItem(LIVE_CHAT_DB_KEY, JSON.stringify(updatedMessages));
    
    if (newMessage.sender.type !== 'admin' && createNotification) {
        createNotification('admin', `رسالة جديدة في الدردشة الحية من ${newMessage.sender.name}`, 'chat', '/admin?tab=live-chat');
    }
  };
  
  const editMessage = (chatId, messageId, newText) => {
    const updatedMessages = { ...messages };
    if (updatedMessages[chatId]) {
      const messageIndex = updatedMessages[chatId].findIndex(msg => msg.id === messageId);
      if (messageIndex !== -1) {
        updatedMessages[chatId][messageIndex].text = newText;
        updatedMessages[chatId][messageIndex].edited = true;
        setMessages(updatedMessages);
        localStorage.setItem(LIVE_CHAT_DB_KEY, JSON.stringify(updatedMessages));
      }
    }
  };

  const deleteMessage = (chatId, messageId) => {
    const updatedMessages = { ...messages };
    if (updatedMessages[chatId]) {
      updatedMessages[chatId] = updatedMessages[chatId].filter(msg => msg.id !== messageId);
      setMessages(updatedMessages);
      localStorage.setItem(LIVE_CHAT_DB_KEY, JSON.stringify(updatedMessages));
    }
  };

  const sendInternalMessage = (chatId, text, sender, fileLink = null) => {
    const newMessage = {
      id: uuidv4(),
      text,
      sender,
      fileLink,
      timestamp: new Date().toISOString(),
    };

    const updatedMessages = { ...internalMessages };
    if (!updatedMessages[chatId]) {
      updatedMessages[chatId] = [];
    }
    updatedMessages[chatId].push(newMessage);

    setInternalMessages(updatedMessages);
    localStorage.setItem(INTERNAL_CHAT_DB_KEY, JSON.stringify(updatedMessages));

    if (!createNotification) return;

    const isGroupChat = chatId.startsWith('group_');
    if (isGroupChat) {
        const group = chatGroups.find(g => g.id === chatId);
        if (group) {
            group.members.forEach(memberEmail => {
                if (memberEmail !== sender.id) {
                    const recipient = users.find(u => u.email === memberEmail);
                    const link = recipient ? `/${recipient.userType}/chat?open=${chatId}` : '#';
                    createNotification(memberEmail, `رسالة جديدة في مجموعة "${group.name}" من ${sender.name}`, 'chat', link);
                }
            });
        }
    } else {
        const recipientEmail = chatId.split('_').find(id => id !== sender.id);
        if (recipientEmail) {
            const allUsers = [...(students || []), ...(teachers || [])];
            const recipient = allUsers.find(u => u.email === recipientEmail);

            const linkTarget = sender.id;

            const link = recipient ? `/${recipient.userType}/chat?open=${linkTarget}` : '#';
            const message = `رسالة جديدة من ${sender.name}`;
            createNotification(recipientEmail, message, 'chat', link);
        }
    }
  };

  const sendAnnouncement = (subject, message, targetAudience, file = null) => {
    const newAnnouncement = {
        id: uuidv4(),
        subject,
        message,
        targetAudience, // 'all_students', 'all_teachers', 'all_users'
        file, // { title, link }
        timestamp: new Date().toISOString(),
        sender: { id: user.email, name: user.name, type: 'admin' }
    };

    const updatedAnnouncements = [newAnnouncement, ...announcements];
    setAnnouncements(updatedAnnouncements);
    localStorage.setItem(ANNOUNCEMENTS_DB_KEY, JSON.stringify(updatedAnnouncements));
    
    if (!createNotification) return;

    let recipients = [];
    if (targetAudience === 'all_students') recipients = students;
    else if (targetAudience === 'all_teachers') recipients = teachers;
    else if (targetAudience === 'all_users') recipients = [...students, ...teachers];

    const uniqueRecipients = Array.from(new Set(recipients.map(r => r.email)))
        .map(email => recipients.find(r => r.email === email));

    uniqueRecipients.forEach(recipient => {
        const link = `/${recipient.userType}/chat?open=inbox`;
        createNotification(recipient.email, `إشعار جديد: ${subject}`, 'announcement', link);
    });
  };

  const editInternalMessage = (chatId, messageId, newText) => {
    const updatedMessages = { ...internalMessages };
    if (updatedMessages[chatId]) {
      const messageIndex = updatedMessages[chatId].findIndex(msg => msg.id === messageId);
      if (messageIndex !== -1) {
        updatedMessages[chatId][messageIndex].text = newText;
        updatedMessages[chatId][messageIndex].edited = true;
        setInternalMessages(updatedMessages);
        localStorage.setItem(INTERNAL_CHAT_DB_KEY, JSON.stringify(updatedMessages));
      }
    }
  };

  const deleteInternalMessage = (chatId, messageId) => {
    const updatedMessages = { ...internalMessages };
    if (updatedMessages[chatId]) {
      updatedMessages[chatId] = updatedMessages[chatId].filter(msg => msg.id !== messageId);
      setInternalMessages(updatedMessages);
      localStorage.setItem(INTERNAL_CHAT_DB_KEY, JSON.stringify(updatedMessages));
    }
  };

  const getInternalChatId = (user1, user2) => {
    return [user1, user2].sort().join('_');
  };

  const createChatGroup = (name, members) => {
    const newGroup = {
      id: `group_${uuidv4()}`,
      name,
      members, // array of user emails
      createdAt: new Date().toISOString(),
    };
    const updatedGroups = [...chatGroups, newGroup];
    setChatGroups(updatedGroups);
    localStorage.setItem(CHAT_GROUPS_DB_KEY, JSON.stringify(updatedGroups));
    toast({ title: "🎉 تم إنشاء المجموعة بنجاح!" });
    return newGroup;
  };
  
  const getUserAnnouncements = () => {
    if (!user) return [];
    return announcements.filter(a => {
        if (a.targetAudience === 'all_users') return true;
        if (user.userType === 'student' && a.targetAudience === 'all_students') return true;
        if (user.userType === 'teacher' && a.targetAudience === 'all_teachers') return true;
        return false;
    });
  };

  const value = {
    messages,
    sendMessage,
    editMessage,
    deleteMessage,
    getSessionId,
    internalMessages,
    sendInternalMessage,
    sendAnnouncement,
    announcements: getUserAnnouncements(),
    editInternalMessage,
    deleteInternalMessage,
    getInternalChatId,
    chatGroups,
    createChatGroup,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};