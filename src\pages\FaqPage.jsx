import React from 'react';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useSettings } from '@/contexts/SettingsContext';
import { motion } from 'framer-motion';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const FaqPage = () => {
  const { settings } = useSettings();

  return (
    <>
      <Helmet>
        <title>الأسئلة الشائعة - Gulf Academy</title>
        <meta name="description" content="ابحث عن إجابات لأسئلتك الأكثر شيوعًا حول أكاديمية الخليج." />
      </Helmet>
      <div className="flex flex-col min-h-screen bg-background text-foreground">
        <Header />
        <main className="flex-grow">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto px-4 py-16"
          >
            <div className="max-w-3xl mx-auto">
              <h1 className="text-4xl font-bold text-center mb-8 text-white">الأسئلة الشائعة</h1>
              <Accordion type="single" collapsible className="w-full space-y-4">
                {settings.faqs.map((faq) => (
                  <AccordionItem key={faq.id} value={`item-${faq.id}`} className="glass-effect rounded-xl px-6 border-none">
                    <AccordionTrigger className="text-lg text-right font-semibold text-white hover:no-underline">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-white/80 text-right">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </motion.div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default FaqPage;