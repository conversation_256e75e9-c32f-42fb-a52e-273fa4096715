import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getDeviceFingerprint, hashString } from '@/lib/deviceFingerprint';
import { supabase } from '@/lib/customSupabaseClient';

const AuthContext = createContext(null);

const MOCK_USERS_DB = 'gulfAcademyUsers';
const CURRENT_USER_SESSION = 'currentUser';

const initializeUsers = () => {
    let usersJson = localStorage.getItem(MOCK_USERS_DB);
    let users = {};
    
    try {
        users = usersJson ? JSON.parse(usersJson) : {};
    } catch (e) {
        console.error("Could not parse users from localStorage", e);
        users = {};
    }

    const defaultUsers = {
        '<EMAIL>': { 
            name: 'Admin', 
            email: '<EMAIL>', 
            password: 'Gulf2025', 
            userType: 'admin',
            registrationDate: new Date().toISOString(),
            status: 'نشط',
            id: '<EMAIL>'
        },
        '<EMAIL>': {
            name: 'أ. محمد أحمد',
            email: '<EMAIL>',
            password: 'password',
            userType: 'teacher',
            registrationDate: new Date().toISOString(),
            specialization: 'الرياضيات',
            status: 'نشط',
            id: '<EMAIL>',
            assignedStudents: ['<EMAIL>', '<EMAIL>']
        },
        '<EMAIL>': {
            name: 'أ. سارة علي',
            email: '<EMAIL>',
            password: 'password',
            userType: 'teacher',
            registrationDate: new Date().toISOString(),
            specialization: 'الفيزياء',
            status: 'نشط',
            id: '<EMAIL>',
            assignedStudents: ['<EMAIL>']
        },
        '<EMAIL>': {
            name: 'أحمد الغامدي',
            email: '<EMAIL>',
            password: 'password',
            userType: 'student',
            registrationDate: new Date().toISOString(),
            specialization: 'المرحلة الثانوية',
            status: 'نشط',
            id: '<EMAIL>',
            countryId: 'sa',
            subscriptions: [
                { subject: 'الرياضيات', totalHours: 20 },
                { subject: 'الفيزياء', totalHours: 15 }
            ]
        },
        '<EMAIL>': {
            name: 'فاطمة الزهراني',
            email: '<EMAIL>',
            password: 'password',
            userType: 'student',
            registrationDate: new Date().toISOString(),
            specialization: 'المرحلة الثانوية',
            status: 'نشط',
            id: '<EMAIL>',
            countryId: 'qa',
            subscriptions: [
                { subject: 'الرياضيات', totalHours: 25 }
            ]
        }
    };
    
    const usersWithDefaults = { ...defaultUsers, ...users };
    
    localStorage.setItem(MOCK_USERS_DB, JSON.stringify(usersWithDefaults));
    return usersWithDefaults;
};

const useAuthHook = () => {
    const [user, setUser] = useState(null);
    const [users, setUsers] = useState({});
    const [loading, setLoading] = useState(true);
    const [loginMethod, setLoginMethod] = useState(null);
    
    useEffect(() => {
        const allUsers = initializeUsers();
        setUsers(allUsers);
        
        const sessionUserJson = localStorage.getItem(CURRENT_USER_SESSION);
        if (sessionUserJson) {
            try {
                const sessionData = JSON.parse(sessionUserJson);
                if (allUsers[sessionData.user.email]) {
                    setUser(allUsers[sessionData.user.email]);
                    setLoginMethod(sessionData.method);
                } else {
                    localStorage.removeItem(CURRENT_USER_SESSION);
                }
            } catch(e) {
                console.error("Error parsing user session", e);
                localStorage.removeItem(CURRENT_USER_SESSION);
            }
        }
        setLoading(false);
    }, []);

    const login = (email, password) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const userData = allUsers[email];
        if (userData && userData.password === password && userData.status === 'نشط') {
            const sessionData = { user: userData, method: 'password' };
            localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
            setUser(userData);
            setLoginMethod('password');
            return userData;
        }
        return null;
    };

    const loginWithAccessCode = async (accessCode) => {
        const { data: codeData, error } = await supabase
            .from('access_codes')
            .select('*')
            .eq('code', accessCode)
            .single();

        if (error || !codeData) {
            return { success: false, message: 'الكود غير صحيح أو غير موجود.' };
        }
        
        if (!codeData.is_active) {
            return { success: false, message: 'هذا الكود غير نشط حالياً.' };
        }
        
        const fingerprint = await getDeviceFingerprint();
        const fingerprintHash = await hashString(fingerprint);

        if (codeData.device_fingerprint_hash) {
            if (codeData.device_fingerprint_hash !== fingerprintHash) {
                return { success: false, message: 'تم استخدام هذا الكود من جهاز آخر ولا يمكن استخدامه هنا.' };
            }
        } else {
            await supabase
                .from('access_codes')
                .update({ 
                    device_fingerprint_hash: fingerprintHash,
                    first_used_at: new Date().toISOString()
                })
                .eq('id', codeData.id);
        }

        await supabase
            .from('access_codes')
            .update({ last_used_at: new Date().toISOString() })
            .eq('id', codeData.id);

        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const userData = allUsers[codeData.user_email];

        if (!userData || userData.status !== 'نشط') {
            return { success: false, message: 'الحساب المرتبط بهذا الكود غير نشط.' };
        }

        const sessionData = { user: userData, method: 'access_code' };
        localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
        setUser(userData);
        setLoginMethod('access_code');
        return { success: true, user: userData };
    };


    const register = (userData, byAdmin = false) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        if (allUsers[userData.email]) {
            return false; 
        }
        
        const newUser = {
            ...userData,
            id: userData.email,
            registrationDate: new Date().toISOString(),
            status: userData.status || 'نشط',
            userType: byAdmin ? userData.userType : 'student',
        };
        const updatedUsers = { ...allUsers, [userData.email]: newUser };
        
        setUsers(updatedUsers);
        localStorage.setItem(MOCK_USERS_DB, JSON.stringify(updatedUsers));

        if (!byAdmin) {
          const sessionData = { user: newUser, method: 'register' };
          localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
          setUser(newUser);
          setLoginMethod('register');
        }
        return true;
    };

    const logout = () => {
        localStorage.removeItem(CURRENT_USER_SESSION);
        setUser(null);
        setLoginMethod(null);
    };

    const updateUser = useCallback((originalEmail, updatedData) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const newEmail = updatedData.email;

        if (allUsers[originalEmail]) {
            const originalUserData = { ...allUsers[originalEmail] };
            const updatedUser = { ...originalUserData, ...updatedData };
            
            if (!updatedData.password) {
                updatedUser.password = originalUserData.password;
            }

            if (originalEmail !== newEmail) {
                if (allUsers[newEmail]) {
                    console.error("New email already exists.");
                    return false;
                }
                delete allUsers[originalEmail];
            }

            allUsers[newEmail] = updatedUser;

            if (user && user.email === originalEmail) {
                const sessionData = JSON.parse(localStorage.getItem(CURRENT_USER_SESSION));
                sessionData.user = updatedUser;
                localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
                setUser(updatedUser);
            }

            setUsers(allUsers);
            localStorage.setItem(MOCK_USERS_DB, JSON.stringify(allUsers));
            return true;
        }
        return false;
    }, [user]);

    const updateUserMetadata = useCallback((metadata) => {
        if (user) {
            const updatedUserData = { ...user, ...metadata };
            updateUser(user.email, updatedUserData);
        }
    }, [user, updateUser]);


    const deleteUser = (email) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        delete allUsers[email];
        setUsers(allUsers);
        localStorage.setItem(MOCK_USERS_DB, JSON.stringify(allUsers));
    };

    const getAllUsers = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers);
    }, []);
    
    const getAllStudents = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers).filter(u => u.userType === 'student');
    }, []);

    const getAllTeachers = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers).filter(u => u.userType === 'teacher');
    }, []);

    const getUserById = useCallback((email) => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return allUsers[email];
    }, []);
    
    const getStudentsForTeacher = useCallback((teacherEmail, bookings) => {
        const studentEmails = new Set();
        bookings.forEach(booking => {
            if (booking.teacherEmail === teacherEmail && (booking.status === 'approved' || booking.status === 'completed')) {
                studentEmails.add(booking.studentEmail);
            }
        });
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        return Array.from(studentEmails).map(email => allUsers[email]).filter(Boolean);
    }, []);

    return { 
        user,
        loginMethod,
        isAuthenticatedWithCode: loginMethod === 'access_code',
        users: getAllUsers(), 
        students: getAllStudents(),
        teachers: getAllTeachers(),
        getUserById,
        getStudentsForTeacher,
        login, 
        loginWithAccessCode,
        register, 
        logout, 
        loading,
        updateUser,
        updateUserMetadata,
        deleteUser,
        getAllStudents,
        getAllTeachers
    };
};

export const AuthProvider = ({ children }) => {
    const auth = useAuthHook();
    return (
        <AuthContext.Provider value={auth}>
            {!auth.loading && children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};