import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight, Mail, Lock, User, Phone, Eye, EyeOff, Calendar } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCountry } from '@/contexts/CountryContext';

const countryCodes = [
    { code: '+966', name: 'SA', country: 'السعودية' },
    { code: '+974', name: 'QA', country: 'قطر' },
    { code: '+965', name: 'KW', country: 'الكويت' },
    { code: '+968', name: 'OM', country: 'عُمان' },
    { code: '+971', name: 'AE', country: 'الإمارات' },
];

const RegisterPage = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    secondName: '',
    lastName: '',
    gender: '',
    birthDate: '',
    countryCode: '+966',
    phone: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { register } = useAuth();
  const { country } = useCountry();
  
  React.useEffect(() => {
    if (country) {
      const selectedCountryCode = countryCodes.find(c => c.name.toLowerCase() === country.id);
      if (selectedCountryCode) {
        setFormData(prev => ({...prev, countryCode: selectedCountryCode.code}));
      }
    }
  }, [country]);

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const { firstName, lastName, email, password, confirmPassword, gender, birthDate, phone } = formData;
    if (!firstName || !lastName || !email || !password || !gender || !birthDate || !phone) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء جميع الحقول الإجبارية",
        variant: "destructive"
      });
      return;
    }

    if (password !== confirmPassword) {
      toast({
        title: "خطأ في كلمة المرور",
        description: "كلمة المرور وتأكيدها غير متطابقتين",
        variant: "destructive"
      });
      return;
    }
    
    const fullName = `${firstName} ${formData.secondName} ${lastName}`.replace(/\s+/g, ' ').trim();
    const fullPhone = `${formData.countryCode}${formData.phone}`;
    
    const success = register({ 
      name: fullName, 
      email, 
      phone: fullPhone, 
      password,
      gender,
      birthDate,
      status: 'نشط' 
    });
    
    if (success) {
      toast({
        title: "تم إنشاء الحساب بنجاح!",
        description: `مرحباً بك ${fullName} في Gulf Academy`
      });
      navigate(`/student`);
    } else {
       toast({
        title: "فشل التسجيل",
        description: `البريد الإلكتروني ${email} مستخدم بالفعل.`,
        variant: "destructive"
      });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <>
      <Helmet>
        <title>إنشاء حساب جديد - Gulf Academy</title>
        <meta name="description" content="أنشئ حسابك الجديد في منصة Gulf Academy وابدأ رحلتك التعليمية اليوم" />
      </Helmet>

      <div className="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div className="w-full max-w-lg">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="glass-effect rounded-2xl p-8"
          >
            <div className="text-center mb-8">
              <Link to="/" className="inline-flex items-center gap-3 mb-6 justify-center">
                <img  alt="Gulf Academy Logo" class="h-12 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
              </Link>
              <h1 className="text-3xl font-bold text-white mb-2">إنشاء حساب جديد</h1>
              <p className="text-muted-foreground">انضم إلى مجتمعنا التعليمي</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-white">الاسم الأول*</Label>
                  <Input id="firstName" type="text" value={formData.firstName} onChange={(e) => handleInputChange('firstName', e.target.value)} className="bg-secondary/50 border-border text-white" required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="secondName" className="text-white">الاسم الثاني</Label>
                  <Input id="secondName" type="text" value={formData.secondName} onChange={(e) => handleInputChange('secondName', e.target.value)} className="bg-secondary/50 border-border text-white" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-white">العائلة*</Label>
                  <Input id="lastName" type="text" value={formData.lastName} onChange={(e) => handleInputChange('lastName', e.target.value)} className="bg-secondary/50 border-border text-white" required />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gender" className="text-white">الجنس*</Label>
                   <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                    <SelectTrigger className="bg-secondary/50 border-border text-white"><SelectValue placeholder="حدد الجنس..." /></SelectTrigger>
                    <SelectContent className="bg-secondary border-border text-white">
                      <SelectItem value="male">ذكر</SelectItem>
                      <SelectItem value="female">أنثى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="birthDate" className="text-white">تاريخ الميلاد*</Label>
                  <div className="relative">
                    <Calendar className="absolute right-3 top-3 h-5 w-5 text-muted-foreground" />
                    <Input id="birthDate" type="date" value={formData.birthDate} onChange={(e) => handleInputChange('birthDate', e.target.value)} className="bg-secondary/50 border-border text-white pr-10" required />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-white">رقم الهاتف*</Label>
                <div className="flex gap-2">
                  <Input id="phone" type="tel" value={formData.phone} onChange={(e) => handleInputChange('phone', e.target.value)} className="bg-secondary/50 border-border text-white flex-grow" required />
                  <Select value={formData.countryCode} onValueChange={(value) => handleInputChange('countryCode', value)}>
                    <SelectTrigger className="w-32 bg-secondary/50 border-border text-white"><SelectValue/></SelectTrigger>
                    <SelectContent className="bg-secondary border-border text-white">
                       {countryCodes.map(c => <SelectItem key={c.code} value={c.code}>{c.code} ({c.country})</SelectItem>)}
                    </SelectContent>
                  </Select>
                </div>
                 <p className="text-xs text-muted-foreground mt-1">يجب أن تتوفر على الرقم خدمة WhatsApp.</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">البريد الإلكتروني*</Label>
                <Input id="email" type="email" value={formData.email} onChange={(e) => handleInputChange('email', e.target.value)} className="bg-secondary/50 border-border text-white" required />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="password" className="text-white">كلمة المرور*</Label>
                  <div className="relative">
                    <Input id="password" type={showPassword ? "text" : "password"} value={formData.password} onChange={(e) => handleInputChange('password', e.target.value)} className="bg-secondary/50 border-border text-white" required />
                    <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-white">
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword" className="text-white">تأكيد كلمة المرور*</Label>
                  <div className="relative">
                    <Input id="confirmPassword" type={showConfirmPassword ? "text" : "password"} value={formData.confirmPassword} onChange={(e) => handleInputChange('confirmPassword', e.target.value)} className="bg-secondary/50 border-border text-white" required />
                    <button type="button" onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-white">
                      {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>
              </div>

              <Button type="submit" className="w-full bg-primary text-primary-foreground hover:bg-primary/90 text-lg py-3 mt-4">
                إنشاء الحساب <ArrowRight className="mr-2 h-5 w-5" />
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-muted-foreground">
                لديك حساب بالفعل؟{' '}
                <Link to="/login" className="text-primary hover:text-primary/80 font-medium">
                  سجل الدخول
                </Link>
              </p>
            </div>

            <div className="mt-4 text-center">
              <Link to="/" className="text-muted-foreground hover:text-white transition-colors">
                العودة إلى الصفحة الرئيسية
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default RegisterPage;