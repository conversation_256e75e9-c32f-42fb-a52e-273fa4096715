
    import React, { useState, useEffect, useCallback } from 'react';
    import { supabase } from '@/lib/customSupabaseClient';
    import { useToast } from '@/components/ui/use-toast';
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Label } from '@/components/ui/label';
    import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose, DialogDescription } from '@/components/ui/dialog';
    import { PlusCircle, Edit, Trash2, Package, Link, X } from 'lucide-react';
    import { useCountry } from '@/contexts/CountryContext';
    import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
    import { Checkbox } from '@/components/ui/checkbox';
    import { ScrollArea } from '@/components/ui/scroll-area';

    const PackageForm = ({ onSave, onCancel, existingPackage, countries }) => {
        const [pkg, setPkg] = useState(existingPackage || { name: '', country_id: '', hours: '', price: '', discount: '0', is_active: true });

        const handleChange = (field, value) => {
            setPkg(p => ({ ...p, [field]: value }));
        };

        const handleSave = () => {
            onSave(pkg);
        };

        return (
            <DialogContent className="bg-secondary border-border text-white">
                <DialogHeader>
                    <DialogTitle>{existingPackage ? 'تعديل الباقة' : 'إضافة باقة جديدة'}</DialogTitle>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    <div>
                        <Label>اسم الباقة</Label>
                        <Input value={pkg.name} onChange={e => handleChange('name', e.target.value)} placeholder="مثال: باقة 8 ساعات" />
                    </div>
                    <div>
                        <Label>الدولة</Label>
                        <Select value={pkg.country_id} onValueChange={v => handleChange('country_id', v)}>
                            <SelectTrigger><SelectValue placeholder="اختر الدولة" /></SelectTrigger>
                            <SelectContent>{countries.map(c => <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>)}</SelectContent>
                        </Select>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                        <div><Label>عدد الساعات</Label><Input type="number" value={pkg.hours} onChange={e => handleChange('hours', e.target.value)} /></div>
                        <div><Label>السعر</Label><Input type="number" value={pkg.price} onChange={e => handleChange('price', e.target.value)} /></div>
                    </div>
                    <div><Label>الخصم (إن وجد)</Label><Input type="number" value={pkg.discount} onChange={e => handleChange('discount', e.target.value)} /></div>
                    <div className="flex items-center space-x-2 space-x-reverse">
                        <Checkbox id="is_active" checked={pkg.is_active} onCheckedChange={v => handleChange('is_active', v)} />
                        <Label htmlFor="is_active">الباقة مفعلة</Label>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={onCancel}>إلغاء</Button>
                    <Button onClick={handleSave}>حفظ</Button>
                </DialogFooter>
            </DialogContent>
        );
    };

    const LinkMaterialsModal = ({ pkg, onSave, onCancel }) => {
        const { toast } = useToast();
        const [materials, setMaterials] = useState([]);
        const [selectedMaterials, setSelectedMaterials] = useState([]);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
            const fetchMaterials = async () => {
                setLoading(true);
                const { data, error } = await supabase.from('study_materials').select('id, name, subject');
                if (error) {
                    toast({ title: 'فشل جلب المواد', variant: 'destructive' });
                } else {
                    setMaterials(data);
                }

                const { data: linkedData, error: linkedError } = await supabase.from('package_materials').select('material_id').eq('package_id', pkg.id);
                if (linkedError) {
                    toast({ title: 'فشل جلب المواد المرتبطة', variant: 'destructive' });
                } else {
                    setSelectedMaterials(linkedData.map(item => item.material_id));
                }
                setLoading(false);
            };
            fetchMaterials();
        }, [pkg.id, toast]);

        const handleSave = async () => {
            await onSave(pkg.id, selectedMaterials);
            onCancel();
        };

        const handleSelect = (materialId) => {
            setSelectedMaterials(prev => 
                prev.includes(materialId) ? prev.filter(id => id !== materialId) : [...prev, materialId]
            );
        };

        return (
            <DialogContent className="bg-secondary border-border text-white max-w-2xl">
                <DialogHeader>
                    <DialogTitle>ربط المواد بالباقة: {pkg.name}</DialogTitle>
                    <DialogDescription>حدد المواد التي سيتمكن الطالب من الوصول إليها عند شراء هذه الباقة.</DialogDescription>
                </DialogHeader>
                <ScrollArea className="h-96 my-4 border border-border rounded-md p-4">
                    {loading ? <p>جاري التحميل...</p> : (
                        <div className="space-y-2">
                            {materials.map(material => (
                                <div key={material.id} className="flex items-center space-x-2 space-x-reverse p-2 rounded hover:bg-white/10">
                                    <Checkbox id={`mat-${material.id}`} checked={selectedMaterials.includes(material.id)} onCheckedChange={() => handleSelect(material.id)} />
                                    <Label htmlFor={`mat-${material.id}`} className="flex-1 cursor-pointer">{material.name} <span className="text-xs text-white/60">({material.subject})</span></Label>
                                </div>
                            ))}
                        </div>
                    )}
                </ScrollArea>
                <DialogFooter>
                    <Button variant="outline" onClick={onCancel}>إلغاء</Button>
                    <Button onClick={handleSave}>حفظ الربط</Button>
                </DialogFooter>
            </DialogContent>
        );
    };

    const PackagesContent = () => {
        const { toast } = useToast();
        const { countries } = useCountry();
        const [packages, setPackages] = useState([]);
        const [loading, setLoading] = useState(true);
        const [isFormOpen, setIsFormOpen] = useState(false);
        const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
        const [editingPackage, setEditingPackage] = useState(null);
        const [linkingPackage, setLinkingPackage] = useState(null);

        const fetchPackages = useCallback(async () => {
            setLoading(true);
            const { data, error } = await supabase.from('packages').select('*').order('created_at');
            if (error) {
                toast({ title: 'فشل جلب الباقات', variant: 'destructive' });
            } else {
                setPackages(data);
            }
            setLoading(false);
        }, [toast]);

        useEffect(() => {
            fetchPackages();
        }, [fetchPackages]);

        const handleSavePackage = async (pkgData) => {
            const { id, ...dataToSave } = pkgData;
            let error;
            if (id) {
                ({ error } = await supabase.from('packages').update(dataToSave).eq('id', id));
            } else {
                ({ error } = await supabase.from('packages').insert(dataToSave));
            }

            if (error) {
                toast({ title: 'فشل حفظ الباقة', description: error.message, variant: 'destructive' });
            } else {
                toast({ title: 'تم حفظ الباقة بنجاح' });
                setIsFormOpen(false);
                setEditingPackage(null);
                fetchPackages();
            }
        };

        const handleDeletePackage = async (packageId) => {
            const { error } = await supabase.from('packages').delete().eq('id', packageId);
            if (error) {
                toast({ title: 'فشل حذف الباقة', description: error.message, variant: 'destructive' });
            } else {
                toast({ title: 'تم حذف الباقة بنجاح' });
                fetchPackages();
            }
        };

        const handleSaveLinkedMaterials = async (packageId, materialIds) => {
            const { error: deleteError } = await supabase.from('package_materials').delete().eq('package_id', packageId);
            if (deleteError) {
                toast({ title: 'فشل تحديث الربط', variant: 'destructive' });
                return;
            }

            if (materialIds.length > 0) {
                const links = materialIds.map(material_id => ({ package_id: packageId, material_id }));
                const { error: insertError } = await supabase.from('package_materials').insert(links);
                if (insertError) {
                    toast({ title: 'فشل حفظ الربط الجديد', variant: 'destructive' });
                    return;
                }
            }
            toast({ title: 'تم حفظ ربط المواد بنجاح' });
        };

        const openForm = (pkg = null) => {
            setEditingPackage(pkg);
            setIsFormOpen(true);
        };

        const openLinkModal = (pkg) => {
            setLinkingPackage(pkg);
            setIsLinkModalOpen(true);
        };

        const getCountryName = (countryId) => countries.find(c => c.id === countryId)?.name || 'غير محدد';

        return (
            <div className="space-y-8">
                <div className="glass-effect rounded-xl p-6">
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-2xl font-bold text-white flex items-center gap-3"><Package /> إدارة الباقات والأسعار</h3>
                        <Button onClick={() => openForm()}><PlusCircle className="h-4 w-4 ml-2" /> إضافة باقة</Button>
                    </div>
                    <div className="overflow-x-auto">
                        <table className="w-full text-right">
                            <thead>
                                <tr className="border-b border-white/20">
                                    <th className="p-3">الاسم</th><th className="p-3">الدولة</th><th className="p-3">الساعات</th><th className="p-3">السعر</th><th className="p-3">الخصم</th><th className="p-3">الحالة</th><th className="p-3">إجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {loading ? <tr><td colSpan="7" className="text-center p-8">جاري التحميل...</td></tr> :
                                packages.map(pkg => (
                                    <tr key={pkg.id} className="border-b border-white/10 hover:bg-secondary/30">
                                        <td className="p-3">{pkg.name}</td>
                                        <td className="p-3">{getCountryName(pkg.country_id)}</td>
                                        <td className="p-3">{pkg.hours}</td>
                                        <td className="p-3">{pkg.price}</td>
                                        <td className="p-3">{pkg.discount}</td>
                                        <td className="p-3">{pkg.is_active ? <span className="text-green-400">مفعل</span> : <span className="text-red-400">معطل</span>}</td>
                                        <td className="p-3 flex gap-2">
                                            <Button size="icon" variant="ghost" onClick={() => openLinkModal(pkg)}><Link className="h-4 w-4 text-cyan-400" /></Button>
                                            <Button size="icon" variant="ghost" onClick={() => openForm(pkg)}><Edit className="h-4 w-4 text-yellow-400" /></Button>
                                            <Button size="icon" variant="ghost" onClick={() => handleDeletePackage(pkg.id)}><Trash2 className="h-4 w-4 text-red-400" /></Button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
                <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
                    {isFormOpen && <PackageForm onSave={handleSavePackage} onCancel={() => setIsFormOpen(false)} existingPackage={editingPackage} countries={countries} />}
                </Dialog>
                <Dialog open={isLinkModalOpen} onOpenChange={setIsLinkModalOpen}>
                    {linkingPackage && <LinkMaterialsModal pkg={linkingPackage} onSave={handleSaveLinkedMaterials} onCancel={() => setIsLinkModalOpen(false)} />}
                </Dialog>
            </div>
        );
    };

    export default PackagesContent;
  