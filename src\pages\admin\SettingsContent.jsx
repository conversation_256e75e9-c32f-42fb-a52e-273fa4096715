import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { Save, Plus, Trash2, Loader2, FileImage as ImageIcon } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

const SettingsContent = () => {
  const { toast } = useToast();
  const { settings, updateSettings, uploadAcademyLogo } = useSettings();
  const [localSettings, setLocalSettings] = useState(settings);
  const [loading, setLoading] = useState(true);
  const [isUploadingLogo, setIsUploadingLogo] = useState(false);
  const [logoFile, setLogoFile] = useState(null);
  const logoInputRef = useRef(null);

  useEffect(() => {
    setLocalSettings(settings);
    setLoading(false);
  }, [settings]);

  const handleLogoFileChange = (event) => {
    if (event.target.files.length > 0) {
      const file = event.target.files[0];
      if (file.type.startsWith('image/')) {
        setLogoFile(file);
      } else {
        toast({ title: "ملف غير صالح", description: "الرجاء اختيار ملف صورة فقط.", variant: "destructive" });
      }
    }
  };

  const handleUploadLogo = async () => {
      if (!logoFile) return;
      setIsUploadingLogo(true);
      const { data, error } = await uploadAcademyLogo(logoFile);
      if (error) {
          toast({ title: 'فشل رفع الشعار', description: error.message, variant: 'destructive' });
      } else {
          toast({ title: 'تم رفع الشعار بنجاح' });
          setLogoFile(null);
          if (logoInputRef.current) logoInputRef.current.value = "";
      }
      setIsUploadingLogo(false);
  };

  const handleFaqChange = (id, field, value) => {
    const updatedFaqs = localSettings.faqs.map(faq => faq.id === id ? { ...faq, [field]: value } : faq);
    setLocalSettings({ ...localSettings, faqs: updatedFaqs });
  };

  const handleAddFaq = () => {
    const newFaq = { id: uuidv4(), question: '', answer: '' };
    setLocalSettings({ ...localSettings, faqs: [...localSettings.faqs, newFaq] });
  };

  const handleRemoveFaq = (id) => {
    const updatedFaqs = localSettings.faqs.filter(faq => faq.id !== id);
    setLocalSettings({ ...localSettings, faqs: updatedFaqs });
  };

  const handleSave = async () => {
    setLoading(true);
    updateSettings(localSettings);
    setLoading(false);
    toast({ title: "✅ تم حفظ الإعدادات", description: "تم تحديث إعدادات الموقع بنجاح." });
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <h3 className="text-2xl font-bold text-white mb-6">إعدادات الهوية والمحتوى</h3>
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label className="text-lg font-medium text-white mb-2 block">شعار الأكاديمية</Label>
              <div className="flex gap-4 items-center bg-secondary/50 p-4 rounded-lg">
                  <img src={localSettings.academyLogoUrl || 'https://via.placeholder.com/100'} alt="شعار الأكاديمية" className="h-20 w-20 rounded-full object-cover bg-white/10" />
                  <div className="flex-1">
                      <Input id="logo-upload" type="file" accept="image/*" ref={logoInputRef} onChange={handleLogoFileChange} className="bg-white/10 border-border file:text-white" />
                      <Button onClick={handleUploadLogo} disabled={isUploadingLogo || !logoFile} size="sm" className="mt-2 w-full sm:w-auto">
                          {isUploadingLogo ? <Loader2 className="h-4 w-4 ml-2 animate-spin"/> : <ImageIcon className="h-4 w-4 ml-2" />}
                          {isUploadingLogo ? 'جاري الرفع...' : 'رفع وتغيير الشعار'}
                      </Button>
                  </div>
              </div>
            </div>
          </div>
          <div>
            <Label htmlFor="aboutUs" className="text-lg font-medium text-white mb-2 block">من نحن</Label>
            <Textarea id="aboutUs" value={localSettings.aboutUs} onChange={(e) => setLocalSettings({ ...localSettings, aboutUs: e.target.value })} rows={6} className="bg-secondary/50 border-border text-white" placeholder="اكتب هنا محتوى صفحة من نحن..."/>
          </div>
          <div>
            <Label htmlFor="terms" className="text-lg font-medium text-white mb-2 block">الشروط والخصوصية</Label>
            <Textarea id="terms" value={localSettings.termsAndPrivacy} onChange={(e) => setLocalSettings({ ...localSettings, termsAndPrivacy: e.target.value })} rows={10} className="bg-secondary/50 border-border text-white" placeholder="اكتب هنا الشروط والأحكام وسياسة الخصوصية..."/>
          </div>
        </div>
      </div>

      <div className="glass-effect rounded-xl p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-2xl font-bold text-white">الأسئلة الشائعة</h3>
          <Button onClick={handleAddFaq} variant="outline" size="sm"><Plus className="h-4 w-4 ml-2" />إضافة سؤال</Button>
        </div>
        <div className="space-y-4">
          {localSettings.faqs.map((faq, index) => (
            <div key={faq.id} className="bg-secondary/50 p-4 rounded-lg space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor={`faq-q-${faq.id}`} className="text-white">السؤال {index + 1}</Label>
                <Button variant="ghost" size="icon" className="text-red-400 hover:bg-red-500/20" onClick={() => handleRemoveFaq(faq.id)}><Trash2 className="h-4 w-4" /></Button>
              </div>
              <Input id={`faq-q-${faq.id}`} value={faq.question} onChange={(e) => handleFaqChange(faq.id, 'question', e.target.value)} placeholder="اكتب السؤال..." className="bg-white/10 border-border text-white" />
              <Label htmlFor={`faq-a-${faq.id}`} className="text-white">الإجابة</Label>
              <Textarea id={`faq-a-${faq.id}`} value={faq.answer} onChange={(e) => handleFaqChange(faq.id, 'answer', e.target.value)} placeholder="اكتب الإجابة..." className="bg-white/10 border-border text-white" rows={3} />
            </div>
          ))}
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button onClick={handleSave} size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90" disabled={loading}>
          {loading ? <Loader2 className="h-5 w-5 ml-2 animate-spin" /> : <Save className="h-5 w-5 ml-2" />}
          حفظ جميع الإعدادات
        </Button>
      </div>
    </div>
  );
};

export default SettingsContent;