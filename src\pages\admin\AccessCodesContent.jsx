
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Plus, RefreshCw, Search, KeyRound, Copy, Check, Trash2, Power, PowerOff, Calendar, Share2, CreditCard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/customSupabaseClient';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';
import StudentIdCard from '@/components/StudentIdCard';
import html2canvas from 'html2canvas';
import { useSocialShare } from '@/lib/socialShare';

const generateAccessCode = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

const AccessCodesContent = () => {
  const { toast } = useToast();
  const { students } = useAuth();
  const { share } = useSocialShare();
  const [accessCodes, setAccessCodes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newCodeData, setNewCodeData] = useState({ user_email: '', code: generateAccessCode(), expires_at: null });
  const [actionDialog, setActionDialog] = useState({ isOpen: false, type: '', data: null });
  const [copiedCode, setCopiedCode] = useState(null);
  const [cardToShare, setCardToShare] = useState(null);
  const cardRef = useRef(null);

  const fetchAccessCodes = useCallback(async () => {
    setLoading(true);
    const { data, error } = await supabase.from('access_codes').select('*').order('created_at', { ascending: false });
    if (error) {
      toast({ title: 'خطأ', description: 'فشل في جلب أكواد الدخول.', variant: 'destructive' });
    } else {
      setAccessCodes(data);
    }
    setLoading(false);
  }, [toast]);

  useEffect(() => {
    fetchAccessCodes();
  }, [fetchAccessCodes]);
  
  const handleCreateCode = async () => {
    if (!newCodeData.user_email) {
      toast({ title: "بيانات غير مكتملة", description: "الرجاء اختيار مستخدم.", variant: "destructive" });
      return;
    }
    const { error } = await supabase.from('access_codes').insert([newCodeData]);
    if (error) {
      toast({ title: "خطأ", description: "فشل إنشاء الكود، قد يكون الكود مستخدماً.", variant: "destructive" });
    } else {
      toast({ title: "✅ تم إنشاء الكود بنجاح" });
      setIsModalOpen(false);
      fetchAccessCodes();
    }
  };

  const handleAction = async () => {
    const { type, data, new_expires_at } = actionDialog;
    let error;

    if (type === 'reset') {
      ({ error } = await supabase.from('access_codes').update({ device_fingerprint_hash: null, first_used_at: null }).eq('id', data.id));
    } else if (type === 'toggle') {
      ({ error } = await supabase.from('access_codes').update({ is_active: !data.is_active }).eq('id', data.id));
    } else if (type === 'delete') {
      ({ error } = await supabase.from('access_codes').delete().eq('id', data.id));
    } else if (type === 'edit_expiry') {
      ({ error } = await supabase.from('access_codes').update({ expires_at: new_expires_at }).eq('id', data.id));
    }

    if (error) {
        toast({ title: "خطأ", description: `فشل تنفيذ الإجراء: ${error.message}`, variant: "destructive" });
    } else {
        toast({ title: "✅ تم تنفيذ الإجراء بنجاح" });
        fetchAccessCodes();
    }
    setActionDialog({ isOpen: false, type: '', data: null });
  };
  
  const copyToClipboard = (code) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(code);
    setTimeout(() => setCopiedCode(null), 2000);
  };
  
  const getUserName = (email) => {
    const user = students.find(s => s.email === email);
    return user ? user.name : email;
  }
  
  const getStudentByEmail = (email) => students.find(s => s.email === email);

  const filteredCodes = accessCodes.filter(code =>
    getUserName(code.user_email).toLowerCase().includes(searchTerm.toLowerCase()) ||
    code.code.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const isExpired = (expiryDate) => expiryDate && new Date(expiryDate) < new Date();

  const handleShareCard = async () => {
    if (!cardRef.current) return;
    try {
        const canvas = await html2canvas(cardRef.current, { backgroundColor: null });
        canvas.toBlob(async (blob) => {
            if (blob) {
                const file = new File([blob], "student-card.png", { type: "image/png" });
                const student = getStudentByEmail(cardToShare.user_email);
                await share({
                    title: `بطاقة الطالب ${student.name}`,
                    text: `استخدم هذا الكود لتسجيل الدخول: ${cardToShare.code}`,
                    files: [file],
                });
            }
        });
    } catch (error) {
        toast({ title: 'فشل مشاركة البطاقة', variant: 'destructive' });
    } finally {
        setCardToShare(null);
    }
  };
  
  useEffect(() => {
    if (cardToShare && cardRef.current) {
      handleShareCard();
    }
  }, [cardToShare]);

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">إدارة أكواد الدخول</h3>
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => { setIsModalOpen(true); setNewCodeData({ user_email: '', code: generateAccessCode(), expires_at: null }); }}><Plus className="h-4 w-4 ml-2" />إنشاء كود جديد</Button>
        </div>
        <div className="relative mb-6">
          <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input type="text" placeholder="ابحث باسم الطالب أو الكود..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="bg-secondary/50 border-border text-white placeholder:text-muted-foreground pr-10"/>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-right">
            <thead>
              <tr className="border-b border-white/20">
                <th className="p-4 text-white font-medium">الكود</th>
                <th className="p-4 text-white font-medium">المستخدم</th>
                <th className="p-4 text-white font-medium">الحالة</th>
                <th className="p-4 text-white font-medium">الصلاحية</th>
                <th className="p-4 text-white font-medium">أول استخدام</th>
                <th className="p-4 text-white font-medium text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredCodes.map((code) => (
                <tr key={code.id} className="border-b border-white/10">
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-primary tracking-widest">{code.code}</span>
                      <Button variant="ghost" size="icon" className="h-7 w-7" onClick={() => copyToClipboard(code.code)}>
                        {copiedCode === code.code ? <Check className="h-4 w-4 text-green-400" /> : <Copy className="h-4 w-4 text-white/70" />}
                      </Button>
                    </div>
                  </td>
                  <td className="p-4 text-white/80">{getUserName(code.user_email)}</td>
                  <td className="p-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${isExpired(code.expires_at) ? 'bg-gray-500/20 text-gray-300' : (code.is_active ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300')}`}>
                      {isExpired(code.expires_at) ? 'منتهي الصلاحية' : (code.is_active ? 'نشط' : 'معطل')}
                    </span>
                  </td>
                  <td className="p-4 text-white/70">{code.expires_at ? format(parseISO(code.expires_at), 'd MMM yyyy', { locale: arSA }) : 'غير محدد'}</td>
                  <td className="p-4 text-white/70">{code.first_used_at ? new Date(code.first_used_at).toLocaleString('ar-SA') : 'لم يستخدم'}</td>
                  <td className="p-4 text-center">
                    <div className="flex gap-1 justify-center">
                      <Button size="icon" variant="ghost" className="text-cyan-400 hover:text-cyan-300" onClick={() => setCardToShare(code)}><CreditCard className="h-4 w-4" /></Button>
                      <Button size="icon" variant="ghost" className="text-blue-400 hover:text-blue-300" onClick={() => setActionDialog({ isOpen: true, type: 'edit_expiry', data: code, new_expires_at: code.expires_at })}><Calendar className="h-4 w-4" /></Button>
                      <Button size="icon" variant="ghost" className="text-yellow-400 hover:text-yellow-300" onClick={() => setActionDialog({ isOpen: true, type: 'reset', data: code })}><RefreshCw className="h-4 w-4" /></Button>
                      <Button size="icon" variant="ghost" className={code.is_active ? "text-orange-400 hover:text-orange-300" : "text-green-400 hover:text-green-300"} onClick={() => setActionDialog({ isOpen: true, type: 'toggle', data: code })}>{code.is_active ? <PowerOff className="h-4 w-4" /> : <Power className="h-4 w-4" />}</Button>
                      <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={() => setActionDialog({ isOpen: true, type: 'delete', data: code })}><Trash2 className="h-4 w-4" /></Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader><DialogTitle>إنشاء كود دخول جديد</DialogTitle><DialogDescription>اربط كود دخول فريد بأحد الطلاب.</DialogDescription></DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="userEmail">الطالب</Label>
              <Select value={newCodeData.user_email} onValueChange={(value) => setNewCodeData({ ...newCodeData, user_email: value })}>
                <SelectTrigger><SelectValue placeholder="اختر طالباً..." /></SelectTrigger>
                <SelectContent>{students.map(student => <SelectItem key={student.id} value={student.email}>{student.name}</SelectItem>)}</SelectContent>
              </Select>
            </div>
            <div>
                <Label>تاريخ انتهاء الصلاحية (اختياري)</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal bg-white/10">{newCodeData.expires_at ? format(newCodeData.expires_at, "PPP", { locale: arSA }) : <span>اختر تاريخ</span>}</Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={newCodeData.expires_at} onSelect={(date) => setNewCodeData(d => ({ ...d, expires_at: date }))} initialFocus /></PopoverContent>
                </Popover>
            </div>
            <div>
              <Label htmlFor="accessCode">الكود</Label>
              <div className="flex items-center gap-2">
                <Input id="accessCode" value={newCodeData.code} readOnly className="bg-white/10 border-border font-mono tracking-widest" />
                <Button variant="outline" size="icon" onClick={() => setNewCodeData({ ...newCodeData, code: generateAccessCode() })}><RefreshCw className="h-4 w-4" /></Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>إلغاء</Button>
            <Button onClick={handleCreateCode} className="bg-primary hover:bg-primary/90">إنشاء</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      <AlertDialog open={actionDialog.isOpen} onOpenChange={(isOpen) => !isOpen && setActionDialog({ isOpen: false, type: '', data: null })}>
          <AlertDialogContent className="bg-secondary border-border text-white">
              <AlertDialogHeader>
                  <AlertDialogTitle>تأكيد الإجراء</AlertDialogTitle>
                  <AlertDialogDescription>
                      {actionDialog.type === 'reset' && `هل أنت متأكد من أنك تريد إعادة تعيين الجهاز للكود ${actionDialog.data?.code}؟`}
                      {actionDialog.type === 'toggle' && `هل أنت متأكد من أنك تريد ${actionDialog.data?.is_active ? 'تعطيل' : 'تفعيل'} الكود ${actionDialog.data?.code}?`}
                      {actionDialog.type === 'delete' && `هل أنت متأكد من حذف الكود ${actionDialog.data?.code}؟ لا يمكن التراجع.`}
                      {actionDialog.type === 'edit_expiry' && 'اختر تاريخ انتهاء الصلاحية الجديد.'}
                  </AlertDialogDescription>
                  {actionDialog.type === 'edit_expiry' && (
                     <div className='py-4'>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="outline" className="w-full justify-start text-left font-normal bg-white/10">{actionDialog.new_expires_at ? format(parseISO(actionDialog.new_expires_at), "PPP", { locale: arSA }) : <span>اختر تاريخ</span>}</Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0"><CalendarComponent mode="single" selected={actionDialog.new_expires_at ? parseISO(actionDialog.new_expires_at) : null} onSelect={(date) => setActionDialog(d => ({ ...d, new_expires_at: date?.toISOString() }))} initialFocus /></PopoverContent>
                        </Popover>
                        <Button variant="link" size="sm" onClick={() => setActionDialog(d => ({...d, new_expires_at: null}))}>إزالة تاريخ الانتهاء</Button>
                     </div>
                  )}
              </AlertDialogHeader>
              <AlertDialogFooter>
                  <AlertDialogCancel className="border-border hover:bg-white/10">إلغاء</AlertDialogCancel>
                  <AlertDialogAction onClick={handleAction} className={`${actionDialog.type === 'delete' ? 'bg-destructive hover:bg-destructive/80' : 'bg-primary hover:bg-primary/90'}`}>تأكيد</AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>

      {cardToShare && (
          <div className="fixed top-[-9999px] left-[-9999px]">
            <StudentIdCard ref={cardRef} student={getStudentByEmail(cardToShare.user_email)} accessCode={cardToShare} />
          </div>
      )}

    </div>
  );
};

export default AccessCodesContent;