import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Laptop, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Trash2,
  Eye,
  EyeOff
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  getDeviceInfo,
  getStoredDeviceInfo,
  updateDeviceLastSeen,
  verifyDevice
} from '@/lib/deviceFingerprint';
import { devicesAPI, isAPIAvailable } from '@/lib/externalAPI';

const DeviceController = ({ userEmail, isAdmin = false }) => {
  const [devices, setDevices] = useState([]);
  const [currentDevice, setCurrentDevice] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState({});
  const [useExternalAPI, setUseExternalAPI] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadDevices();
  }, [userEmail]);

  const loadDevices = async () => {
    try {
      setLoading(true);

      // التحقق من توفر API الخارجي
      const apiAvailable = await isAPIAvailable();
      setUseExternalAPI(apiAvailable);

      // جلب الجهاز الحالي
      const current = await getDeviceInfo();
      setCurrentDevice(current);

      // جلب الأجهزة المحفوظة
      if (apiAvailable) {
        await loadDevicesFromAPI();
      } else {
        loadDevicesFromLocalStorage();
      }

      // تحديث آخر ظهور
      if (apiAvailable) {
        try {
          await devicesAPI.updateLastSeen(userEmail, current.deviceId);
        } catch (error) {
          console.error('خطأ في تحديث آخر ظهور عبر API:', error);
          updateDeviceLastSeen(userEmail);
        }
      } else {
        updateDeviceLastSeen(userEmail);
      }

    } catch (error) {
      console.error('خطأ في تحميل الأجهزة:', error);
      toast({
        title: "خطأ",
        description: "فشل في تحميل معلومات الأجهزة",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // تحميل الأجهزة من API الخارجي
  const loadDevicesFromAPI = async () => {
    try {
      if (isAdmin) {
        const response = await devicesAPI.getAll();
        setDevices(response.devices || []);
      } else {
        const response = await devicesAPI.getUserDevices(userEmail);
        setDevices(response.devices || []);
      }
    } catch (error) {
      console.error('خطأ في تحميل الأجهزة من API:', error);
      // العودة إلى localStorage في حالة الخطأ
      loadDevicesFromLocalStorage();
    }
  };

  // تحميل الأجهزة من localStorage
  const loadDevicesFromLocalStorage = () => {
    if (isAdmin) {
      const allDevices = getAllStoredDevices();
      setDevices(allDevices);
    } else {
      const userDevice = getStoredDeviceInfo(userEmail);
      setDevices(userDevice ? [userDevice] : []);
    }
  };

  const getAllStoredDevices = () => {
    const devices = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('device_')) {
        try {
          const deviceData = JSON.parse(localStorage.getItem(key));
          const userEmail = key.replace('device_', '');
          devices.push({ ...deviceData, userEmail });
        } catch (error) {
          console.error('خطأ في قراءة بيانات الجهاز:', error);
        }
      }
    }
    return devices;
  };

  const getDeviceIcon = (device) => {
    const userAgent = device.browser?.userAgent || '';
    if (userAgent.includes('Mobile')) return <Smartphone className="h-5 w-5" />;
    if (userAgent.includes('Tablet')) return <Tablet className="h-5 w-5" />;
    if (userAgent.includes('Laptop')) return <Laptop className="h-5 w-5" />;
    return <Monitor className="h-5 w-5" />;
  };

  const getDeviceType = (device) => {
    const userAgent = device.browser?.userAgent || '';
    if (userAgent.includes('Mobile')) return 'هاتف محمول';
    if (userAgent.includes('Tablet')) return 'جهاز لوحي';
    if (userAgent.includes('Laptop')) return 'حاسوب محمول';
    return 'حاسوب مكتبي';
  };

  const getDeviceStatus = (device) => {
    const lastSeen = new Date(device.lastSeen);
    const now = new Date();
    const diffHours = (now - lastSeen) / (1000 * 60 * 60);
    
    if (diffHours < 1) return { status: 'نشط', color: 'bg-green-500', icon: CheckCircle };
    if (diffHours < 24) return { status: 'نشط مؤخراً', color: 'bg-yellow-500', icon: Clock };
    return { status: 'غير نشط', color: 'bg-red-500', icon: AlertTriangle };
  };

  const toggleDetails = (deviceId) => {
    setShowDetails(prev => ({
      ...prev,
      [deviceId]: !prev[deviceId]
    }));
  };

  const removeDevice = async (deviceId, userEmail) => {
    try {
      if (useExternalAPI) {
        // حذف من قاعدة البيانات الخارجية
        await devicesAPI.delete(deviceId);
      } else {
        // حذف من localStorage
        const deviceKey = `device_${userEmail}`;
        localStorage.removeItem(deviceKey);
      }

      setDevices(prev => prev.filter(d => d.deviceId !== deviceId));

      toast({
        title: "تم الحذف",
        description: "تم حذف الجهاز بنجاح",
      });
    } catch (error) {
      console.error('خطأ في حذف الجهاز:', error);
      toast({
        title: "خطأ",
        description: "فشل في حذف الجهاز",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Shield className="h-6 w-6 text-primary" />
        <h2 className="text-2xl font-bold text-white">نظام التحكم في الأجهزة</h2>
      </div>

      {/* الجهاز الحالي */}
      {currentDevice && (
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              {getDeviceIcon(currentDevice)}
              الجهاز الحالي
            </CardTitle>
            <CardDescription>
              هذا هو الجهاز الذي تستخدمه حالياً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">نوع الجهاز</p>
                <p className="text-white">{getDeviceType(currentDevice)}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">المتصفح</p>
                <p className="text-white">{currentDevice.browser?.name} {currentDevice.browser?.version}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">النظام</p>
                <p className="text-white">{currentDevice.system?.platform}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">الدقة</p>
                <p className="text-white">{currentDevice.screen?.width}x{currentDevice.screen?.height}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* قائمة الأجهزة المسجلة */}
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-white">
          {isAdmin ? 'جميع الأجهزة المسجلة' : 'أجهزتك المسجلة'}
        </h3>
        
        {devices.length === 0 ? (
          <Card className="bg-secondary/50 border-border">
            <CardContent className="p-6 text-center">
              <p className="text-muted-foreground">لا توجد أجهزة مسجلة</p>
            </CardContent>
          </Card>
        ) : (
          devices.map((device) => {
            const status = getDeviceStatus(device);
            const StatusIcon = status.icon;
            
            return (
              <motion.div
                key={device.deviceId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-secondary/50 border border-border rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getDeviceIcon(device)}
                    <div>
                      <h4 className="text-white font-medium">
                        {getDeviceType(device)}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        {device.browser?.name} على {device.system?.platform}
                      </p>
                      {isAdmin && (
                        <p className="text-xs text-muted-foreground">
                          المستخدم: {device.userEmail}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${status.color}`}></div>
                      {status.status}
                    </Badge>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleDetails(device.deviceId)}
                    >
                      {showDetails[device.deviceId] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    
                    {isAdmin && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDevice(device.deviceId, device.userEmail)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                
                {showDetails[device.deviceId] && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="mt-4 pt-4 border-t border-border"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">معرف الجهاز</p>
                        <p className="text-white font-mono">{device.deviceId}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">تاريخ التسجيل</p>
                        <p className="text-white">{formatDate(device.registeredAt)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">آخر ظهور</p>
                        <p className="text-white">{formatDate(device.lastSeen)}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">اللغة</p>
                        <p className="text-white">{device.system?.language}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">المنطقة الزمنية</p>
                        <p className="text-white">{device.system?.timezone}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">عدد المعالجات</p>
                        <p className="text-white">{device.hardware?.cores || 'غير محدد'}</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default DeviceController;
