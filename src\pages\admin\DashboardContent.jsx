import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Users, BookOpen, CheckCircle, UserCheck, UserPlus, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { useNavigate } from 'react-router-dom';

const DashboardContent = ({ setActiveTab }) => {
  const { students, teachers, users } = useAuth();
  const { bookings } = useBooking();
  const navigate = useNavigate();

  const [showPendingAlert, setShowPendingAlert] = useState(false);

  const pendingBookingsCount = bookings.filter(b => b.status === 'pending').length;
  const pendingRequestsCount = bookings.filter(b => 
    b.postponementRequest?.status === 'pending' || 
    b.teacherChangeRequest?.status === 'pending' || 
    b.deletionRequest?.status === 'pending' || 
    b.linkProblemRequest?.status === 'pending'
  ).length;

  useEffect(() => {
    if (pendingBookingsCount > 0 || pendingRequestsCount > 0) {
      setShowPendingAlert(true);
    }
  }, [pendingBookingsCount, pendingRequestsCount]);

  const totalStudents = students.length;
  const totalTeachers = teachers.length;
    
  const completedBookings = bookings.filter(b => b.status === 'completed').length;
  const activeBookings = bookings.filter(b => b.status === 'approved').length;

  const recentUsers = [...users].sort((a, b) => new Date(b.registrationDate) - new Date(a.registrationDate)).slice(0, 5);
  const recentPendingBookings = [...bookings].filter(b => b.status === 'pending').sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)).slice(0, 5);


  const getStatusClass = (status) => {
    switch (status) {
      case 'approved':
      case 'مؤكد':
      case 'نشط': return 'bg-green-500/20 text-green-300';
      case 'pending':
      case 'في الانتظار': return 'bg-yellow-500/20 text-yellow-300';
      default: return 'bg-red-500/20 text-red-300';
    }
  };
  
  const handleGoToBookings = () => {
    setShowPendingAlert(false);
    setActiveTab('bookings');
  };

  return (
    <>
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="stats-card rounded-xl p-6">
          <div className="flex items-center gap-4"><div className="bg-blue-500 p-3 rounded-full"><UserPlus className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الطلاب</p><p className="text-2xl font-bold text-white">{totalStudents}</p></div></div>
        </div>
        <div className="stats-card rounded-xl p-6">
          <div className="flex items-center gap-4"><div className="bg-indigo-500 p-3 rounded-full"><UserCheck className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">المعلمون</p><p className="text-2xl font-bold text-white">{totalTeachers}</p></div></div>
        </div>
        <div className="stats-card rounded-xl p-6">
          <div className="flex items-center gap-4"><div className="bg-green-500 p-3 rounded-full"><BookOpen className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الحجوزات النشطة</p><p className="text-2xl font-bold text-white">{activeBookings}</p></div></div>
        </div>
        <div className="stats-card rounded-xl p-6">
          <div className="flex items-center gap-4"><div className="bg-purple-500 p-3 rounded-full"><CheckCircle className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الحجوزات المكتملة</p><p className="text-2xl font-bold text-white">{completedBookings}</p></div></div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6"><h3 className="text-xl font-bold text-white">طلبات الحجز الجديدة</h3><Button size="sm" variant="outline" className="border-white/30 text-white hover:bg-white/10" onClick={() => setActiveTab('bookings')}>إدارة الحجوزات</Button></div>
          <div className="space-y-4">
             {recentPendingBookings.length > 0 ? recentPendingBookings.map((booking) => (
              <div key={booking.id} className="bg-white/5 rounded-lg p-4"><div className="flex items-center justify-between"><div><p className="font-medium text-white">{booking.studentName}</p><p className="text-white/70 text-sm">{booking.teacherName} - {booking.subjects.map(s => s.subject).join(', ')}</p><p className="text-white/60 text-sm">{format(new Date(booking.createdAt), 'd MMMM yyyy, hh:mm a', { locale: arSA })}</p></div><span className={`px-3 py-1 rounded-full text-xs ${getStatusClass(booking.status)}`}>في الانتظار</span></div></div>
            )) : <p className="text-center text-white/70 py-4">لا توجد طلبات حجز جديدة.</p>}
          </div>
        </div>
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6"><h3 className="text-xl font-bold text-white">المستخدمون الجدد</h3><Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => setActiveTab('users')}>عرض الكل</Button></div>
          <div className="space-y-4">
            {recentUsers.map((user) => (
              <div key={user.id} className="bg-white/5 rounded-lg p-4"><div className="flex items-center justify-between"><div><p className="font-medium text-white">{user.name}</p><p className="text-white/70 text-sm">{user.email}</p><p className="text-white/60 text-sm capitalize">{user.userType} - {format(new Date(user.registrationDate), 'd MMMM yyyy', { locale: arSA })}</p></div><span className={`px-3 py-1 rounded-full text-xs ${getStatusClass(user.status)}`}>{user.status}</span></div></div>
            ))}
          </div>
        </div>
      </div>
    </div>
    <AlertDialog open={showPendingAlert} onOpenChange={setShowPendingAlert}>
        <AlertDialogContent className="bg-secondary border-border text-white">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="text-yellow-400 h-6 w-6" />
              تنبيه: توجد طلبات تحتاج إلى إجراء
            </AlertDialogTitle>
            <AlertDialogDescription className="text-white/80 pt-2">
              لديك {pendingBookingsCount} حجوزات جديدة و {pendingRequestsCount} طلبات إجرائية بانتظار المراجعة.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <Button variant="outline" onClick={() => setShowPendingAlert(false)}>
              إغلاق
            </Button>
            <AlertDialogAction onClick={handleGoToBookings}>
              الانتقال إلى صفحة الحجوزات
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DashboardContent;