import React, { useState, useEffect } from 'react';
import { useSettings } from '@/contexts/SettingsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Mail, Send, Trash2, Archive, Inbox, MailQuestion } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

const InquiriesContent = () => {
  const { settings, updateInquiry } = useSettings();
  const { toast } = useToast();
  const [selectedInquiry, setSelectedInquiry] = useState(null);
  const [reply, setReply] = useState('');
  const [inquiries, setInquiries] = useState([]);

  useEffect(() => {
    // Filter to show only contact form submissions.
    const contactInquiries = settings.inquiries?.filter(inq => inq.type === 'contact') || [];
    setInquiries(contactInquiries.sort((a, b) => new Date(b.date) - new Date(a.date)));
  }, [settings.inquiries]);

  const handleReply = () => {
    if (!reply) {
      toast({ title: 'الرد فارغ', variant: 'destructive' });
      return;
    }
    
    toast({ title: '✅ تم إرسال الرد (محاكاة)', description: `تم إرسال الرد إلى ${selectedInquiry.email}` });
    
    updateInquiry({ ...selectedInquiry, status: 'تم الرد' });
    
    setSelectedInquiry(null);
    setReply('');
  };

  const handleStatusChange = (inquiry, status) => {
    updateInquiry({ ...inquiry, status });
    toast({ title: `✅ تم تغيير الحالة إلى "${status}"` });
  };
  
  const handleDelete = (inquiry) => {
    // In a real app, you might want a confirmation dialog.
    // Here we'll just filter it out from the view. This is not a permanent delete.
    const updatedInquiry = { ...inquiry, status: 'محذوف' };
    updateInquiry(updatedInquiry);
    toast({ title: '🗑️ تم نقل الاستفسار إلى سلة المحذوفات' });
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'جديد': return 'bg-blue-500/20 text-blue-300';
      case 'تم الرد': return 'bg-green-500/20 text-green-300';
      case 'مؤرشف': return 'bg-gray-500/20 text-gray-300';
      default: return 'bg-white/10 text-white/70';
    }
  };

  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center gap-4 mb-6">
        <MailQuestion className="h-8 w-8 text-primary" />
        <h3 className="text-2xl font-bold text-white">صندوق الاستفسارات</h3>
      </div>
      <p className="text-white/70 mb-6">
        عرض وإدارة الرسائل الواردة من نموذج "اتصل بنا".
      </p>
      <div className="space-y-4">
        {inquiries && inquiries.length > 0 ? (
          inquiries.filter(inq => inq.status !== 'محذوف').map(inquiry => (
            <div key={inquiry.id} className="bg-secondary/50 rounded-lg p-4 flex items-center justify-between hover:bg-secondary/70 transition-colors duration-200 cursor-pointer" onClick={() => setSelectedInquiry(inquiry)}>
              <div>
                <p className="font-bold text-white">{inquiry.subject}</p>
                <p className="text-white/70 text-sm">{inquiry.name} ({inquiry.email})</p>
                <p className="text-white/60 text-xs mt-1">
                  {new Date(inquiry.date).toLocaleString('ar-SA')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusClass(inquiry.status)}`}>{inquiry.status}</span>
                <Button size="icon" variant="ghost" className="text-yellow-400 hover:text-yellow-300" onClick={(e) => { e.stopPropagation(); handleStatusChange(inquiry, 'مؤرشف'); }}><Archive className="h-4 w-4" /></Button>
                <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={(e) => { e.stopPropagation(); handleDelete(inquiry); }}><Trash2 className="h-4 w-4" /></Button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-white/70 py-12">
            <Inbox className="h-16 w-16 mx-auto text-white/30 mb-4" />
            <p className="text-xl">صندوق الاستفسارات فارغ</p>
            <p>لم تصل أي رسائل جديدة بعد.</p>
          </div>
        )}
      </div>

      {selectedInquiry && (
        <Dialog open={!!selectedInquiry} onOpenChange={() => setSelectedInquiry(null)}>
          <DialogContent className="bg-secondary border-border text-white max-w-2xl">
            <DialogHeader>
              <DialogTitle>الرد على: {selectedInquiry.subject}</DialogTitle>
              <DialogDescription>من: {selectedInquiry.name} ({selectedInquiry.email})</DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div className="bg-white/5 p-4 rounded-md max-h-60 overflow-y-auto">
                <p className="text-white/80 whitespace-pre-wrap">{selectedInquiry.message}</p>
              </div>
              <div>
                <Textarea value={reply} onChange={(e) => setReply(e.target.value)} placeholder="اكتب ردك هنا..." rows={5} className="bg-white/10 border-border text-white" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedInquiry(null)}>إغلاق</Button>
              <Button onClick={handleReply} className="bg-primary hover:bg-primary/90"><Send className="h-4 w-4 ml-2" />إرسال الرد</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default InquiriesContent;