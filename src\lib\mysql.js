// External Database API Configuration
// تكوين API قاعدة البيانات الخارجية

// إعدادات API قاعدة البيانات الخارجية
const API_CONFIG = {
  baseURL: process.env.VITE_API_BASE_URL || 'https://your-api-endpoint.com/api',
  apiKey: process.env.VITE_API_KEY || '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// SQL Scripts لإنشاء الجداول المطلوبة
export const CREATE_TABLES_SQL = `
-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(255) PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  gender ENUM('male', 'female'),
  birth_date DATE,
  user_type ENUM('admin', 'teacher', 'student') DEFAULT 'student',
  status ENUM('نشط', 'غير نشط', 'محظور') DEFAULT 'نشط',
  device_registered BOOLEAN DEFAULT FALSE,
  device_id VARCHAR(32),
  registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_user_type (user_type),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول بيانات الأجهزة
CREATE TABLE IF NOT EXISTS user_devices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_email VARCHAR(255) NOT NULL,
  device_id VARCHAR(32) NOT NULL,
  device_fingerprint VARCHAR(64) NOT NULL,
  browser_name VARCHAR(50),
  browser_version VARCHAR(20),
  browser_user_agent TEXT,
  system_platform VARCHAR(100),
  system_language VARCHAR(10),
  system_languages TEXT,
  system_timezone VARCHAR(50),
  system_timezone_offset INT,
  screen_width INT,
  screen_height INT,
  screen_color_depth INT,
  screen_pixel_ratio DECIMAL(3,2),
  hardware_cores INT,
  hardware_memory INT,
  hardware_max_touch_points INT,
  registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (user_email) REFERENCES users(email) ON DELETE CASCADE,
  UNIQUE KEY unique_user_device (user_email, device_id),
  INDEX idx_user_email (user_email),
  INDEX idx_device_id (device_id),
  INDEX idx_fingerprint (device_fingerprint),
  INDEX idx_last_seen (last_seen)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول محاولات تسجيل الدخول
CREATE TABLE IF NOT EXISTS login_attempts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_email VARCHAR(255),
  device_fingerprint VARCHAR(64),
  attempt_type ENUM('success', 'failed_password', 'failed_device', 'blocked') NOT NULL,
  ip_address VARCHAR(45), -- للاستخدام المستقبلي إذا لزم الأمر
  user_agent TEXT,
  attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  success BOOLEAN DEFAULT FALSE,
  failure_reason TEXT,
  INDEX idx_user_email (user_email),
  INDEX idx_attempt_time (attempt_time),
  INDEX idx_attempt_type (attempt_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description TEXT,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by VARCHAR(255),
  INDEX idx_setting_key (setting_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('device_security_enabled', 'true', 'boolean', 'تفعيل نظام أمان الأجهزة'),
('max_devices_per_user', '1', 'number', 'الحد الأقصى للأجهزة المسموحة لكل مستخدم'),
('device_verification_required', 'true', 'boolean', 'إجبارية التحقق من الجهاز عند تسجيل الدخول'),
('failed_login_attempts_limit', '5', 'number', 'عدد محاولات تسجيل الدخول الفاشلة المسموحة'),
('device_session_timeout', '24', 'number', 'مدة انتهاء جلسة الجهاز بالساعات');
`;

// دوال للتعامل مع قاعدة البيانات الخارجية عبر API
export class ExternalDatabaseController {
  constructor(apiConfig = API_CONFIG) {
    this.config = apiConfig;
  }

  // دالة مساعدة لإرسال طلبات API
  async apiRequest(endpoint, method = 'GET', data = null) {
    try {
      const url = `${this.config.baseURL}${endpoint}`;
      const options = {
        method,
        headers: {
          ...this.config.headers,
          'Authorization': `Bearer ${this.config.apiKey}`
        },
        timeout: this.config.timeout
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('خطأ في طلب API:', error);
      throw error;
    }
  }

  // حفظ بيانات الجهاز
  async saveDeviceInfo(userEmail, deviceInfo) {
    const data = {
      user_email: userEmail,
      device_id: deviceInfo.deviceId,
      device_fingerprint: deviceInfo.fingerprint,
      browser_name: deviceInfo.browser?.name,
      browser_version: deviceInfo.browser?.version,
      browser_user_agent: deviceInfo.browser?.userAgent,
      system_platform: deviceInfo.system?.platform,
      system_language: deviceInfo.system?.language,
      system_languages: deviceInfo.system?.languages,
      system_timezone: deviceInfo.system?.timezone,
      system_timezone_offset: deviceInfo.system?.timezoneOffset,
      screen_width: deviceInfo.screen?.width,
      screen_height: deviceInfo.screen?.height,
      screen_color_depth: deviceInfo.screen?.colorDepth,
      screen_pixel_ratio: deviceInfo.screen?.pixelRatio,
      hardware_cores: deviceInfo.hardware?.cores,
      hardware_memory: deviceInfo.hardware?.memory,
      hardware_max_touch_points: deviceInfo.hardware?.maxTouchPoints,
      registered_at: deviceInfo.registeredAt,
      last_seen: deviceInfo.lastSeen
    };

    return await this.apiRequest('/devices', 'POST', data);
  }

  // التحقق من الجهاز
  async verifyDevice(userEmail, deviceFingerprint) {
    try {
      const response = await this.apiRequest(`/devices/verify`, 'POST', {
        user_email: userEmail,
        device_fingerprint: deviceFingerprint
      });
      return response.is_valid || false;
    } catch (error) {
      console.error('خطأ في التحقق من الجهاز:', error);
      return false;
    }
  }

  // جلب أجهزة المستخدم
  async getUserDevices(userEmail) {
    try {
      const response = await this.apiRequest(`/devices/user/${encodeURIComponent(userEmail)}`);
      return response.devices || [];
    } catch (error) {
      console.error('خطأ في جلب أجهزة المستخدم:', error);
      return [];
    }
  }

  // جلب جميع الأجهزة (للمدير)
  async getAllDevices() {
    try {
      const response = await this.apiRequest('/devices/all');
      return response.devices || [];
    } catch (error) {
      console.error('خطأ في جلب جميع الأجهزة:', error);
      return [];
    }
  }

  // تسجيل محاولة تسجيل دخول
  async logLoginAttempt(userEmail, deviceFingerprint, attemptType, success, failureReason = null) {
    const query = `
      INSERT INTO login_attempts (
        user_email, device_fingerprint, attempt_type, user_agent, 
        success, failure_reason
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      userEmail,
      deviceFingerprint,
      attemptType,
      navigator.userAgent,
      success,
      failureReason
    ];

    return await this.db.execute(query, values);
  }

  // حذف جهاز
  async removeDevice(userEmail, deviceId) {
    const query = `
      UPDATE user_devices 
      SET is_active = FALSE 
      WHERE user_email = ? AND device_id = ?
    `;
    return await this.db.execute(query, [userEmail, deviceId]);
  }

  // تحديث آخر ظهور للجهاز
  async updateDeviceLastSeen(userEmail, deviceFingerprint) {
    const query = `
      UPDATE user_devices 
      SET last_seen = CURRENT_TIMESTAMP 
      WHERE user_email = ? AND device_fingerprint = ?
    `;
    return await this.db.execute(query, [userEmail, deviceFingerprint]);
  }
}

// دالة للحصول على إعدادات النظام
export async function getSystemSetting(db, settingKey, defaultValue = null) {
  try {
    const query = 'SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?';
    const [rows] = await db.execute(query, [settingKey]);
    
    if (rows.length === 0) {
      return defaultValue;
    }

    const { setting_value, setting_type } = rows[0];
    
    switch (setting_type) {
      case 'boolean':
        return setting_value === 'true';
      case 'number':
        return parseFloat(setting_value);
      case 'json':
        return JSON.parse(setting_value);
      default:
        return setting_value;
    }
  } catch (error) {
    console.error('خطأ في جلب إعدادات النظام:', error);
    return defaultValue;
  }
}

// دالة لتحديث إعدادات النظام
export async function updateSystemSetting(db, settingKey, settingValue, settingType = 'string', updatedBy = null) {
  try {
    const query = `
      INSERT INTO system_settings (setting_key, setting_value, setting_type, updated_by)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        setting_value = VALUES(setting_value),
        setting_type = VALUES(setting_type),
        updated_by = VALUES(updated_by),
        updated_at = CURRENT_TIMESTAMP
    `;
    
    const valueToStore = settingType === 'json' ? JSON.stringify(settingValue) : String(settingValue);
    return await db.execute(query, [settingKey, valueToStore, settingType, updatedBy]);
  } catch (error) {
    console.error('خطأ في تحديث إعدادات النظام:', error);
    throw error;
  }
}

export default MYSQL_CONFIG;
