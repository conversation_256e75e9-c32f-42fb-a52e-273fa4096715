import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useCountry } from '@/contexts/CountryContext';

const PRICING_DB_KEY = 'gulfAcademyPricing';

const PricingContent = () => {
  const { toast } = useToast();
  const { countries, updateCountryPackages } = useCountry();
  const [pricing, setPricing] = useState({});
  const [selectedCountryId, setSelectedCountryId] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPackage, setEditingPackage] = useState(null);
  const [packageToDelete, setPackageToDelete] = useState(null);

  useEffect(() => {
    if (countries.length > 0 && !selectedCountryId) {
        setSelectedCountryId(countries[0].id);
    }
    const initialPricing = countries.reduce((acc, country) => {
        acc[country.id] = country.packages;
        return acc;
    }, {});
    
    const storedPricing = JSON.parse(localStorage.getItem(PRICING_DB_KEY) || '{}');
    
    // Merge initial and stored pricing
    const finalPricing = { ...initialPricing };
    for (const countryId in storedPricing) {
        if (finalPricing[countryId]) {
            finalPricing[countryId] = storedPricing[countryId];
        }
    }
    
    setPricing(finalPricing);
  }, [countries, selectedCountryId]);

  const savePricing = (countryId, updatedPackages) => {
    const updatedPricing = { ...pricing, [countryId]: updatedPackages };
    setPricing(updatedPricing);
    localStorage.setItem(PRICING_DB_KEY, JSON.stringify(updatedPricing));
    updateCountryPackages(countryId, updatedPackages);
  };

  const handleOpenModal = (pkg = null) => {
    setEditingPackage(pkg ? { ...pkg } : { id: Date.now().toString(), name: '', sessions: '', price: '' });
    setIsModalOpen(true);
  };

  const handleSave = () => {
    if (!editingPackage.name || !editingPackage.sessions || !editingPackage.price) {
      toast({ title: "بيانات غير مكتملة", variant: "destructive" });
      return;
    }
    const countryPackages = pricing[selectedCountryId] || [];
    const existingPackage = countryPackages.find(p => p.id === editingPackage.id);
    let updatedPackages;
    if (existingPackage) {
      updatedPackages = countryPackages.map(p => p.id === editingPackage.id ? editingPackage : p);
    } else {
      updatedPackages = [...countryPackages, editingPackage];
    }
    savePricing(selectedCountryId, updatedPackages);
    toast({ title: `✅ تم ${existingPackage ? 'تحديث' : 'إضافة'} الباقة بنجاح` });
    setIsModalOpen(false);
    setEditingPackage(null);
  };

  const handleDelete = () => {
    if (packageToDelete) {
      const updatedPackages = (pricing[selectedCountryId] || []).filter(p => p.id !== packageToDelete.id);
      savePricing(selectedCountryId, updatedPackages);
      toast({ title: '🗑️ تم حذف الباقة بنجاح' });
      setPackageToDelete(null);
    }
  };

  const selectedCountry = countries.find(c => c.id === selectedCountryId);
  const countryPackages = pricing[selectedCountryId] || [];

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <h3 className="text-2xl font-bold text-white mb-6">إدارة باقات الأسعار</h3>
        <div className="mb-6 flex items-center justify-between">
          <div>
            <Label className="text-white mb-2 block">اختر الدولة لعرض/تعديل الباقات</Label>
            <Select onValueChange={setSelectedCountryId} value={selectedCountryId}>
              <SelectTrigger className="w-[180px] bg-secondary/50 border-border text-white"><SelectValue placeholder="اختر دولة..." /></SelectTrigger>
              <SelectContent className="bg-secondary border-border text-white">{countries.map(c => <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>)}</SelectContent>
            </Select>
          </div>
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => handleOpenModal()}><Plus className="h-4 w-4 ml-2" />إضافة باقة جديدة</Button>
        </div>

        {selectedCountry && (
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="py-3 px-4 text-white font-medium">اسم الباقة</th>
                  <th className="py-3 px-4 text-white font-medium">عدد الحصص</th>
                  <th className="py-3 px-4 text-white font-medium">السعر ({selectedCountry.currencyCode})</th>
                  <th className="py-3 px-4 text-white font-medium text-center">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {countryPackages.map((pkg) => (
                  <tr key={pkg.id} className="border-b border-white/10">
                    <td className="py-3 px-4 text-white">{pkg.name}</td>
                    <td className="py-3 px-4 text-white/70">{pkg.sessions}</td>
                    <td className="py-3 px-4 text-white/70">{pkg.price}</td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex justify-center gap-1">
                        <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={() => handleOpenModal(pkg)}><Edit className="h-4 w-4" /></Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={() => setPackageToDelete(pkg)}><Trash2 className="h-4 w-4" /></Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent className="bg-secondary border-border text-white">
                            <AlertDialogHeader><AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle><AlertDialogDescription>سيتم حذف باقة "{pkg.name}" بشكل نهائي.</AlertDialogDescription></AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel className="border-border hover:bg-white/10">إلغاء</AlertDialogCancel>
                              <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/80">حذف</AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>{editingPackage?.id && countryPackages.some(p => p.id === editingPackage.id) ? 'تعديل' : 'إضافة'} باقة لـ{selectedCountry?.name}</DialogTitle>
          </DialogHeader>
          {editingPackage && (
            <div className="space-y-4 py-4">
              <div>
                <Label htmlFor="pkgName" className="text-white/80 mb-2 block">اسم الباقة</Label>
                <Input id="pkgName" value={editingPackage.name} onChange={(e) => setEditingPackage({ ...editingPackage, name: e.target.value })} className="bg-white/10 border-border text-white" />
              </div>
              <div>
                <Label htmlFor="pkgSessions" className="text-white/80 mb-2 block">عدد الحصص</Label>
                <Input id="pkgSessions" type="number" value={editingPackage.sessions} onChange={(e) => setEditingPackage({ ...editingPackage, sessions: e.target.value })} className="bg-white/10 border-border text-white" />
              </div>
              <div>
                <Label htmlFor="pkgPrice" className="text-white/80 mb-2 block">السعر ({selectedCountry?.currencyCode})</Label>
                <Input id="pkgPrice" type="number" value={editingPackage.price} onChange={(e) => setEditingPackage({ ...editingPackage, price: e.target.value })} className="bg-white/10 border-border text-white" />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)} className="border-border hover:bg-white/10">إلغاء</Button>
            <Button onClick={handleSave} className="bg-primary text-primary-foreground hover:bg-primary/90">حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PricingContent;