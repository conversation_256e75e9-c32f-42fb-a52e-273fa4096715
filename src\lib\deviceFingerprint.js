// Device Controller - نظام التحكم في الأجهزة
// يقوم بجمع بيانات الجهاز وإنشاء بصمة فريدة للجهاز

export const getDeviceFingerprint = () => {
  return new Promise((resolve) => {
    try {
      // Canvas fingerprinting - بصمة الرسم
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const txt = 'Gulf Academy Device Controller - نظام التحكم في الأجهزة';
      ctx.textBaseline = "top";
      ctx.font = "14px 'Arial'";
      ctx.textBaseline = "alphabetic";
      ctx.fillStyle = "#f60";
      ctx.fillRect(125, 1, 62, 20);
      ctx.fillStyle = "#069";
      ctx.fillText(txt, 2, 15);
      ctx.fillStyle = "rgba(102, 204, 0, 0.7)";
      ctx.fillText(txt, 4, 17);

      // معلومات المتصفح والنظام
      const userAgent = navigator.userAgent;
      const language = navigator.language || navigator.languages[0];
      const languages = navigator.languages ? navigator.languages.join(',') : '';

      // معلومات الشاشة
      const screenResolution = `${window.screen.width}x${window.screen.height}x${window.screen.colorDepth}`;
      const availableScreenSize = `${window.screen.availWidth}x${window.screen.availHeight}`;
      const pixelRatio = window.devicePixelRatio || 1;

      // معلومات التوقيت والمنطقة الزمنية
      const timezone = new Date().getTimezoneOffset();
      const timezoneString = Intl.DateTimeFormat().resolvedOptions().timeZone;

      // معلومات النظام
      const platform = navigator.platform;
      const hardwareConcurrency = navigator.hardwareConcurrency || 0;
      const maxTouchPoints = navigator.maxTouchPoints || 0;

      // معلومات الإضافات (بدون تفاصيل حساسة)
      const pluginsCount = navigator.plugins ? navigator.plugins.length : 0;

      // معلومات WebGL
      let webglInfo = '';
      try {
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
          const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
          if (debugInfo) {
            webglInfo = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
          }
        }
      } catch (e) {
        webglInfo = 'unavailable';
      }

      // معلومات الذاكرة (إذا كانت متاحة)
      const memory = navigator.deviceMemory || 0;

      // معلومات الاتصال
      const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
      const connectionType = connection ? connection.effectiveType || connection.type : 'unknown';

      // دمج جميع البيانات
      const deviceData = [
        canvas.toDataURL(),
        userAgent,
        language,
        languages,
        screenResolution,
        availableScreenSize,
        pixelRatio,
        timezone,
        timezoneString,
        platform,
        hardwareConcurrency,
        maxTouchPoints,
        pluginsCount,
        webglInfo,
        memory,
        connectionType,
        // إضافة timestamp لضمان التفرد
        Date.now().toString()
      ].join('~~~');

      resolve(deviceData);
    } catch (error) {
      console.error('خطأ في جمع بيانات الجهاز:', error);
      // في حالة الخطأ، نستخدم بيانات أساسية
      const fallbackData = [
        navigator.userAgent || 'unknown',
        navigator.language || 'unknown',
        `${window.screen.width}x${window.screen.height}`,
        new Date().getTimezoneOffset().toString(),
        navigator.platform || 'unknown',
        Date.now().toString()
      ].join('~~~');
      resolve(fallbackData);
    }
  });
};

export const hashString = async (str) => {
  const encoder = new TextEncoder();
  const data = encoder.encode(str);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  return hashHex;
};

// إنشاء معرف فريد للجهاز
export const generateDeviceId = async () => {
  const fingerprint = await getDeviceFingerprint();
  const deviceHash = await hashString(fingerprint);
  return deviceHash.substring(0, 16); // استخدام أول 16 حرف كمعرف
};

// جمع معلومات الجهاز المفصلة
export const getDeviceInfo = async () => {
  try {
    const fingerprint = await getDeviceFingerprint();
    const deviceId = await generateDeviceId();

    return {
      deviceId,
      fingerprint: await hashString(fingerprint),
      browser: {
        name: getBrowserName(),
        version: getBrowserVersion(),
        userAgent: navigator.userAgent
      },
      system: {
        platform: navigator.platform,
        language: navigator.language,
        languages: navigator.languages ? navigator.languages.join(',') : '',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset()
      },
      screen: {
        width: window.screen.width,
        height: window.screen.height,
        colorDepth: window.screen.colorDepth,
        pixelRatio: window.devicePixelRatio || 1
      },
      hardware: {
        cores: navigator.hardwareConcurrency || 0,
        memory: navigator.deviceMemory || 0,
        maxTouchPoints: navigator.maxTouchPoints || 0
      },
      registeredAt: new Date().toISOString(),
      lastSeen: new Date().toISOString()
    };
  } catch (error) {
    console.error('خطأ في جمع معلومات الجهاز:', error);
    throw error;
  }
};

// استخراج اسم المتصفح
const getBrowserName = () => {
  const userAgent = navigator.userAgent;
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
};

// استخراج إصدار المتصفح
const getBrowserVersion = () => {
  const userAgent = navigator.userAgent;
  const match = userAgent.match(/(Chrome|Firefox|Safari|Edge|Opera)\/(\d+)/);
  return match ? match[2] : 'Unknown';
};

// التحقق من تطابق الجهاز
export const verifyDevice = async (storedFingerprint) => {
  try {
    const currentFingerprint = await getDeviceFingerprint();
    const currentHash = await hashString(currentFingerprint);
    return currentHash === storedFingerprint;
  } catch (error) {
    console.error('خطأ في التحقق من الجهاز:', error);
    return false;
  }
};

// حفظ معلومات الجهاز في التخزين المحلي
export const saveDeviceInfo = async (userEmail) => {
  try {
    const deviceInfo = await getDeviceInfo();
    const deviceKey = `device_${userEmail}`;
    localStorage.setItem(deviceKey, JSON.stringify(deviceInfo));
    return deviceInfo;
  } catch (error) {
    console.error('خطأ في حفظ معلومات الجهاز:', error);
    throw error;
  }
};

// استرجاع معلومات الجهاز من التخزين المحلي
export const getStoredDeviceInfo = (userEmail) => {
  try {
    const deviceKey = `device_${userEmail}`;
    const stored = localStorage.getItem(deviceKey);
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.error('خطأ في استرجاع معلومات الجهاز:', error);
    return null;
  }
};

// تحديث آخر ظهور للجهاز
export const updateDeviceLastSeen = (userEmail) => {
  try {
    const deviceInfo = getStoredDeviceInfo(userEmail);
    if (deviceInfo) {
      deviceInfo.lastSeen = new Date().toISOString();
      const deviceKey = `device_${userEmail}`;
      localStorage.setItem(deviceKey, JSON.stringify(deviceInfo));
    }
  } catch (error) {
    console.error('خطأ في تحديث آخر ظهور للجهاز:', error);
  }
};