import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, Clock, User, BookOpen } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useCountry } from '@/contexts/CountryContext';

const BookingCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedSubject, setSelectedSubject] = useState('');
  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const { toast } = useToast();
  const { country: selectedCountry } = useCountry();

  const subjects = ['الرياضيات', 'الفيزياء', 'الكيمياء', 'الأحياء', 'اللغة العربية'];
  const teachers = [
    { id: 1, name: 'أ. محمد أحمد', subject: 'الرياضيات', rating: 4.9 },
    { id: 2, name: 'أ. سارة علي', subject: 'الفيزياء', rating: 4.8 },
    { id: 3, name: 'أ. أحمد محمود', subject: 'الكيمياء', rating: 4.7 }
  ];
  const timeSlots = ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'];

  const getDaysInMonth = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const startingDayOfWeek = new Date(year, month, 1).getDay();
    const days = Array.from({ length: startingDayOfWeek }, () => null);
    for (let day = 1; day <= daysInMonth; day++) days.push(new Date(year, month, day));
    return days;
  };

  const handleDateClick = (date) => {
    if (date && date >= new Date().setHours(0, 0, 0, 0)) {
      setSelectedDate(date);
      setSelectedTime(null);
    }
  };

  const handleBooking = () => {
    if (!selectedDate || !selectedTime || !selectedSubject || !selectedTeacher) {
      toast({ title: "بيانات ناقصة", description: "يرجى اختيار جميع البيانات المطلوبة", variant: "destructive" });
      return;
    }
    toast({ title: "تم حجز الحصة بنجاح!", description: `تم حجز حصة ${selectedSubject} يوم ${selectedDate.toLocaleDateString('ar-SA')} في ${selectedTime}` });
    setSelectedDate(null);
    setSelectedTime(null);
    setSelectedSubject('');
    setSelectedTeacher('');
  };

  const filteredTeachers = teachers.filter(t => !selectedSubject || t.subject === selectedSubject);
  const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
  const dayNames = ['ح', 'ن', 'ث', 'ر', 'خ', 'ج', 'س'];

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-white font-medium">المادة</label>
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white"><div className="flex items-center gap-2"><BookOpen className="h-4 w-4" /><SelectValue placeholder="اختر المادة" /></div></SelectTrigger>
            <SelectContent>{subjects.map((s) => <SelectItem key={s} value={s}>{s}</SelectItem>)}</SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <label className="block text-white font-medium">المعلم</label>
          <Select value={selectedTeacher} onValueChange={setSelectedTeacher} disabled={!selectedSubject}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white"><div className="flex items-center gap-2"><User className="h-4 w-4" /><SelectValue placeholder="اختر المعلم" /></div></SelectTrigger>
            <SelectContent>{filteredTeachers.map((t) => <SelectItem key={t.id} value={t.name}>{t.name} - تقييم {t.rating}</SelectItem>)}</SelectContent>
          </Select>
        </div>
      </div>

      <div className="bg-white/5 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-white">اختر التاريخ</h3>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() - 1)))} className="text-white hover:bg-white/10"><ChevronRight className="h-4 w-4" /></Button>
            <span className="text-white font-medium">{monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}</span>
            <Button variant="ghost" size="sm" onClick={() => setCurrentMonth(new Date(currentMonth.setMonth(currentMonth.getMonth() + 1)))} className="text-white hover:bg-white/10"><ChevronLeft className="h-4 w-4" /></Button>
          </div>
        </div>
        <div className="grid grid-cols-7 gap-2 mb-4">{dayNames.map((day) => <div key={day} className="text-center text-white/70 font-medium py-2">{day}</div>)}</div>
        <div className="grid grid-cols-7 gap-2">
          {getDaysInMonth(currentMonth).map((date, index) => (
            <motion.button key={index} whileHover={{ scale: date ? 1.05 : 1 }} whileTap={{ scale: date ? 0.95 : 1 }} onClick={() => handleDateClick(date)} disabled={!date || date < new Date().setHours(0, 0, 0, 0)} className={`calendar-day h-12 rounded-lg text-white font-medium ${!date ? 'invisible' : date < new Date().setHours(0, 0, 0, 0) ? 'opacity-30 cursor-not-allowed' : selectedDate?.toDateString() === date.toDateString() ? 'selected' : 'available'}`}>{date ? date.getDate() : ''}</motion.button>
          ))}
        </div>
      </div>

      {selectedDate && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="bg-white/5 rounded-xl p-6">
          <div className="flex items-center gap-2 mb-6"><Clock className="h-5 w-5 text-white" /><h3 className="text-xl font-bold text-white">اختر الوقت</h3></div>
          <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {timeSlots.map((time) => <motion.button key={time} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} onClick={() => setSelectedTime(time)} className={`time-slot h-12 rounded-lg text-white font-medium ${selectedTime === time ? 'selected' : 'available'}`}>{time}</motion.button>)}
          </div>
        </motion.div>
      )}

      {selectedDate && selectedTime && selectedSubject && selectedTeacher && (
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="bg-white/5 rounded-xl p-6">
          <h3 className="text-xl font-bold text-white mb-4">ملخص الحجز</h3>
          <div className="space-y-3 mb-6">
            <div className="flex justify-between"><span className="text-white/70">المادة:</span><span className="text-white font-medium">{selectedSubject}</span></div>
            <div className="flex justify-between"><span className="text-white/70">المعلم:</span><span className="text-white font-medium">{selectedTeacher}</span></div>
            <div className="flex justify-between"><span className="text-white/70">التاريخ:</span><span className="text-white font-medium">{selectedDate.toLocaleDateString('ar-SA')}</span></div>
            <div className="flex justify-between"><span className="text-white/70">الوقت:</span><span className="text-white font-medium">{selectedTime}</span></div>
            {selectedCountry && <div className="flex justify-between"><span className="text-white/70">السعر:</span><span className="text-white font-medium">50 {selectedCountry.currencyCode}</span></div>}
          </div>
          <Button onClick={handleBooking} className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg py-3">تأكيد الحجز</Button>
        </motion.div>
      )}
    </div>
  );
};

export default BookingCalendar;