// Device Controller Test Utilities
// أدوات اختبار نظام التحكم في الأجهزة

import { 
  getDeviceFingerprint, 
  hashString, 
  getDeviceInfo, 
  saveDeviceInfo, 
  verifyDevice,
  updateDeviceLastSeen 
} from '@/lib/deviceFingerprint';

// اختبار شامل لنظام التحكم في الأجهزة
export class DeviceControllerTester {
  constructor() {
    this.testResults = [];
    this.testEmail = '<EMAIL>';
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    console.log('🚀 بدء اختبار نظام التحكم في الأجهزة...');
    
    try {
      await this.testDeviceFingerprint();
      await this.testDeviceInfo();
      await this.testDeviceRegistration();
      await this.testDeviceVerification();
      await this.testDeviceUpdate();
      await this.testErrorHandling();
      
      this.printResults();
      return this.testResults;
    } catch (error) {
      console.error('❌ خطأ في تشغيل الاختبارات:', error);
      return null;
    }
  }

  // اختبار جمع بصمة الجهاز
  async testDeviceFingerprint() {
    console.log('📱 اختبار جمع بصمة الجهاز...');
    
    try {
      const fingerprint = await getDeviceFingerprint();
      
      this.assert(
        fingerprint && typeof fingerprint === 'string' && fingerprint.length > 0,
        'جمع بصمة الجهاز',
        'تم جمع بصمة الجهاز بنجاح'
      );

      const hash = await hashString(fingerprint);
      
      this.assert(
        hash && typeof hash === 'string' && hash.length === 64,
        'تشفير بصمة الجهاز',
        'تم تشفير البصمة بنجاح (SHA-256)'
      );

      // اختبار ثبات البصمة
      const fingerprint2 = await getDeviceFingerprint();
      const hash2 = await hashString(fingerprint2);
      
      this.assert(
        hash === hash2,
        'ثبات بصمة الجهاز',
        'البصمة ثابتة عبر الاستدعاءات المتعددة'
      );

    } catch (error) {
      this.assert(false, 'جمع بصمة الجهاز', `خطأ: ${error.message}`);
    }
  }

  // اختبار جمع معلومات الجهاز
  async testDeviceInfo() {
    console.log('ℹ️ اختبار جمع معلومات الجهاز...');
    
    try {
      const deviceInfo = await getDeviceInfo();
      
      this.assert(
        deviceInfo && typeof deviceInfo === 'object',
        'جمع معلومات الجهاز',
        'تم جمع معلومات الجهاز بنجاح'
      );

      // التحقق من وجود الحقول المطلوبة
      const requiredFields = ['deviceId', 'fingerprint', 'browser', 'system', 'screen', 'hardware'];
      
      for (const field of requiredFields) {
        this.assert(
          deviceInfo.hasOwnProperty(field),
          `وجود حقل ${field}`,
          `الحقل ${field} موجود في معلومات الجهاز`
        );
      }

      // التحقق من معلومات المتصفح
      this.assert(
        deviceInfo.browser && deviceInfo.browser.name,
        'معلومات المتصفح',
        'تم جمع معلومات المتصفح بنجاح'
      );

      // التحقق من معلومات النظام
      this.assert(
        deviceInfo.system && deviceInfo.system.platform,
        'معلومات النظام',
        'تم جمع معلومات النظام بنجاح'
      );

    } catch (error) {
      this.assert(false, 'جمع معلومات الجهاز', `خطأ: ${error.message}`);
    }
  }

  // اختبار تسجيل الجهاز
  async testDeviceRegistration() {
    console.log('📝 اختبار تسجيل الجهاز...');
    
    try {
      // حذف أي بيانات سابقة
      localStorage.removeItem(`device_${this.testEmail}`);
      
      const deviceInfo = await saveDeviceInfo(this.testEmail);
      
      this.assert(
        deviceInfo && deviceInfo.deviceId,
        'تسجيل الجهاز',
        'تم تسجيل الجهاز بنجاح'
      );

      // التحقق من حفظ البيانات
      const storedData = localStorage.getItem(`device_${this.testEmail}`);
      
      this.assert(
        storedData !== null,
        'حفظ بيانات الجهاز',
        'تم حفظ بيانات الجهاز في التخزين المحلي'
      );

      const parsedData = JSON.parse(storedData);
      
      this.assert(
        parsedData.deviceId === deviceInfo.deviceId,
        'تطابق بيانات الجهاز',
        'البيانات المحفوظة تطابق البيانات المُرجعة'
      );

    } catch (error) {
      this.assert(false, 'تسجيل الجهاز', `خطأ: ${error.message}`);
    }
  }

  // اختبار التحقق من الجهاز
  async testDeviceVerification() {
    console.log('🔍 اختبار التحقق من الجهاز...');
    
    try {
      // التأكد من وجود جهاز مسجل
      const deviceInfo = await saveDeviceInfo(this.testEmail);
      
      // اختبار التحقق الصحيح
      const isValid = await verifyDevice(deviceInfo.fingerprint);
      
      this.assert(
        isValid === true,
        'التحقق من الجهاز الصحيح',
        'تم التحقق من الجهاز المسجل بنجاح'
      );

      // اختبار التحقق الخاطئ
      const fakeFingerprint = 'fake_fingerprint_hash';
      const isInvalid = await verifyDevice(fakeFingerprint);
      
      this.assert(
        isInvalid === false,
        'رفض الجهاز غير المسجل',
        'تم رفض الجهاز غير المسجل بنجاح'
      );

    } catch (error) {
      this.assert(false, 'التحقق من الجهاز', `خطأ: ${error.message}`);
    }
  }

  // اختبار تحديث بيانات الجهاز
  async testDeviceUpdate() {
    console.log('🔄 اختبار تحديث بيانات الجهاز...');
    
    try {
      // تسجيل جهاز جديد
      await saveDeviceInfo(this.testEmail);
      
      // الحصول على الوقت الأولي
      const initialData = JSON.parse(localStorage.getItem(`device_${this.testEmail}`));
      const initialTime = new Date(initialData.lastSeen);
      
      // انتظار قصير
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // تحديث آخر ظهور
      updateDeviceLastSeen(this.testEmail);
      
      // التحقق من التحديث
      const updatedData = JSON.parse(localStorage.getItem(`device_${this.testEmail}`));
      const updatedTime = new Date(updatedData.lastSeen);
      
      this.assert(
        updatedTime > initialTime,
        'تحديث آخر ظهور',
        'تم تحديث وقت آخر ظهور بنجاح'
      );

    } catch (error) {
      this.assert(false, 'تحديث بيانات الجهاز', `خطأ: ${error.message}`);
    }
  }

  // اختبار معالجة الأخطاء
  async testErrorHandling() {
    console.log('⚠️ اختبار معالجة الأخطاء...');
    
    try {
      // اختبار معالجة بيانات فاسدة
      localStorage.setItem(`device_invalid`, 'invalid_json');
      
      // محاولة قراءة البيانات الفاسدة
      const invalidData = localStorage.getItem('device_invalid');
      let errorCaught = false;
      
      try {
        JSON.parse(invalidData);
      } catch (parseError) {
        errorCaught = true;
      }
      
      this.assert(
        errorCaught,
        'معالجة JSON فاسد',
        'تم اكتشاف ومعالجة البيانات الفاسدة'
      );

      // تنظيف البيانات الفاسدة
      localStorage.removeItem('device_invalid');

    } catch (error) {
      this.assert(false, 'معالجة الأخطاء', `خطأ: ${error.message}`);
    }
  }

  // دالة مساعدة للتحقق من النتائج
  assert(condition, testName, message) {
    const result = {
      test: testName,
      passed: condition,
      message: message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    if (condition) {
      console.log(`✅ ${testName}: ${message}`);
    } else {
      console.log(`❌ ${testName}: ${message}`);
    }
  }

  // طباعة ملخص النتائج
  printResults() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    console.log('\n📊 ملخص نتائج الاختبار:');
    console.log(`إجمالي الاختبارات: ${totalTests}`);
    console.log(`نجح: ${passedTests} ✅`);
    console.log(`فشل: ${failedTests} ❌`);
    console.log(`معدل النجاح: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ الاختبارات الفاشلة:');
      this.testResults
        .filter(r => !r.passed)
        .forEach(r => console.log(`- ${r.test}: ${r.message}`));
    }
  }

  // تنظيف بيانات الاختبار
  cleanup() {
    localStorage.removeItem(`device_${this.testEmail}`);
    console.log('🧹 تم تنظيف بيانات الاختبار');
  }
}

// دوال مساعدة للاختبار السريع
export const quickTest = async () => {
  const tester = new DeviceControllerTester();
  const results = await tester.runAllTests();
  tester.cleanup();
  return results;
};

// اختبار بصمة الجهاز فقط
export const testFingerprint = async () => {
  console.log('🔍 اختبار سريع لبصمة الجهاز...');
  
  try {
    const fingerprint = await getDeviceFingerprint();
    const hash = await hashString(fingerprint);
    
    console.log('البصمة الخام:', fingerprint.substring(0, 100) + '...');
    console.log('البصمة المشفرة:', hash);
    console.log('طول البصمة:', fingerprint.length);
    console.log('طول الهاش:', hash.length);
    
    return { fingerprint, hash };
  } catch (error) {
    console.error('خطأ في اختبار البصمة:', error);
    return null;
  }
};

// اختبار معلومات الجهاز فقط
export const testDeviceInfo = async () => {
  console.log('📱 اختبار سريع لمعلومات الجهاز...');
  
  try {
    const deviceInfo = await getDeviceInfo();
    console.log('معلومات الجهاز:', JSON.stringify(deviceInfo, null, 2));
    return deviceInfo;
  } catch (error) {
    console.error('خطأ في اختبار معلومات الجهاز:', error);
    return null;
  }
};

export default DeviceControllerTester;
