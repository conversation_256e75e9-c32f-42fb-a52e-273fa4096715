import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { useCountry } from '@/contexts/CountryContext';

const countries = [
  { id: 'sa', name: 'السعودية', flagUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/4a4808d96425052db5d46edb5dd49c66.jpg' },
  { id: 'qa', name: 'قطر', flagUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/670e54af2e448ae4a6a42cfd9d96ba72.jpg' },
  { id: 'kw', name: 'الكويت', flagUrl: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/585d007cec3f352f8e21a334db8e7184.jpg' },
  { id: 'om', name: 'عُمان', flagUrl: 'https://horizons-cdn.hostinger.com/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/a5e775253126c14c19619713d4af89c1.jpg' },
  { id: 'ae', name: 'الإمارات', flagUrl: 'https://horizons-cdn.hostinger.com/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/9b70e5a20a1d3fc06819adeedb4efa15.jpg' },
];

const CountrySelector = () => {
  const [selected, setSelected] = useState(null);
  const { selectCountry, countries: countriesWithData } = useCountry();

  const handleConfirm = () => {
    if (selected) {
      const fullCountryData = countriesWithData.find(c => c.id === selected.id);
      if (fullCountryData) {
        selectCountry(fullCountryData);
      }
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="glass-effect rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-primary/20"
        >
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">مرحباً بك في <span className="text-gradient">Gulf Academy</span></h2>
            <p className="text-muted-foreground text-lg">اختر دولتك للحصول على تجربة مخصصة</p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
            {countries.map((country) => (
              <motion.div
                key={country.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setSelected(country)}
                className={`country-card rounded-xl p-6 cursor-pointer border-2 ${selected?.id === country.id ? 'border-primary' : 'border-transparent'}`}
              >
                <div className="text-center">
                  <img  src={country.flagUrl} alt={`علم ${country.name}`} className="w-24 h-16 object-cover rounded-md mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white">{country.name}</h3>
                </div>
              </motion.div>
            ))}
          </div>
          <div className="flex justify-center">
            <Button
              onClick={handleConfirm}
              disabled={!selected}
              size="lg"
              className="bg-primary text-primary-foreground hover:bg-primary/90 disabled:opacity-50"
            >
              تأكيد الاختيار
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default CountrySelector;