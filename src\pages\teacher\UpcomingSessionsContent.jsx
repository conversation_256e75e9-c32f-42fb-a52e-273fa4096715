import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Calendar, Clock, AlertTriangle, Link as LinkIcon, XCircle } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose, DialogDescription } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { format, parseISO, isAfter, isBefore, addMinutes } from 'date-fns';
import { arSA } from 'date-fns/locale';

const PostponeModal = ({ booking, onPostpone }) => {
  const [reason, setReason] = useState('');
  const { toast } = useToast();

  const handleSubmit = () => {
    if (!reason.trim()) {
      toast({ title: "خطأ", description: "يرجى كتابة سبب التأجيل.", variant: "destructive" });
      return;
    }
    onPostpone(booking.id, reason);
  };

  const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;

  return (
    <DialogContent className="bg-secondary border-border text-white">
      <DialogHeader>
        <DialogTitle>طلب تأجيل اشتراك</DialogTitle>
        <DialogDescription>
            أنت على وشك طلب تأجيل اشتراك الطالب/الطلاب {studentNames} في مادة {booking.subjects.map(s=>s.subject).join(', ')}. سيتم إرسال الطلب للإدارة للموافقة.
        </DialogDescription>
      </DialogHeader>
      <div className="py-4 space-y-4">
        <Label htmlFor="postpone-reason">سبب التأجيل</Label>
        <Textarea id="postpone-reason" value={reason} onChange={(e) => setReason(e.target.value)} placeholder="اكتب سبب طلب التأجيل هنا..." className="bg-white/10 border-border" />
      </div>
      <DialogFooter>
        <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
        <DialogClose asChild><Button onClick={handleSubmit}>إرسال الطلب</Button></DialogClose>
      </DialogFooter>
    </DialogContent>
  );
};

const UpcomingSessionsContent = () => {
  const { user } = useAuth();
  const { bookings, requestPostponement, requestNewLink, trackSessionJoin, trackSessionLeave, markSessionAttendance } = useBooking();
  const { toast } = useToast();
  
  const [isPostponeModalOpen, setIsPostponeModalOpen] = useState(false);
  const [selectedBookingForPostpone, setSelectedBookingForPostpone] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000); // Update every minute
    return () => clearInterval(timer);
  }, []);

  const myBookings = bookings.filter(b => b.teacherEmail === user.email && b.status === 'approved');

  const handlePostponeClick = (booking) => {
    setSelectedBookingForPostpone(booking);
    setIsPostponeModalOpen(true);
  };

  const handlePostponeSubmit = (bookingId, reason) => {
    requestPostponement(bookingId, reason, 'teacher');
    toast({ title: "✅ تم إرسال طلب التأجيل", description: "سيتم مراجعته من قبل الإدارة." });
    setIsPostponeModalOpen(false);
  };
  
  const handleJoin = (booking, session) => {
    if (booking.zoomLink && session) {
        trackSessionJoin(booking.id, session.sessionId, 'teacher');
        const newWindow = window.open(booking.zoomLink, '_blank', 'noopener,noreferrer');
        
        const checkWindowClosed = setInterval(() => {
            if (!newWindow || newWindow.closed) {
                trackSessionLeave(booking.id, session.sessionId, 'teacher');
                clearInterval(checkWindowClosed);
            }
        }, 1000);
    } else {
        toast({ title: "خطأ", description: "رابط الحصة غير متوفر بعد أو لا توجد حصة قادمة.", variant: "destructive" });
    }
  };

  const handleReportProblem = (bookingId) => {
    requestNewLink(bookingId, "أبلغ المعلم عن مشكلة في الرابط.");
    toast({ title: "✅ تم إرسال البلاغ للإدارة.", description: "سيتم تزويدكم برابط جديد قريباً." });
  };

  const handleEndLesson = useCallback((bookingId, sessionId) => {
    markSessionAttendance(bookingId, sessionId, 'absent', 0);
    toast({ title: 'تم إنهاء الحصة', description: 'تم تسجيل الحصة على أنها غياب لعدم الحضور.' });
  }, [markSessionAttendance, toast]);

  const getStatusBadge = (booking) => {
    if (booking.linkProblemRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-orange-900 text-orange-300">بلاغ عن رابط قيد المراجعة</span>;
    }
    if (booking.teacherChangeRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-cyan-900 text-cyan-300">طلب تغيير معلم قيد المراجعة</span>;
    }
    if (booking.postponementRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-yellow-900 text-yellow-300">طلب تأجيل قيد المراجعة</span>;
    }
    return null;
  };

  const getNextSessionInfo = (booking) => {
    const upcomingSessions = (booking.sessions || [])
      .filter(s => s.status === 'scheduled' && isAfter(parseISO(`${s.date}T${s.time}`), new Date()))
      .sort((a, b) => parseISO(`${a.date}T${a.time}`) - parseISO(`${b.date}T${b.time}`));
    
    if (upcomingSessions.length > 0) {
      const nextSession = upcomingSessions[0];
      try {
        const sessionDateTime = parseISO(`${nextSession.date}T${nextSession.time}`);
        return {
            text: `الحصة القادمة: ${format(sessionDateTime, "eeee, d MMMM yyyy 'الساعة' p", { locale: arSA })}`,
            session: nextSession
        };
      } catch (e) {
        return { text: "تاريخ غير صالح", session: null };
      }
    }
    return { text: "لا توجد حصص قادمة مجدولة", session: null };
  };

  return (
    <>
      <div className="space-y-8">
        <div className="glass-effect rounded-xl p-6">
          <h3 className="text-2xl font-bold text-white mb-6">الاشتراكات الفعالة</h3>
          {myBookings.length === 0 ? (
            <div className="text-center py-12 text-white/70">
              <Calendar className="mx-auto h-12 w-12" />
              <p className="mt-4">ليس لديك أي اشتراكات فعالة حالياً.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {myBookings.map((booking) => {
                const { text: nextSessionText, session: nextSession } = getNextSessionInfo(booking);
                const completedHours = (booking.completedDuration || 0) / 60;
                const progress = booking.totalHours > 0 ? (completedHours / booking.totalHours) * 100 : 0;
                const countryName = booking.country?.name || (typeof booking.country === 'string' ? booking.country : '');
                const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;
                
                let isJoinable = false;
                if (nextSession) {
                    const sessionTime = parseISO(`${nextSession.date}T${nextSession.time}`);
                    const tenMinutesBefore = new Date(sessionTime.getTime() - 10 * 60 * 1000);
                    const sessionEndTime = addMinutes(sessionTime, 90); // Joinable for 90 minutes
                    isJoinable = isAfter(currentTime, tenMinutesBefore) && isBefore(currentTime, sessionEndTime);
                }

                return (
                <div key={booking.id} className="bg-secondary/50 rounded-lg p-4">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div className="flex-1">
                        <div className="flex items-center flex-wrap gap-2">
                          <p className="font-medium text-white text-lg">{booking.subjects.map(s=>s.subject).join(', ')} مع الطالب/الطلاب {studentNames} {countryName && `(${countryName})`}</p>
                           {getStatusBadge(booking)}
                        </div>
                        <p className="text-sm text-primary mt-1">{nextSessionText}</p>
                       <div className="w-full mt-2">
                           <div className="flex justify-between text-xs text-white/80 mb-1">
                               <span>التقدم في الباقة</span>
                               <span>{completedHours.toFixed(2)} / {booking.totalHours} ساعة</span>
                           </div>
                           <Progress value={progress} className="w-full h-2" />
                       </div>
                    </div>
                    <div className="flex items-center gap-2 flex-wrap">
                      <Button variant="outline" className="border-primary text-primary hover:bg-primary/10" onClick={() => handleJoin(booking, nextSession)} disabled={!booking.zoomLink}>
                        {isJoinable && <span className="animate-pulse absolute h-3 w-3 rounded-full bg-green-400 top-0 right-0 -mt-1 -mr-1"></span>}
                        <LinkIcon className="h-4 w-4 ml-2" />انضم الآن
                      </Button>
                      <Button variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500/10" onClick={() => handleReportProblem(booking.id)}><AlertTriangle className="h-4 w-4 ml-2" />إبلاغ عن مشكلة</Button>
                      <Button variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500/10" onClick={() => handlePostponeClick(booking)}>
                        <Clock className="h-4 w-4 ml-2" /> تأجيل
                      </Button>
                      {nextSession && (
                        <Button size="sm" variant="destructive" onClick={() => handleEndLesson(booking.id, nextSession.sessionId)}>
                            <XCircle className="h-4 w-4 ml-1" />
                            إنهاء
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )})}
            </div>
          )}
        </div>
      </div>
      <Dialog open={isPostponeModalOpen} onOpenChange={setIsPostponeModalOpen}>
        {selectedBookingForPostpone && <PostponeModal booking={selectedBookingForPostpone} onPostpone={handlePostponeSubmit} />}
      </Dialog>
    </>
  );
};

export default UpcomingSessionsContent;