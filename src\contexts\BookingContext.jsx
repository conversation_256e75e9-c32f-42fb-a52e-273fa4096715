import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useNotifications } from './NotificationsContext';
import { useAuth } from './AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/customSupabaseClient.js';
import { loadInitialData, createBookingRequestState, updateBookingState, getAvailableSlotsState } from './booking/bookingState';
import { approveBookingAction, handleNewLinkRequestAction } from './booking/bookingActions';
import { handleSubscriptionDeletionState, handlePostponementRequestState, handleTeacherChangeRequestState } from './booking/requestHandlers';
import { useSettings } from './SettingsContext';

const BOOKINGS_DB_KEY = 'gulfAcademyBookings';
const TEACHER_AVAILABILITY_DB_KEY = 'gulfAcademyTeacherAvailability';
const BookingContext = createContext();

export const useBooking = () => {
    const context = useContext(BookingContext);
    if (context === undefined) {
        throw new Error('useBooking must be used within a BookingProvider');
    }
    return context;
};

export const BookingProvider = ({ children }) => {
  const [availability, setAvailability] = useState({});
  const [bookings, setBookings] = useState([]);
  const [sessionLogs, setSessionLogs] = useState([]);
  const { createNotification } = useNotifications();
  const { getUserById, user } = useAuth();
  const { toast } = useToast();
  const { settings } = useSettings();

  const updateBookingsInStorage = useCallback((updatedBookings) => {
    setBookings(updatedBookings);
    localStorage.setItem(BOOKINGS_DB_KEY, JSON.stringify(updatedBookings));
  }, []);

  const markSessionAsCompleted = useCallback((bookingId, sessionId, durationInMinutes, attendanceStatus = 'present') => {
      setBookings(prevBookings => {
          const newBookings = prevBookings.map(b => {
              if (b.id === bookingId) {
                  const updatedSessions = b.sessions.map(s => s.sessionId === sessionId ? {...s, attendance: attendanceStatus, duration: durationInMinutes, status: 'completed'} : s);
                  const completedSessionsCount = updatedSessions.filter(s => s.status === 'completed').length;
                  const totalCompletedDuration = updatedSessions.reduce((acc, s) => acc + (s.duration || 0), 0);
                  
                  const isFinished = completedSessionsCount >= b.sessionsTotal;

                  return {
                      ...b,
                      sessions: updatedSessions,
                      sessionsCompleted: completedSessionsCount,
                      completedDuration: totalCompletedDuration,
                      status: isFinished ? 'completed' : b.status,
                      zoomLink: attendanceStatus === 'absent' ? null : b.zoomLink, // Remove link if absent
                  };
              }
              return b;
          });
          updateBookingsInStorage(newBookings);
          return newBookings;
      });
  }, [updateBookingsInStorage]);

  useEffect(() => {
    const { availability: loadedAvailability, bookings: loadedBookings } = loadInitialData();
    setAvailability(loadedAvailability);
    setBookings(loadedBookings);
    
    const fetchLogs = async () => {
      const { data, error } = await supabase.from('session_logs').select('*');
      if (error) console.error("Error fetching session logs:", { message: error.message, details: error.details, hint: error.hint, code: error.code });
      else setSessionLogs(data || []);
    };
    fetchLogs();

    const channel = supabase
      .channel('session_logs_changes')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'session_logs' }, (payload) => {
        let updatedLog = payload.new;
        let oldLog = payload.old;

        if (payload.eventType === 'INSERT') {
          setSessionLogs(prev => [...prev, updatedLog]);
        } else if (payload.eventType === 'UPDATE') {
          setSessionLogs(prev => prev.map(log => log.id === updatedLog.id ? updatedLog : log));
          
          if (updatedLog.status === 'completed' && oldLog.status !== 'completed') {
            markSessionAsCompleted(updatedLog.booking_id, updatedLog.session_id, updatedLog.actual_session_duration_minutes);
            
            setBookings(currentBookings => {
              const booking = currentBookings.find(b => b.id === updatedLog.booking_id);
              if(booking) {
                createNotification(booking.studentEmail, `اكتملت حصة ${booking.subjects.map(s=>s.subject).join(', ')}.`, 'success', '/student/reports');
                createNotification(booking.teacherEmail, `اكتملت حصة ${booking.subjects.map(s=>s.subject).join(', ')} مع ${booking.studentName}.`, 'success', '/teacher/reports');
                createNotification('admin', `اكتملت حصة بين ${booking.studentName} و ${booking.teacherName}.`, 'info', '/admin/finished-lessons');
              }
              return currentBookings;
            });
          }
        } else if (payload.eventType === 'DELETE') {
          setSessionLogs(prev => prev.filter(log => log.id !== oldLog.id));
        }
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };

  }, [createNotification, markSessionAsCompleted]);
  
  const updateTeacherAvailability = (teacherEmail, newAvailability) => {
    const updated = { ...availability, [teacherEmail]: newAvailability };
    setAvailability(updated);
    localStorage.setItem(TEACHER_AVAILABILITY_DB_KEY, JSON.stringify(updated));
  };

  const createBookingRequest = (bookingDetails) => {
    const updatedBookings = createBookingRequestState(bookings, bookingDetails, createNotification);
    updateBookingsInStorage(updatedBookings);
  };
  
  const approveBooking = async (bookingId) => {
    const bookingToApprove = bookings.find(b => b.id === bookingId);
    if (!bookingToApprove) return;
    const approvedBooking = await approveBookingAction(bookingToApprove, createNotification, getUserById, toast, settings.zoom);
    if(approvedBooking) {
      const updatedBookings = bookings.map(b => b.id === bookingId ? approvedBooking : b);
      updateBookingsInStorage(updatedBookings);
    }
  };
  
  const rejectBooking = (bookingId) => {
    const booking = bookings.find(b => b.id === bookingId);
    if(booking) {
      createNotification(booking.studentEmail, `تم رفض طلب الحجز الخاص بك لمادة ${booking.subjects[0]?.subject || 'جديدة'}.`, 'error', '/student/upcoming-sessions');
    }
    const updatedBookings = bookings.map(b => b.id === bookingId ? { ...b, status: 'rejected' } : b);
    updateBookingsInStorage(updatedBookings);
  };

  const deleteBooking = (bookingId) => {
    const updatedBookings = bookings.filter(b => b.id !== bookingId);
    updateBookingsInStorage(updatedBookings);
  };
  
  const requestSubscriptionDeletion = (bookingId, reason) => {
    const updatedBookings = bookings.map(b => (b.id === bookingId) ? { ...b, deletionRequest: { reason, status: 'pending' } } : b);
    createNotification('admin', `طلب إلغاء اشتراك من الطالب ${bookings.find(b=>b.id===bookingId)?.studentName}.`, 'warning', '/admin/bookings');
    updateBookingsInStorage(updatedBookings);
  };

  const handleSubscriptionDeletion = (bookingId, approved) => {
    const updatedBookings = handleSubscriptionDeletionState(bookings, bookingId, approved, createNotification);
    const bookingIsDeleted = !updatedBookings.some(b => b.id === bookingId);
    if (bookingIsDeleted) {
       updateBookingsInStorage(bookings.filter(b => b.id !== bookingId));
    } else {
       updateBookingsInStorage(updatedBookings);
    }
  };

  const requestPostponement = (bookingId, reason, userType) => {
    const booking = bookings.find(b => b.id === bookingId);
    if(!booking) return;
    const updatedBookings = bookings.map(b => (b.id === bookingId) ? { ...b, postponementRequest: { from: userType, reason, status: 'pending' } } : b);
    const requester = userType === 'student' ? booking.studentName : booking.teacherName;
    createNotification('admin', `طلب تأجيل حصة من ${requester}.`, 'warning', '/admin/bookings');
    updateBookingsInStorage(updatedBookings);
  };
  
  const handlePostponementRequest = (bookingId, approved, newDate, newTime) => {
    const updatedBookings = handlePostponementRequestState(bookings, bookingId, approved, newDate, newTime, createNotification);
    updateBookingsInStorage(updatedBookings);
  };
  
  const requestTeacherChange = (bookingId, reason) => {
    const updatedBookings = bookings.map(b => (b.id === bookingId) ? { ...b, teacherChangeRequest: { reason, status: 'pending' } } : b);
    createNotification('admin', `طلب تغيير معلم من الطالب ${bookings.find(b=>b.id===bookingId)?.studentName}.`, 'warning', '/admin/bookings');
    updateBookingsInStorage(updatedBookings);
  };
  
  const handleTeacherChangeRequest = (bookingId, approved, newTeacherEmail) => {
    const newTeacher = getUserById(newTeacherEmail);
    const updatedBookings = handleTeacherChangeRequestState(bookings, bookingId, approved, newTeacher, createNotification);
    updateBookingsInStorage(updatedBookings);
  };

  const requestNewLink = (bookingId, reason) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking) return;

    const studentName = booking.studentName;
    const teacherName = booking.teacherName;
    const requesterName = reason.includes("الطالب") ? studentName : teacherName;
    
    const updatedBookings = bookings.map(b => (b.id === bookingId) ? { ...b, linkProblemRequest: { reason, status: 'pending', requestedAt: new Date().toISOString() } } : b);
    
    createNotification('admin', `تنبيه: أبلغ ${requesterName} عن مشكلة في رابط الحصة مع ${reason.includes("الطالب") ? teacherName : studentName }.`, 'error', '/admin/bookings');
    updateBookingsInStorage(updatedBookings);
  };

  const handleNewLinkRequest = async (bookingId, approved) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking) return;
    const updatedBooking = await handleNewLinkRequestAction(booking, approved, toast, createNotification, settings.zoom);
    const updatedBookings = bookings.map(b => b.id === bookingId ? updatedBooking : b);
    updateBookingsInStorage(updatedBookings);
  };
  
  const markSessionAttendance = (bookingId, sessionId, attendance, duration) => {
    markSessionAsCompleted(bookingId, sessionId, duration, attendance);
  };

  const trackSessionJoin = async (bookingId, sessionId, userType) => {
    console.log(`User ${userType} joining session ${sessionId}. This is now primarily handled by webhooks.`);
  };

  const trackSessionLeave = async (bookingId, sessionId, userType) => {
    console.log(`User ${userType} leaving session ${sessionId}. This is now primarily handled by webhooks.`);
  };
  
  const getAvailableSlots = (teacherEmail, date) => {
      return getAvailableSlotsState(availability, bookings, teacherEmail, date);
  };
  
  const updateBooking = (bookingId, updatedData) => {
    const updatedBookings = updateBookingState(bookings, bookingId, updatedData, createNotification);
    updateBookingsInStorage(updatedBookings);
  };

  const value = {
    availability, updateTeacherAvailability, bookings, sessionLogs, createBookingRequest, approveBooking,
    rejectBooking, deleteBooking, requestSubscriptionDeletion, handleSubscriptionDeletion,
    updateBooking, requestPostponement, handlePostponementRequest, requestTeacherChange,
    handleTeacherChangeRequest, getAvailableSlots, markSessionAttendance, requestNewLink,
    handleNewLinkRequest, trackSessionJoin, trackSessionLeave
  };

  return <BookingContext.Provider value={value}>{children}</BookingContext.Provider>;
};