const axios = require('axios');

exports.handler = async (event) => {
  if (event.httpMethod !== 'POST') {
    return { statusCode: 405, body: 'Method Not Allowed' };
  }

  try {
    const { accountId, clientId, clientSecret, bookingDetails } = JSON.parse(event.body);

    if (!accountId || !clientId || !clientSecret || !bookingDetails) {
      return {
        statusCode: 400,
        body: JSON.stringify({ error: 'Missing required parameters.' }),
      };
    }

    const credentials = `${clientId}:${clientSecret}`;
    const encodedCredentials = Buffer.from(credentials).toString('base64');
    
    const tokenResponse = await axios.post(
      `https://zoom.us/oauth/token?grant_type=account_credentials&account_id=${accountId}`,
      {},
      {
        headers: {
          'Authorization': `Basic ${encodedCredentials}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    const accessToken = tokenResponse.data.access_token;

    const meetingConfig = {
      topic: bookingDetails.topic || "Gulf Academy Session",
      type: 2,
      start_time: bookingDetails.startTime,
      timezone: bookingDetails.timezone || "Asia/Riyadh",
      settings: {
        join_before_host: true,
        mute_upon_entry: true,
        participant_video: true,
        host_video: true,
        waiting_room: false,
      },
    };

    const meetingResponse = await axios.post(
      'https://api.zoom.us/v2/users/me/meetings',
      meetingConfig,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*', 
      },
      body: JSON.stringify({
        join_url: meetingResponse.data.join_url,
        start_url: meetingResponse.data.start_url,
      }),
    };

  } catch (error) {
    console.error('Error in Zoom API interaction:', error.response ? error.response.data : error.message);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        error: 'Failed to create Zoom meeting.',
        details: error.response ? error.response.data : 'No response from server.',
      }),
    };
  }
};