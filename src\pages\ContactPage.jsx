import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useSettings } from '@/contexts/SettingsContext';
import { motion } from 'framer-motion';
import { Send, Mail, Phone, MapPin } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

const ContactPage = () => {
  const { toast } = useToast();
  const { addInquiry } = useSettings();
  const [formData, setFormData] = useState({ name: '', email: '', subject: '', message: '' });

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData(prev => ({ ...prev, [id]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.name || !formData.email || !formData.subject || !formData.message) {
      toast({ title: 'بيانات غير مكتملة', description: 'يرجى ملء جميع الحقول.', variant: 'destructive' });
      return;
    }
    const newInquiry = {
      id: uuidv4(),
      ...formData,
      status: 'جديد',
      date: new Date().toISOString(),
      type: 'contact',
    };
    addInquiry(newInquiry);
    toast({ title: '✅ تم إرسال رسالتك بنجاح!', description: 'سنتواصل معك في أقرب وقت ممكن.' });
    setFormData({ name: '', email: '', subject: '', message: '' });
  };

  return (
    <>
      <Helmet>
        <title>اتصل بنا - Gulf Academy</title>
        <meta name="description" content="تواصل مع فريق أكاديمية الخليج. نحن هنا لمساعدتك." />
      </Helmet>
      <div className="flex flex-col min-h-screen bg-background text-foreground">
        <Header />
        <main className="flex-grow">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto px-4 py-16"
          >
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">تواصل معنا</h1>
                <p className="text-lg text-white/70">نحن هنا للإجابة على جميع استفساراتك.</p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <div className="glass-effect rounded-xl p-8">
                  <h2 className="text-3xl font-bold mb-6 text-white">أرسل لنا رسالة</h2>
                  <form onSubmit={handleSubmit} className="space-y-6 text-right">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name" className="text-white/80 mb-2 block">الاسم الكامل</Label>
                        <Input id="name" value={formData.name} onChange={handleChange} placeholder="اسمك" className="bg-white/10 border-border text-white" />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-white/80 mb-2 block">البريد الإلكتروني</Label>
                        <Input id="email" type="email" value={formData.email} onChange={handleChange} placeholder="بريدك الإلكتروني" className="bg-white/10 border-border text-white" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="subject" className="text-white/80 mb-2 block">الموضوع</Label>
                      <Input id="subject" value={formData.subject} onChange={handleChange} placeholder="موضوع رسالتك" className="bg-white/10 border-border text-white" />
                    </div>
                    <div>
                      <Label htmlFor="message" className="text-white/80 mb-2 block">رسالتك</Label>
                      <Textarea id="message" value={formData.message} onChange={handleChange} placeholder="اكتب رسالتك هنا..." rows={5} className="bg-white/10 border-border text-white" />
                    </div>
                    <div className="text-center">
                      <Button type="submit" className="bg-primary hover:bg-primary/90 w-full text-lg">
                        إرسال الرسالة <Send className="h-5 w-5 mr-2" />
                      </Button>
                    </div>
                  </form>
                </div>

                <div className="flex flex-col justify-center space-y-6">
                  <div className="glass-effect rounded-xl p-6 flex items-center gap-4">
                    <div className="bg-primary/20 p-3 rounded-full">
                      <Mail className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white">البريد الإلكتروني</h3>
                      <p className="text-white/70"><EMAIL></p>
                    </div>
                  </div>
                  <div className="glass-effect rounded-xl p-6 flex items-center gap-4">
                    <div className="bg-primary/20 p-3 rounded-full">
                      <Phone className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white">الهاتف (محاكاة)</h3>
                      <p className="text-white/70" dir="ltr">+966 11 123 4567</p>
                    </div>
                  </div>
                   <div className="glass-effect rounded-xl p-6 flex items-center gap-4">
                    <div className="bg-primary/20 p-3 rounded-full">
                      <MapPin className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white">الموقع (محاكاة)</h3>
                      <p className="text-white/70">الرياض، المملكة العربية السعودية</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default ContactPage;