// MySQL Database Connection and Management
// اتصال وإدارة قاعدة بيانات MySQL

import mysql from 'mysql2/promise';

// إعدادات قاعدة البيانات
const dbConfig = {
  host: '*************',
  port: 3306,
  user: 'u480169857_new',
  password: 'uD8PbKLgW4^Y',
  database: 'u480169857_new',
  charset: 'utf8mb4',
  timezone: '+03:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  connectionLimit: 10
};

// متغير للاتصال
let connection = null;

// إنشاء اتصال قاعدة البيانات
export const createConnection = async () => {
  try {
    if (!connection) {
      connection = await mysql.createConnection(dbConfig);
      console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
    }
    return connection;
  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    throw error;
  }
};

// إغلاق الاتصال
export const closeConnection = async () => {
  try {
    if (connection) {
      await connection.end();
      connection = null;
      console.log('✅ تم إغلاق اتصال قاعدة البيانات');
    }
  } catch (error) {
    console.error('❌ خطأ في إغلاق اتصال قاعدة البيانات:', error);
  }
};

// تنفيذ استعلام
export const executeQuery = async (query, params = []) => {
  try {
    const conn = await createConnection();
    const [results] = await conn.execute(query, params);
    return results;
  } catch (error) {
    console.error('❌ خطأ في تنفيذ الاستعلام:', error);
    throw error;
  }
};

// سكريبت إنشاء الجداول
export const createTables = async () => {
  const conn = await createConnection();
  
  try {
    console.log('🔄 بدء إنشاء الجداول...');

    // جدول المستخدمين
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        gender ENUM('male', 'female'),
        birth_date DATE,
        user_type ENUM('admin', 'teacher', 'student') DEFAULT 'student',
        status ENUM('نشط', 'غير نشط', 'محظور') DEFAULT 'نشط',
        device_registered BOOLEAN DEFAULT FALSE,
        device_id VARCHAR(32),
        registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_user_type (user_type),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ تم إنشاء جدول المستخدمين');

    // جدول بيانات الأجهزة
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS user_devices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_email VARCHAR(255) NOT NULL,
        device_id VARCHAR(32) NOT NULL,
        device_fingerprint VARCHAR(64) NOT NULL,
        browser_name VARCHAR(50),
        browser_version VARCHAR(20),
        browser_user_agent TEXT,
        system_platform VARCHAR(100),
        system_language VARCHAR(10),
        system_languages TEXT,
        system_timezone VARCHAR(50),
        system_timezone_offset INT,
        screen_width INT,
        screen_height INT,
        screen_color_depth INT,
        screen_pixel_ratio DECIMAL(3,2),
        hardware_cores INT,
        hardware_memory INT,
        hardware_max_touch_points INT,
        registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        UNIQUE KEY unique_user_device (user_email, device_id),
        INDEX idx_user_email (user_email),
        INDEX idx_device_id (device_id),
        INDEX idx_fingerprint (device_fingerprint),
        INDEX idx_last_seen (last_seen)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ تم إنشاء جدول بيانات الأجهزة');

    // جدول محاولات تسجيل الدخول
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS login_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_email VARCHAR(255),
        device_fingerprint VARCHAR(64),
        attempt_type ENUM('success', 'failed_password', 'failed_device', 'blocked') NOT NULL,
        user_agent TEXT,
        attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        success BOOLEAN DEFAULT FALSE,
        failure_reason TEXT,
        INDEX idx_user_email (user_email),
        INDEX idx_attempt_time (attempt_time),
        INDEX idx_attempt_type (attempt_type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ تم إنشاء جدول محاولات تسجيل الدخول');

    // جدول إعدادات النظام
    await conn.execute(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
        description TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by VARCHAR(255),
        INDEX idx_setting_key (setting_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ تم إنشاء جدول إعدادات النظام');

    // إدراج الإعدادات الافتراضية
    await conn.execute(`
      INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
      ('device_security_enabled', 'true', 'boolean', 'تفعيل نظام أمان الأجهزة'),
      ('max_devices_per_user', '1', 'number', 'الحد الأقصى للأجهزة المسموحة لكل مستخدم'),
      ('device_verification_required', 'true', 'boolean', 'إجبارية التحقق من الجهاز عند تسجيل الدخول'),
      ('failed_login_attempts_limit', '5', 'number', 'عدد محاولات تسجيل الدخول الفاشلة المسموحة'),
      ('device_session_timeout', '24', 'number', 'مدة انتهاء جلسة الجهاز بالساعات')
    `);
    console.log('✅ تم إدراج الإعدادات الافتراضية');

    // إدراج المستخدمين الافتراضيين
    await conn.execute(`
      INSERT IGNORE INTO users (id, name, email, password, user_type, status, device_registered) VALUES
      ('<EMAIL>', 'Admin', '<EMAIL>', 'Gulf2025', 'admin', 'نشط', FALSE),
      ('<EMAIL>', 'أ. محمد أحمد', '<EMAIL>', 'password', 'teacher', 'نشط', FALSE),
      ('<EMAIL>', 'أ. سارة علي', '<EMAIL>', 'password', 'teacher', 'نشط', FALSE),
      ('<EMAIL>', 'أحمد الغامدي', '<EMAIL>', 'password', 'student', 'نشط', FALSE),
      ('<EMAIL>', 'فاطمة الزهراني', '<EMAIL>', 'password', 'student', 'نشط', FALSE)
    `);
    console.log('✅ تم إدراج المستخدمين الافتراضيين');

    console.log('🎉 تم إنشاء جميع الجداول بنجاح!');
    return true;
  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
    throw error;
  }
};

// فحص الاتصال
export const testConnection = async () => {
  try {
    const conn = await createConnection();
    await conn.execute('SELECT 1');
    console.log('✅ اختبار الاتصال نجح');
    return true;
  } catch (error) {
    console.error('❌ فشل اختبار الاتصال:', error);
    return false;
  }
};

// دوال CRUD للمستخدمين
export const userQueries = {
  // إنشاء مستخدم جديد
  async create(userData) {
    const query = `
      INSERT INTO users (id, name, email, password, phone, gender, birth_date, user_type, status, device_registered, device_id)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const params = [
      userData.email,
      userData.name,
      userData.email,
      userData.password,
      userData.phone,
      userData.gender,
      userData.birthDate,
      userData.userType || 'student',
      userData.status || 'نشط',
      userData.deviceRegistered || false,
      userData.deviceId || null
    ];
    return await executeQuery(query, params);
  },

  // جلب مستخدم بالبريد الإلكتروني
  async getByEmail(email) {
    const query = 'SELECT * FROM users WHERE email = ?';
    const results = await executeQuery(query, [email]);
    return results[0] || null;
  },

  // تحديث مستخدم
  async update(email, userData) {
    const fields = [];
    const params = [];
    
    Object.keys(userData).forEach(key => {
      if (userData[key] !== undefined) {
        fields.push(`${key} = ?`);
        params.push(userData[key]);
      }
    });
    
    if (fields.length === 0) return false;
    
    params.push(email);
    const query = `UPDATE users SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE email = ?`;
    
    const result = await executeQuery(query, params);
    return result.affectedRows > 0;
  },

  // جلب جميع المستخدمين
  async getAll() {
    const query = 'SELECT * FROM users ORDER BY registration_date DESC';
    return await executeQuery(query);
  }
};

// دوال CRUD للأجهزة
export const deviceQueries = {
  // تسجيل جهاز جديد
  async register(deviceData) {
    const query = `
      INSERT INTO user_devices (
        user_email, device_id, device_fingerprint, browser_name, browser_version,
        browser_user_agent, system_platform, system_language, system_languages,
        system_timezone, system_timezone_offset, screen_width, screen_height,
        screen_color_depth, screen_pixel_ratio, hardware_cores, hardware_memory,
        hardware_max_touch_points
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        device_fingerprint = VALUES(device_fingerprint),
        browser_name = VALUES(browser_name),
        browser_version = VALUES(browser_version),
        browser_user_agent = VALUES(browser_user_agent),
        last_seen = CURRENT_TIMESTAMP
    `;
    
    const params = [
      deviceData.user_email,
      deviceData.device_id,
      deviceData.device_fingerprint,
      deviceData.browser_name,
      deviceData.browser_version,
      deviceData.browser_user_agent,
      deviceData.system_platform,
      deviceData.system_language,
      deviceData.system_languages,
      deviceData.system_timezone,
      deviceData.system_timezone_offset,
      deviceData.screen_width,
      deviceData.screen_height,
      deviceData.screen_color_depth,
      deviceData.screen_pixel_ratio,
      deviceData.hardware_cores,
      deviceData.hardware_memory,
      deviceData.hardware_max_touch_points
    ];
    
    return await executeQuery(query, params);
  },

  // التحقق من الجهاز
  async verify(userEmail, deviceFingerprint) {
    const query = `
      SELECT * FROM user_devices 
      WHERE user_email = ? AND device_fingerprint = ? AND is_active = TRUE
    `;
    const results = await executeQuery(query, [userEmail, deviceFingerprint]);
    return results.length > 0;
  },

  // جلب أجهزة المستخدم
  async getUserDevices(userEmail) {
    const query = `
      SELECT * FROM user_devices 
      WHERE user_email = ? AND is_active = TRUE
      ORDER BY last_seen DESC
    `;
    return await executeQuery(query, [userEmail]);
  },

  // جلب جميع الأجهزة
  async getAll() {
    const query = `
      SELECT ud.*, u.name as user_name 
      FROM user_devices ud
      LEFT JOIN users u ON ud.user_email = u.email
      WHERE ud.is_active = TRUE AND u.user_type = 'student'
      ORDER BY ud.last_seen DESC
    `;
    return await executeQuery(query);
  },

  // تحديث آخر ظهور
  async updateLastSeen(userEmail, deviceId) {
    const query = `
      UPDATE user_devices 
      SET last_seen = CURRENT_TIMESTAMP 
      WHERE user_email = ? AND device_id = ?
    `;
    const result = await executeQuery(query, [userEmail, deviceId]);
    return result.affectedRows > 0;
  },

  // حذف جهاز
  async delete(deviceId) {
    const query = 'UPDATE user_devices SET is_active = FALSE WHERE device_id = ?';
    const result = await executeQuery(query, [deviceId]);
    return result.affectedRows > 0;
  }
};

export default { createConnection, closeConnection, executeQuery, createTables, testConnection, userQueries, deviceQueries };
