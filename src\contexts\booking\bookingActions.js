import { supabase } from '@/lib/customSupabaseClient';
import { v4 as uuidv4 } from 'uuid';

const createZoomMeetingLink = async (booking) => {
    const { data, error } = await supabase.functions.invoke('create-zoom-meeting', {
        body: { bookingDetails: booking },
    });
    if (error) {
        throw new Error(`Zoom API Error: ${error.message}`);
    }
    if (!data.success) {
        throw new Error(`Zoom API Error: ${data.error || 'Unknown error occurred'}`);
    }
    return data.join_url;
};

export const approveBookingAction = async (booking, createNotification, getUserById, toast, zoomSettings) => {
    let meetingLink = null;
    try {
        if (zoomSettings?.connected && zoomSettings?.accountId) {
            meetingLink = await createZoomMeetingLink(booking);
            toast({ title: '✅ تم إنشاء رابط اجتماع Zoom بنجاح!' });
        } else {
            toast({ title: '⚠️ تنبيه', description: 'لم يتم ربط خدمة اجتماعات Zoom. يجب إنشاء الرابط يدوياً.', variant: 'destructive' });
        }
    } catch (error) {
        console.error("Failed to create meeting link:", error);
        toast({ title: '❌ فشل إنشاء رابط اجتماع Zoom', description: error.message, variant: 'destructive' });
    }

    const approvedBooking = {
        ...booking,
        status: 'approved',
        zoomLink: meetingLink,
    };

    const teacher = getUserById(booking.teacherEmail);
    const student = getUserById(booking.studentEmail);

    createNotification(booking.studentEmail, `تمت الموافقة على حجزك مع ${teacher?.name || 'المعلم'}.`, 'success', '/student/upcoming-sessions');
    createNotification(booking.teacherEmail, `تم تأكيد حجز جديد مع ${student?.name || 'الطالب'}.`, 'success', '/teacher/schedule');

    return approvedBooking;
};

export const handleNewLinkRequestAction = async (booking, approved, toast, createNotification, zoomSettings) => {
    if (!approved) {
        createNotification(booking.studentEmail, `تم رفض طلبك لرابط جديد.`, 'error', '/student/upcoming-sessions');
        createNotification(booking.teacherEmail, `تم رفض طلب رابط جديد لحصتك مع ${booking.studentName}.`, 'error', '/teacher/schedule');
        return { ...booking, linkProblemRequest: { ...booking.linkProblemRequest, status: 'rejected' } };
    }

    let newMeetingLink = null;
    try {
         if (zoomSettings?.connected && zoomSettings?.accountId) {
            newMeetingLink = await createZoomMeetingLink(booking);
            toast({ title: '✅ تم إنشاء رابط اجتماع Zoom جديد بنجاح!' });
        } else {
            toast({ title: '⚠️ تنبيه', description: 'لم يتم ربط خدمة اجتماعات Zoom. يجب إنشاء الرابط يدوياً.', variant: 'destructive' });
        }
    } catch (error) {
        console.error("Failed to create new meeting link:", error);
        toast({ title: '❌ فشل إنشاء رابط اجتماع Zoom جديد', description: error.message, variant: 'destructive' });
    }

    createNotification(booking.studentEmail, `تم توفير رابط جديد لحصتك.`, 'success', '/student/upcoming-sessions');
    createNotification(booking.teacherEmail, `تم توفير رابط جديد لحصتك مع ${booking.studentName}.`, 'success', '/teacher/schedule');

    return {
        ...booking,
        zoomLink: newMeetingLink,
        linkProblemRequest: { ...booking.linkProblemRequest, status: 'approved' }
    };
};