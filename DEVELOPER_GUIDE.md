# دليل المطور - نظام التحكم في الأجهزة

## نظرة عامة للمطورين

تم تطوير نظام التحكم في الأجهزة (Device Controller) لمنصة Gulf Academy باستخدام React.js مع دعم MySQL. النظام يوفر طبقة أمان إضافية من خلال ربط الحسابات بأجهزة محددة.

## البنية التقنية

### المكونات الرئيسية

```
src/
├── components/
│   ├── DeviceController.jsx          # واجهة إدارة الأجهزة
│   ├── DeviceControllerSettings.jsx  # إعدادات النظام
│   └── DeviceStats.jsx              # إحصائيات الأجهزة
├── lib/
│   ├── deviceFingerprint.js         # مكتبة بصمة الجهاز
│   └── mysql.js                     # إعدادات قاعدة البيانات
└── contexts/
    └── AuthContext.jsx              # سياق المصادقة المحدث
```

### تدفق البيانات

1. **التسجيل**: جمع بصمة الجهاز → تشفيرها → حفظها
2. **تسجيل الدخول**: جمع بصمة حالية → مقارنتها → السماح/المنع
3. **المراقبة**: تحديث آخر نشاط → تتبع الحالة

## API المطور

### دوال بصمة الجهاز

```javascript
// جمع بصمة الجهاز
const fingerprint = await getDeviceFingerprint();

// تشفير البصمة
const hash = await hashString(fingerprint);

// جمع معلومات مفصلة
const deviceInfo = await getDeviceInfo();

// التحقق من الجهاز
const isValid = await verifyDevice(storedFingerprint);
```

### دوال إدارة البيانات

```javascript
// حفظ معلومات الجهاز
await saveDeviceInfo(userEmail);

// استرجاع معلومات محفوظة
const deviceInfo = getStoredDeviceInfo(userEmail);

// تحديث آخر ظهور
updateDeviceLastSeen(userEmail);
```

### دوال قاعدة البيانات (MySQL)

```javascript
const deviceController = new MySQLDeviceController(connection);

// حفظ في قاعدة البيانات
await deviceController.saveDeviceInfo(userEmail, deviceInfo);

// التحقق من الجهاز
const isValid = await deviceController.verifyDevice(userEmail, fingerprint);

// جلب أجهزة المستخدم
const devices = await deviceController.getUserDevices(userEmail);
```

## إعداد البيئة التطويرية

### 1. متطلبات النظام
```bash
Node.js >= 16.0.0
npm >= 8.0.0
MySQL >= 8.0 (اختياري)
```

### 2. تثبيت التبعيات
```bash
npm install mysql2
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
# تحرير الملف وإضافة إعدادات MySQL
```

### 4. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE gulf_academy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تشغيل سكريبت الجداول
-- استخدم CREATE_TABLES_SQL من src/lib/mysql.js
```

## التخصيص والتطوير

### إضافة بيانات جديدة للبصمة

```javascript
// في src/lib/deviceFingerprint.js
export const getDeviceFingerprint = () => {
  return new Promise((resolve) => {
    // إضافة بيانات جديدة هنا
    const customData = getCustomDeviceData();
    
    const deviceData = [
      // البيانات الموجودة...
      customData, // البيانات الجديدة
    ].join('~~~');
    
    resolve(deviceData);
  });
};
```

### إضافة إعدادات جديدة

```javascript
// في src/components/DeviceControllerSettings.jsx
const [settings, setSettings] = useState({
  // الإعدادات الموجودة...
  newSetting: defaultValue, // إعداد جديد
});
```

### إضافة إحصائيات جديدة

```javascript
// في src/components/DeviceStats.jsx
const calculateStats = () => {
  // حساب إحصائية جديدة
  const newStat = calculateNewStatistic(devices);
  
  setStats(prev => ({
    ...prev,
    newStat
  }));
};
```

## اختبار النظام

### اختبار بصمة الجهاز
```javascript
// اختبار جمع البصمة
const fingerprint = await getDeviceFingerprint();
console.log('Device fingerprint:', fingerprint);

// اختبار التشفير
const hash = await hashString(fingerprint);
console.log('Fingerprint hash:', hash);
```

### اختبار التحقق
```javascript
// محاكاة تسجيل جديد
await saveDeviceInfo('<EMAIL>');

// محاكاة تسجيل دخول
const isValid = await verifyDevice(storedFingerprint);
console.log('Device verification:', isValid);
```

### اختبار قاعدة البيانات
```javascript
// اختبار الاتصال
const connection = await mysql.createConnection(MYSQL_CONFIG);
console.log('Database connected successfully');

// اختبار العمليات
const deviceController = new MySQLDeviceController(connection);
await deviceController.saveDeviceInfo(userEmail, deviceInfo);
```

## الأمان والأداء

### أفضل الممارسات

1. **تشفير البيانات**: استخدم SHA-256 للبصمات
2. **التحقق من الصحة**: تحقق من صحة البيانات قبل الحفظ
3. **معالجة الأخطاء**: استخدم try-catch في جميع العمليات
4. **التسجيل**: سجل الأحداث المهمة للمراجعة

### تحسين الأداء

```javascript
// استخدام debouncing لتحديث آخر ظهور
const debouncedUpdate = debounce(updateDeviceLastSeen, 5000);

// تخزين مؤقت للبصمات
const fingerprintCache = new Map();

// تحسين استعلامات قاعدة البيانات
const optimizedQuery = `
  SELECT * FROM user_devices 
  WHERE user_email = ? AND is_active = TRUE
  LIMIT 1
`;
```

## استكشاف الأخطاء

### أخطاء شائعة وحلولها

1. **خطأ في جمع البصمة**
```javascript
try {
  const fingerprint = await getDeviceFingerprint();
} catch (error) {
  console.error('Fingerprint error:', error);
  // استخدام بصمة احتياطية
  const fallbackFingerprint = getFallbackFingerprint();
}
```

2. **خطأ في قاعدة البيانات**
```javascript
try {
  await deviceController.saveDeviceInfo(userEmail, deviceInfo);
} catch (error) {
  console.error('Database error:', error);
  // العودة إلى localStorage
  localStorage.setItem(`device_${userEmail}`, JSON.stringify(deviceInfo));
}
```

3. **خطأ في التحقق**
```javascript
const verifyWithFallback = async (userEmail, currentFingerprint) => {
  try {
    return await verifyDevice(userEmail, currentFingerprint);
  } catch (error) {
    console.error('Verification error:', error);
    // السماح بالدخول في حالة الخطأ (حسب السياسة)
    return true;
  }
};
```

## التوثيق والصيانة

### تسجيل الأحداث
```javascript
const logDeviceEvent = (event, userEmail, details) => {
  console.log(`[Device Controller] ${event}:`, {
    userEmail,
    timestamp: new Date().toISOString(),
    details
  });
};
```

### مراقبة الأداء
```javascript
const performanceMonitor = {
  startTime: Date.now(),
  
  measure(operation) {
    const duration = Date.now() - this.startTime;
    console.log(`${operation} took ${duration}ms`);
  }
};
```

### نسخ احتياطية
```javascript
const backupDeviceData = () => {
  const devices = getAllStoredDevices();
  const backup = {
    timestamp: new Date().toISOString(),
    devices
  };
  
  // حفظ النسخة الاحتياطية
  localStorage.setItem('deviceBackup', JSON.stringify(backup));
};
```

## المساهمة في التطوير

### إرشادات المساهمة

1. اتبع معايير الكود الموجودة
2. أضف اختبارات للميزات الجديدة
3. وثق التغييرات في CHANGELOG.md
4. استخدم commit messages واضحة

### هيكل Commit Messages
```
feat(device): إضافة ميزة جديدة
fix(auth): إصلاح مشكلة في التحقق
docs(readme): تحديث التوثيق
test(device): إضافة اختبارات جديدة
```

## الدعم والمساعدة

للحصول على المساعدة في التطوير:
- مراجعة التوثيق في `/docs`
- فحص الأمثلة في `/examples`
- التواصل مع فريق التطوير

---

**ملاحظة للمطورين**: هذا النظام قيد التطوير المستمر. يُرجى مراجعة التحديثات بانتظام.
