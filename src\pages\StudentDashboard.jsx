import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet';
import { useAuth } from '@/contexts/AuthContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import InternalChat from '@/components/InternalChat';
import BookingModal from '@/pages/student/BookingModal';
import StudentSidebar from '@/pages/student/StudentSidebar';
import DashboardContent from '@/pages/student/DashboardContent';
import UpcomingSessionsContent from '@/pages/student/UpcomingSessionsContent';
import LibraryContent from '@/pages/student/LibraryContent';
import ExamsContent from '@/pages/student/ExamsContent';
import ChatContent from '@/pages/student/ChatContent';
import ProfileContent from '@/pages/student/ProfileContent';
import { useSettings } from '@/contexts/SettingsContext';
import { Button } from '@/components/ui/button';
import DeviceController from '@/components/DeviceController';

const StudentDashboard = () => {
  const { user: currentUser } = useAuth();
  const { settings } = useSettings();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [viewerUrl, setViewerUrl] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatRecipient, setChatRecipient] = useState(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  useEffect(() => {
    const hasAcceptedTerms = localStorage.getItem('gulfAcademyAcceptedTerms');
    if (currentUser && !hasAcceptedTerms) {
      setShowTermsModal(true);
    }
  }, [currentUser]);

  const handleAcceptTerms = () => {
    localStorage.setItem('gulfAcademyAcceptedTerms', 'true');
    setShowTermsModal(false);
  };

  const openChat = (recipient) => {
    setChatRecipient(recipient);
    setIsChatOpen(true);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardContent setActiveTab={setActiveTab} setIsBookingModalOpen={setIsBookingModalOpen} />;
      case 'upcoming-sessions':
        return <UpcomingSessionsContent onBookNew={() => setIsBookingModalOpen(true)} />;
      case 'library':
        return <LibraryContent setViewerUrl={setViewerUrl} setIsViewerOpen={setIsViewerOpen} />;
      case 'exams':
        return <ExamsContent />;
      case 'chat':
        return <ChatContent openChat={openChat} />;
      case 'device-info':
        return <DeviceController userEmail={currentUser.email} isAdmin={false} userType={currentUser.userType} />;
      case 'profile':
        return <ProfileContent />;
      default:
        return <DashboardContent setActiveTab={setActiveTab} setIsBookingModalOpen={setIsBookingModalOpen} />;
    }
  };

  return (
    <>
      <Helmet>
        <title>لوحة تحكم الطالب - Gulf Academy</title>
        <meta name="description" content="لوحة تحكم الطالب في منصة Gulf Academy - إدارة حصصك ومكتبتك الشخصية" />
      </Helmet>
      <div className="min-h-screen gradient-bg">
        <div className="flex">
          <StudentSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
          <main className="flex-1 p-8 overflow-y-auto h-screen">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">مرحباً، {currentUser.name}</h1>
              <p className="text-muted-foreground">إدارة حصصك ومتابعة تقدمك الأكاديمي</p>
            </div>
            <motion.div key={activeTab} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
              {renderContent()}
            </motion.div>
          </main>
        </div>
      </div>
      <Dialog open={isBookingModalOpen} onOpenChange={setIsBookingModalOpen}>
        <BookingModal setIsOpen={setIsBookingModalOpen} />
      </Dialog>
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-5xl h-[90vh] p-0 flex flex-col">
           <DialogHeader className="p-4 border-b border-white/10">
             <DialogTitle>عرض الملف</DialogTitle>
           </DialogHeader>
           <div className="flex-1">
            <iframe src={viewerUrl} className="w-full h-full border-0" allow="fullscreen"></iframe>
           </div>
        </DialogContent>
      </Dialog>
      <Dialog open={isChatOpen} onOpenChange={setIsChatOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-2xl h-[70vh] flex flex-col p-0">
          <DialogHeader className="p-4 border-b border-white/10">
            <DialogTitle>محادثة مع {chatRecipient?.name}</DialogTitle>
          </DialogHeader>
          {chatRecipient && <InternalChat recipient={chatRecipient} />}
        </DialogContent>
      </Dialog>
      <Dialog open={showTermsModal}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>شروط الاستخدام والخصوصية</DialogTitle>
            <DialogDescription>
              يرجى قراءة والموافقة على شروط الاستخدام وسياسة الخصوصية للمتابعة.
            </DialogDescription>
          </DialogHeader>
          <div className="prose prose-invert max-h-[60vh] overflow-y-auto p-4 bg-white/5 rounded-md" dangerouslySetInnerHTML={{ __html: settings.termsAndPrivacy || '<p>لم يتم تحديد الشروط بعد.</p>' }}>
          </div>
          <div className="flex justify-end">
            <Button onClick={handleAcceptTerms} className="bg-primary hover:bg-primary/90">أوافق على الشروط</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default StudentDashboard;