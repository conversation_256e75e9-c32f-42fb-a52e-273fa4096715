import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { LogIn, LogOut, LayoutDashboard } from 'lucide-react';

const Header = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleDashboardRedirect = () => {
    if (user.userType === 'student') navigate('/student');
    if (user.userType === 'teacher') navigate('/teacher');
    if (user.userType === 'admin') navigate('/admin');
  };

  return (
    <header className="bg-background/80 backdrop-blur-sm sticky top-0 z-40 border-b">
      <div className="container mx-auto px-4 h-20 flex items-center justify-between">
        <Link to="/" className="flex items-center gap-2">
          <img alt="Gulf Academy Logo" className="h-16 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
        </Link>
        <nav className="hidden md:flex items-center gap-6 text-lg">
          <Link to="/" className="hover:text-primary transition-colors">الرئيسية</Link>
          <Link to="/about" className="hover:text-primary transition-colors">من نحن</Link>
          <Link to="/faq" className="hover:text-primary transition-colors">الأسئلة الشائعة</Link>
        </nav>
        <div className="flex items-center gap-4">
          {user ? (
            <>
              <Button variant="outline" onClick={handleDashboardRedirect}>
                <LayoutDashboard className="mr-2 h-4 w-4" />
                لوحة التحكم
              </Button>
              <Button variant="ghost" onClick={logout}>
                <LogOut className="mr-2 h-4 w-4" />
                تسجيل الخروج
              </Button>
            </>
          ) : (
            <>
              <Button asChild variant="ghost" className="hidden md:inline-flex">
                <Link to="/login">
                  تسجيل الدخول
                  <LogIn className="mr-2 h-4 w-4" />
                </Link>
              </Button>
              <Button asChild className="bg-primary text-primary-foreground hover:bg-primary/90">
                <Link to="/register">إنشاء حساب</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;