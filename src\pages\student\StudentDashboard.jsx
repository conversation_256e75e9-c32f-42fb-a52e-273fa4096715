import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import InternalChat from '@/components/InternalChat';
import BookingModal from '@/pages/student/BookingModal';
import StudentSidebar from '@/pages/student/StudentSidebar';
import DashboardContent from '@/pages/student/DashboardContent';
import UpcomingSessionsContent from '@/pages/student/UpcomingSessionsContent';
import LibraryContent from '@/pages/student/LibraryContent';
import ExamsContent from '@/pages/student/ExamsContent';
import ChatContent from '@/pages/student/ChatContent';
import ProfileContent from '@/pages/student/ProfileContent';
import ReportsContent from '@/pages/student/ReportsContent';
import NotificationsDropdown from '@/components/NotificationsDropdown';
import SessionReminderModal from '@/components/SessionReminderModal';
import { AlertCircle, Lock } from 'lucide-react';
import { parseISO, isAfter } from 'date-fns';
import { useToast } from '@/components/ui/use-toast';
import { useLocation, useNavigate, Link } from 'react-router-dom';
import InboxContent from '@/components/InboxContent';
import ActiveSessionsList from '@/components/ActiveSessionsList';

const ProtectedContent = ({ children }) => {
  const { isAuthenticatedWithCode } = useAuth();
  if (!isAuthenticatedWithCode) {
    return (
      <div className="glass-effect rounded-xl p-8 text-center">
        <Lock className="mx-auto h-16 w-16 text-yellow-400 mb-4" />
        <h3 className="text-2xl font-bold text-white mb-2">الوصول مقيد</h3>
        <p className="text-white/70 mb-6">هذا المحتوى يتطلب تسجيل الدخول باستخدام كود الوصول الخاص بك لضمان الأمان.</p>
        <Link to="/login/access-code">
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
            الدخول باستخدام كود الوصول
          </Button>
        </Link>
      </div>
    );
  }
  return children;
};

const StudentDashboard = () => {
  const { user: currentUser, users, isAuthenticatedWithCode } = useAuth();
  const { bookings, trackSessionJoin, trackSessionLeave } = useBooking();
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();

  const [activeTab, setActiveTab] = useState('dashboard');
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [viewerUrl, setViewerUrl] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatRecipient, setChatRecipient] = useState(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [showSubscriptionWarning, setShowSubscriptionWarning] = useState(false);
  const [showReminder, setShowReminder] = useState(false);
  const [reminderSession, setReminderSession] = useState(null);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const openChatId = params.get('open');
    if (openChatId) {
      if(openChatId === 'inbox') {
        openChat({ id: 'inbox', name: 'الأخبار والعروض', type: 'inbox' });
      } else {
        const contactToOpen = users.find(u => u.email === openChatId) || { id: openChatId, name: 'الإدارة', type: 'admin' };
         if(contactToOpen) {
            openChat(contactToOpen);
         }
      }
      setActiveTab('chat');
      const newParams = new URLSearchParams(location.search);
      newParams.delete('open');
      navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });
    }
  }, [location.search, users, navigate]);

  const nextUpcomingSession = useMemo(() => {
    if (!currentUser) return null;
    
    const studentBookings = bookings.filter(b => b.studentEmail === currentUser.email && b.status === 'approved');
    let closestSession = null;
    let closestBooking = null;

    studentBookings.forEach(booking => {
      (booking.sessions || []).forEach(session => {
        if (session.status === 'scheduled') {
          const sessionTime = parseISO(`${session.date}T${session.time}`);
          if (isAfter(sessionTime, new Date())) {
            if (!closestSession || isAfter(parseISO(`${closestSession.date}T${closestSession.time}`), sessionTime)) {
              closestSession = session;
              closestBooking = booking;
            }
          }
        }
      });
    });

    return closestSession ? { session: closestSession, booking: closestBooking } : null;
  }, [bookings, currentUser]);

  useEffect(() => {
    if (nextUpcomingSession && isAuthenticatedWithCode) {
      setReminderSession(nextUpcomingSession);
      setShowReminder(true);
    } else {
      setShowReminder(false);
      setReminderSession(null);
    }
  }, [nextUpcomingSession, isAuthenticatedWithCode]);


  useEffect(() => {
    if (currentUser) {
      const activeBookings = bookings.filter(b => b.studentEmail === currentUser.email && b.status === 'approved');
      const totalRemainingHours = activeBookings.reduce((acc, booking) => {
        const remainingMinutes = (booking.totalHours * 60) - (booking.completedDuration || 0);
        return acc + (remainingMinutes / 60);
      }, 0);

      const hasShownWarning = sessionStorage.getItem(`subscriptionWarning_${currentUser.email}`);
      if (totalRemainingHours > 0 && totalRemainingHours <= 3 && !hasShownWarning) {
        setShowSubscriptionWarning(true);
        sessionStorage.setItem(`subscriptionWarning_${currentUser.email}`, 'true');
      }
    }
  }, [bookings, currentUser]);

  const openChat = (recipient) => {
    setChatRecipient(recipient);
    setIsChatOpen(true);
  };

  const handleJoinSession = (booking, session) => {
    if(!isAuthenticatedWithCode) {
        toast({ title: "الوصول مقيد", description: "يرجى تسجيل الدخول بكود الوصول للانضمام للحصة.", variant: "destructive" });
        navigate('/login/access-code');
        return;
    }

    if (booking.zoomLink && session) {
        trackSessionJoin(booking.id, session.sessionId, 'student');
        const newWindow = window.open(booking.zoomLink, '_blank', 'noopener,noreferrer');
        
        const checkWindowClosed = setInterval(() => {
            if (!newWindow || newWindow.closed) {
                trackSessionLeave(booking.id, session.sessionId, 'student');
                clearInterval(checkWindowClosed);
            }
        }, 1000);

    } else {
        toast({ title: "خطأ", description: "رابط الحصة غير متوفر بعد أو لا توجد حصة قادمة.", variant: "destructive" });
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardContent setActiveTab={setActiveTab} setIsBookingModalOpen={setIsBookingModalOpen} />;
      case 'upcoming-sessions':
        return <UpcomingSessionsContent onBookNew={() => setIsBookingModalOpen(true)} />;
      case 'active-lessons':
        return (
          <ProtectedContent>
            <div className="glass-effect rounded-xl p-6">
              <h3 className="text-2xl font-bold text-white mb-4">الحصص الجارية الآن</h3>
              <ActiveSessionsList />
            </div>
          </ProtectedContent>
        );
      case 'library':
        return (
          <ProtectedContent>
            <LibraryContent setViewerUrl={setViewerUrl} setIsViewerOpen={setIsViewerOpen} />
          </ProtectedContent>
        );
      case 'exams':
        return <ExamsContent />;
      case 'chat':
        return <ChatContent openChat={openChat} />;
      case 'reports':
        return <ReportsContent />;
      case 'profile':
        return <ProfileContent />;
      default:
        return <DashboardContent setActiveTab={setActiveTab} setIsBookingModalOpen={setIsBookingModalOpen} />;
    }
  };

  return (
    <>
      <Helmet>
        <title>لوحة تحكم الطالب - Gulf Academy</title>
        <meta name="description" content="لوحة تحكم الطالب في منصة Gulf Academy - إدارة حصصك ومكتبتك الشخصية" />
      </Helmet>
      <div className="min-h-screen gradient-bg">
        <div className="flex">
          <StudentSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
          <main className="flex-1 p-8 overflow-y-auto h-screen">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">مرحباً، {currentUser.name}</h1>
                <p className="text-white/70">إدارة حصصك ومتابعة تقدمك الأكاديمي</p>
              </div>
              <NotificationsDropdown />
            </div>
            <motion.div key={activeTab} initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.5 }}>
              {renderContent()}
            </motion.div>
          </main>
        </div>
      </div>
      
      {reminderSession && <SessionReminderModal session={reminderSession.session} booking={reminderSession.booking} userType="student" isOpen={showReminder} onOpenChange={setShowReminder} onJoinSession={handleJoinSession} />}

      <Dialog open={isBookingModalOpen} onOpenChange={setIsBookingModalOpen}>
        <BookingModal setIsOpen={setIsBookingModalOpen} />
      </Dialog>
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-5xl h-[90vh] p-0 flex flex-col">
           <DialogHeader className="p-4 border-b border-white/10">
             <DialogTitle>عرض الملف</DialogTitle>
           </DialogHeader>
           <div className="flex-1">
            <iframe src={viewerUrl} className="w-full h-full border-0" allow="fullscreen"></iframe>
           </div>
        </DialogContent>
      </Dialog>
      <Dialog open={isChatOpen} onOpenChange={setIsChatOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-2xl h-[70vh] flex flex-col p-0">
          <DialogHeader className="p-4 border-b border-white/10">
            <DialogTitle>محادثة مع {chatRecipient?.name}</DialogTitle>
          </DialogHeader>
          {chatRecipient && chatRecipient.type === 'inbox' ? <InboxContent /> : <InternalChat recipient={chatRecipient} />}
        </DialogContent>
      </Dialog>
      <Dialog open={showSubscriptionWarning} onOpenChange={setShowSubscriptionWarning}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="text-yellow-400" />
              تنبيه قرب انتهاء الاشتراك
            </DialogTitle>
            <DialogDescription>
              لقد اقترب رصيد ساعاتك من الانتهاء. يرجى تجديد اشتراكك لضمان عدم انقطاع الدروس.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setShowSubscriptionWarning(false)}>إغلاق</Button>
            <Button className="bg-primary hover:bg-primary/90" onClick={() => { setIsBookingModalOpen(true); setShowSubscriptionWarning(false); }}>تجديد الاشتراك الآن</Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default StudentDashboard;