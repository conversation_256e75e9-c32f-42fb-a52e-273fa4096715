import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, Calendar, BookOpen, FileText, MessageSquare, 
  User, Home, LogOut, BarChart3
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const SidebarLink = ({ icon, label, active, onClick }) => (
  <button
    onClick={onClick}
    className={`w-full flex items-center p-3 rounded-lg transition-colors text-sm ${
      active
        ? 'bg-primary text-primary-foreground'
        : 'text-white/80 hover:bg-white/10'
    }`}
  >
    {icon}
    <span className="mr-4">{label}</span>
  </button>
);

const SidebarCategory = ({ title }) => (
  <p className="text-xs font-semibold text-white/40 uppercase px-3 pt-4 pb-2">{title}</p>
);

const StudentSidebar = ({ activeTab, setActiveTab }) => {
  const { logout } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = () => {
    logout();
    navigate('/');
    toast({ title: 'تم تسجيل الخروج بنجاح' });
  };

  const menuItems = [
    { 
      category: 'عام', 
      items: [
        { id: 'home', label: 'العودة للرئيسية', icon: <Home size={20} />, action: () => navigate('/') },
        { id: 'dashboard', label: 'لوحة التحكم', icon: <LayoutDashboard size={20} />, action: () => setActiveTab('dashboard') },
      ]
    },
    {
      category: 'العمليات الدراسية',
      items: [
        { id: 'upcoming-sessions', label: 'الحصص القادمة', icon: <Calendar size={20} />, action: () => setActiveTab('upcoming-sessions') },
        { id: 'exams', label: 'الاختبارات', icon: <FileText size={20} />, action: () => setActiveTab('exams') },
        { id: 'library', label: 'المكتبة', icon: <BookOpen size={20} />, action: () => setActiveTab('library') },
        { id: 'reports', label: 'التقارير', icon: <BarChart3 size={20} />, action: () => setActiveTab('reports') },
      ]
    },
    {
      category: 'التواصل والحساب',
      items: [
        { id: 'chat', label: 'المحادثات', icon: <MessageSquare size={20} />, action: () => setActiveTab('chat') },
        { id: 'profile', label: 'الملف الشخصي', icon: <User size={20} />, action: () => setActiveTab('profile') },
      ]
    }
  ];

  return (
    <aside className="w-64 sidebar-gradient p-4 flex flex-col h-screen sticky top-0">
      <Link to="/" className="flex flex-col items-center gap-1 mb-6">
         <img alt="Gulf Academy Logo" className="h-20 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
      </Link>
      <nav className="flex-1 space-y-1 overflow-y-auto">
        {menuItems.map(category => (
          <div key={category.category}>
            <SidebarCategory title={category.category} />
            {category.items.map(item => (
              <SidebarLink
                key={item.id}
                icon={item.icon}
                label={item.label}
                active={activeTab === item.id && item.id !== 'home'}
                onClick={item.action}
              />
            ))}
          </div>
        ))}
      </nav>
      <div className="mt-auto pt-4">
        <Button
          onClick={handleLogout}
          variant="destructive"
          className="w-full justify-start text-destructive-foreground bg-destructive/80 hover:bg-destructive text-sm"
        >
          <LogOut className="ml-2 h-4 w-4" />
          تسجيل الخروج
        </Button>
      </div>
    </aside>
  );
};

export default StudentSidebar;