import React from 'react';
import { motion } from 'framer-motion';
import { WhatsAppIcon, TelegramIcon } from '@/components/CustomIcons';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

const FloatingActionButtons = () => {
  const actions = [
    {
      label: "تواصل عبر تليجرام",
      icon: <TelegramIcon className="h-8 w-8 text-white" />,
      href: "https://t.me/gulf_academy25",
      bgColor: "bg-sky-500 hover:bg-sky-600",
      delay: 0.9,
    },
    {
      label: "تواصل عبر واتساب",
      icon: <WhatsAppIcon className="h-8 w-8 text-white" />,
      href: "https://wa.me/97455031360",
      bgColor: "bg-green-500 hover:bg-green-600",
      delay: 0.8,
    },
  ];

  return (
    <div className="fixed bottom-24 left-4 flex flex-col items-center gap-4 z-40">
      <TooltipProvider>
        {actions.map((action, index) => (
          <Tooltip key={index}>
            <TooltipTrigger asChild>
              <motion.a
                href={action.href}
                target="_blank"
                rel="noopener noreferrer"
                initial={{ scale: 0, y: 50 }}
                animate={{ scale: 1, y: 0 }}
                transition={{ type: 'spring', stiffness: 260, damping: 20, delay: action.delay }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className={`w-16 h-16 rounded-full ${action.bgColor} shadow-lg flex items-center justify-center`}
              >
                {action.icon}
              </motion.a>
            </TooltipTrigger>
            <TooltipContent side="right" align="center" className="bg-secondary text-white border-border">
              <p>{action.label}</p>
            </TooltipContent>
          </Tooltip>
        ))}
      </TooltipProvider>
    </div>
  );
};

export default FloatingActionButtons;