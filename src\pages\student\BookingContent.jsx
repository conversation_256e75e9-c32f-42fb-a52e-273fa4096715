import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import BookingModal from './BookingModal';
import { Dialog } from '@/components/ui/dialog';

const BookingContent = () => {
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  return (
    <>
      <div className="space-y-8">
        <div className="glass-effect rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold text-white mb-4">ابدأ رحلتك التعليمية</h3>
          <p className="text-white/70 mb-8">انقر على الزر أدناه لحجز حصصك الدراسية مع أفضل المعلمين.</p>
          <Button onClick={() => setIsBookingModalOpen(true)} size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90">
            <Plus className="h-5 w-5 ml-2" />
            احجز حصة جديدة
          </Button>
        </div>
      </div>
      <Dialog open={isBookingModalOpen} onOpenChange={setIsBookingModalOpen}>
        <BookingModal setIsOpen={setIsBookingModalOpen} />
      </Dialog>
    </>
  );
};

export default BookingContent;