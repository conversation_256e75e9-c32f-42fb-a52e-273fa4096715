{"version": 3, "sources": ["../../qr-scanner/node_modules/jsqr-es6/src/BitMatrix.ts", "../../qr-scanner/node_modules/jsqr-es6/src/binarizer/index.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/decodeData/BitStream.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/decodeData/index.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/reedsolomon/GenericGFPoly.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/reedsolomon/GenericGF.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/reedsolomon/index.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/version.ts", "../../qr-scanner/node_modules/jsqr-es6/src/decoder/decoder.ts", "../../qr-scanner/node_modules/jsqr-es6/src/extractor/index.ts", "../../qr-scanner/node_modules/jsqr-es6/src/locator/index.ts", "../../qr-scanner/node_modules/jsqr-es6/src/index.ts", "../../qr-scanner/src/worker.ts"], "sourcesContent": ["export class BitMatrix {\n  public static createEmpty(width: number, height: number) {\n    return new BitMatrix(new Uint8ClampedArray(width * height), width);\n  }\n\n  public width: number;\n  public height: number;\n  private data: Uint8ClampedArray;\n\n  constructor(data: Uint8ClampedArray, width: number) {\n    this.width = width;\n    this.height = data.length / width;\n    this.data = data;\n  }\n\n  public get(x: number, y: number): boolean {\n    if (x < 0 || x >= this.width || y < 0 || y >= this.height) {\n      return false;\n    }\n    return !!this.data[y * this.width + x];\n  }\n\n  public set(x: number, y: number, v: boolean) {\n    this.data[y * this.width + x] = v ? 1 : 0;\n  }\n\n  public setRegion(left: number, top: number, width: number, height: number, v: boolean) {\n    for (let y = top; y < top + height; y++) {\n      for (let x = left; x < left + width; x++) {\n        this.set(x, y, !!v);\n      }\n    }\n  }\n}\n", "import {BitMatrix} from \"../BitMatrix\";\nimport {GreyscaleWeights} from \"../index\";\n\nconst REGION_SIZE = 8;\nconst MIN_DYNAMIC_RANGE = 24;\n\nfunction numBetween(value: number, min: number, max: number): number {\n  return value < min ? min : value > max ? max : value;\n}\n\n// Like BitMatrix but accepts arbitry Uint8 values\nclass Matrix {\n  private data: Uint8ClampedArray;\n  private width: number;\n  constructor(width: number, height: number, buffer?: Uint8ClampedArray) {\n    this.width = width;\n    const bufferSize = width * height;\n    if (buffer && buffer.length !== bufferSize) {\n      throw new Error(\"Wrong buffer size\");\n    }\n    this.data = buffer || new Uint8ClampedArray(bufferSize);\n  }\n  public get(x: number, y: number) {\n    return this.data[y * this.width + x];\n  }\n  public set(x: number, y: number, value: number) {\n    this.data[y * this.width + x] = value;\n  }\n}\n\nexport function binarize(data: Uint8ClampedArray, width: number, height: number, returnInverted: boolean,\n                         greyscaleWeights: GreyscaleWeights, canOverwriteImage: boolean) {\n  const pixelCount = width * height;\n  if (data.length !== pixelCount * 4) {\n    throw new Error(\"Malformed data passed to binarizer.\");\n  }\n  // assign the greyscale and binary image within the rgba buffer as the rgba image will not be needed after conversion\n  let bufferOffset = 0;\n  // Convert image to greyscale\n  let greyscaleBuffer: Uint8ClampedArray;\n  if (canOverwriteImage) {\n    greyscaleBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n    bufferOffset += pixelCount;\n  }\n  const greyscalePixels = new Matrix(width, height, greyscaleBuffer);\n  if (greyscaleWeights.useIntegerApproximation) {\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const pixelPosition = (y * width + x) * 4;\n        const r = data[pixelPosition];\n        const g = data[pixelPosition + 1];\n        const b = data[pixelPosition + 2];\n        greyscalePixels.set(x, y,\n          // tslint:disable-next-line no-bitwise\n          (greyscaleWeights.red * r + greyscaleWeights.green * g + greyscaleWeights.blue * b + 128) >> 8);\n      }\n    }\n  } else {\n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        const pixelPosition = (y * width + x) * 4;\n        const r = data[pixelPosition];\n        const g = data[pixelPosition + 1];\n        const b = data[pixelPosition + 2];\n        greyscalePixels.set(x, y,\n          greyscaleWeights.red * r + greyscaleWeights.green * g + greyscaleWeights.blue * b);\n      }\n    }\n  }\n  const horizontalRegionCount = Math.ceil(width / REGION_SIZE);\n  const verticalRegionCount = Math.ceil(height / REGION_SIZE);\n  const blackPointsCount = horizontalRegionCount * verticalRegionCount;\n\n  let blackPointsBuffer: Uint8ClampedArray;\n  if (canOverwriteImage) {\n    blackPointsBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, blackPointsCount);\n    bufferOffset += blackPointsCount;\n  }\n  const blackPoints = new Matrix(horizontalRegionCount, verticalRegionCount, blackPointsBuffer);\n  for (let verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n    for (let hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n      let min = Infinity;\n      let max = 0;\n      for (let y = 0; y < REGION_SIZE; y++) {\n        for (let x = 0; x < REGION_SIZE; x++) {\n          const pixelLumosity =\n            greyscalePixels.get(hortizontalRegion * REGION_SIZE + x, verticalRegion * REGION_SIZE + y);\n          min = Math.min(min, pixelLumosity);\n          max = Math.max(max, pixelLumosity);\n        }\n      }\n      // We could also compute the real average of all pixels but following the assumption that the qr code consists\n      // of bright and dark pixels and essentially not much in between, by (min + max)/2 we make the cut really between\n      // those two classes. If using the average over all pixel in a block of mostly bright pixels and few dark pixels,\n      // the avg would tend to the bright side and darker bright pixels could be interpreted as dark.\n      let average = (min + max) / 2;\n      // Small bias towards black by moving the threshold up. We do this, as in the finder patterns white holes tend\n      // to appear which makes them undetectable.\n      const blackBias = 1.11;\n      average = Math.min(255, average * blackBias);\n      if (max - min <= MIN_DYNAMIC_RANGE) {\n        // If variation within the block is low, assume this is a block with only light or only\n        // dark pixels. In that case we do not want to use the average, as it would divide this\n        // low contrast area into black and white pixels, essentially creating data out of noise.\n        //\n        // Default the blackpoint for these blocks to be half the min - effectively white them out\n        average = min / 2;\n\n        if (verticalRegion > 0 && hortizontalRegion > 0) {\n          // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n          // the pixels in this block to the previously calculated black points. This is based on\n          // the fact that dark barcode symbology is always surrounded by some amount of light\n          // background for which reasonable black point estimates were made. The bp estimated at\n          // the boundaries is used for the interior.\n\n          // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n          const averageNeighborBlackPoint = (\n            blackPoints.get(hortizontalRegion, verticalRegion - 1) +\n            (2 * blackPoints.get(hortizontalRegion - 1, verticalRegion)) +\n            blackPoints.get(hortizontalRegion - 1, verticalRegion - 1)\n          ) / 4;\n          if (min < averageNeighborBlackPoint) {\n            average = averageNeighborBlackPoint; // no need to apply black bias as already applied to neighbors\n          }\n        }\n      }\n      blackPoints.set(hortizontalRegion, verticalRegion, average);\n    }\n  }\n\n  let binarized: BitMatrix;\n  if (canOverwriteImage) {\n    const binarizedBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n    bufferOffset += pixelCount;\n    binarized = new BitMatrix(binarizedBuffer, width);\n  } else {\n    binarized = BitMatrix.createEmpty(width, height);\n  }\n\n  let inverted: BitMatrix = null;\n  if (returnInverted) {\n    if (canOverwriteImage) {\n      const invertedBuffer = new Uint8ClampedArray(data.buffer, bufferOffset, pixelCount);\n      inverted = new BitMatrix(invertedBuffer, width);\n    } else {\n      inverted = BitMatrix.createEmpty(width, height);\n    }\n  }\n\n  for (let verticalRegion = 0; verticalRegion < verticalRegionCount; verticalRegion++) {\n    for (let hortizontalRegion = 0; hortizontalRegion < horizontalRegionCount; hortizontalRegion++) {\n      const left = numBetween(hortizontalRegion, 2, horizontalRegionCount - 3);\n      const top = numBetween(verticalRegion, 2, verticalRegionCount - 3);\n      let sum = 0;\n      for (let xRegion = -2; xRegion <= 2; xRegion++) {\n        for (let yRegion = -2; yRegion <= 2; yRegion++) {\n          sum += blackPoints.get(left + xRegion, top + yRegion);\n        }\n      }\n      const threshold = sum / 25;\n      for (let xRegion = 0; xRegion < REGION_SIZE; xRegion++) {\n        for (let yRegion = 0; yRegion < REGION_SIZE; yRegion++) {\n          const x = hortizontalRegion * REGION_SIZE + xRegion;\n          const y = verticalRegion * REGION_SIZE + yRegion;\n          const lum = greyscalePixels.get(x, y);\n          binarized.set(x, y, lum <= threshold);\n          if (returnInverted) {\n            inverted.set(x, y, !(lum <= threshold));\n          }\n        }\n      }\n    }\n  }\n  if (returnInverted) {\n    return { binarized, inverted };\n  }\n  return { binarized };\n}\n", "// tslint:disable:no-bitwise\n\nexport class BitStream {\n  private bytes: Uint8ClampedArray;\n  private byteOffset: number = 0;\n  private bitOffset: number = 0;\n\n  constructor(bytes: Uint8ClampedArray) {\n    this.bytes = bytes;\n  }\n\n  public readBits(numBits: number): number {\n    if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n      throw new Error(\"Cannot read \" + numBits.toString() + \" bits\");\n    }\n\n    let result = 0;\n    // First, read remainder from current byte\n    if (this.bitOffset > 0) {\n      const bitsLeft = 8 - this.bitOffset;\n      const toRead = numBits < bitsLeft ? numBits : bitsLeft;\n      const bitsToNotRead = bitsLeft - toRead;\n      const mask = (0xFF >> (8 - toRead)) << bitsToNotRead;\n      result = (this.bytes[this.byteOffset] & mask) >> bitsToNotRead;\n      numBits -= toRead;\n      this.bitOffset += toRead;\n      if (this.bitOffset === 8) {\n        this.bitOffset = 0;\n        this.byteOffset++;\n      }\n    }\n\n    // Next read whole bytes\n    if (numBits > 0) {\n      while (numBits >= 8) {\n        result = (result << 8) | (this.bytes[this.byteOffset] & 0xFF);\n        this.byteOffset++;\n        numBits -= 8;\n      }\n\n      // Finally read a partial byte\n      if (numBits > 0) {\n        const bitsToNotRead = 8 - numBits;\n        const mask = (0xFF >> bitsToNotRead) << bitsToNotRead;\n        result = (result << numBits) | ((this.bytes[this.byteOffset] & mask) >> bitsToNotRead);\n        this.bitOffset += numBits;\n      }\n    }\n    return result;\n  }\n\n  public available(): number {\n    return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n  }\n}\n", "// tslint:disable:no-bitwise\nimport { BitStream } from \"./BitStream\";\n\nexport interface Chunk {\n  type: Mode;\n  text: string;\n}\n\nexport interface ByteChunk {\n  type: Mode.Byte | Mode.Kanji;\n  bytes: number[];\n}\n\nexport interface ECIChunk {\n  type: Mode.ECI;\n  assignmentNumber: number;\n}\n\nexport interface StructuredAppend {\n  type: Mode.StructuredAppend;\n  currentSequence: number;\n  totalSequence: number;\n  parity: number;\n}\n\nexport type Chunks = Array<Chunk | ByteChunk | ECIChunk | StructuredAppend>;\n\nexport interface DecodedQR {\n  text: string;\n  bytes: number[];\n  chunks: Chunks;\n  version: number;\n}\n\nexport enum Mode {\n  Numeric = \"numeric\",\n  Alphanumeric = \"alphanumeric\",\n  Byte = \"byte\",\n  Kanji = \"kanji\",\n  ECI = \"eci\",\n  StructuredAppend = \"structuredappend\",\n}\n\nenum ModeByte {\n  Terminator = 0x0,\n  Numeric = 0x1,\n  Alphanumeric = 0x2,\n  Byte = 0x4,\n  Kanji = 0x8,\n  ECI = 0x7,\n  StructuredAppend = 0x3,\n  // FNC1FirstPosition = 0x5,\n  // FNC1SecondPosition = 0x9,\n}\n\nfunction decodeNumeric(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [10, 12, 14][size];\n  let length = stream.readBits(characterCountSize);\n  // Read digits in groups of 3\n  while (length >= 3) {\n    const num = stream.readBits(10);\n    if (num >= 1000) {\n      throw new Error(\"Invalid numeric value above 999\");\n    }\n\n    const a = Math.floor(num / 100);\n    const b = Math.floor(num / 10) % 10;\n    const c = num % 10;\n\n    bytes.push(48 + a, 48 + b, 48 + c);\n    text += a.toString() + b.toString() + c.toString();\n    length -= 3;\n  }\n\n  // If the number of digits aren't a multiple of 3, the remaining digits are special cased.\n  if (length === 2) {\n    const num = stream.readBits(7);\n    if (num >= 100) {\n      throw new Error(\"Invalid numeric value above 99\");\n    }\n\n    const a = Math.floor(num / 10);\n    const b = num % 10;\n\n    bytes.push(48 + a, 48 + b);\n    text += a.toString() + b.toString();\n  } else if (length === 1) {\n    const num = stream.readBits(4);\n    if (num >= 10) {\n      throw new Error(\"Invalid numeric value above 9\");\n    }\n\n    bytes.push(48 + num);\n    text += num.toString();\n  }\n\n  return { bytes, text };\n}\n\nconst AlphanumericCharacterCodes = [\n  \"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\",\n  \"9\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\",\n  \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\",\n  \"R\", \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\",\n  \" \", \"$\", \"%\", \"*\", \"+\", \"-\", \".\", \"/\", \":\",\n];\n\nfunction decodeAlphanumeric(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [9, 11, 13][size];\n  let length = stream.readBits(characterCountSize);\n  while (length >= 2) {\n    const v = stream.readBits(11);\n\n    const a = Math.floor(v / 45);\n    const b = v % 45;\n\n    bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0), AlphanumericCharacterCodes[b].charCodeAt(0));\n    text += AlphanumericCharacterCodes[a] + AlphanumericCharacterCodes[b];\n    length -= 2;\n  }\n\n  if (length === 1) {\n    const a = stream.readBits(6);\n    bytes.push(AlphanumericCharacterCodes[a].charCodeAt(0));\n    text += AlphanumericCharacterCodes[a];\n  }\n\n  return { bytes, text };\n}\n\nfunction decodeByte(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n  let text = \"\";\n\n  const characterCountSize = [8, 16, 16][size];\n  const length = stream.readBits(characterCountSize);\n  for (let i = 0; i < length; i++) {\n    const b = stream.readBits(8);\n    bytes.push(b);\n  }\n  try {\n    text += decodeURIComponent(bytes.map(b => `%${(\"0\" + b.toString(16)).substr(-2)}`).join(\"\"));\n  } catch {\n    // failed to decode\n  }\n\n  return { bytes, text };\n}\n\nfunction decodeKanji(stream: BitStream, size: number) {\n  const bytes: number[] = [];\n\n  const characterCountSize = [8, 10, 12][size];\n  const length = stream.readBits(characterCountSize);\n  for (let i = 0; i < length; i++) {\n    const k = stream.readBits(13);\n\n    let c = (Math.floor(k / 0xC0) << 8) | (k % 0xC0);\n    if (c < 0x1F00) {\n      c += 0x8140;\n    } else {\n      c += 0xC140;\n    }\n\n    bytes.push(c >> 8, c & 0xFF);\n  }\n\n  const text = new TextDecoder(\"shift-jis\").decode(Uint8Array.from(bytes));\n  return { bytes, text };\n}\n\nexport function decode(data: Uint8ClampedArray, version: number): DecodedQR {\n  const stream = new BitStream(data);\n\n  // There are 3 'sizes' based on the version. 1-9 is small (0), 10-26 is medium (1) and 27-40 is large (2).\n  const size = version <= 9 ? 0 : version <= 26 ? 1 : 2;\n\n  const result: DecodedQR = {\n    text: \"\",\n    bytes: [],\n    chunks: [],\n    version,\n  };\n\n  while (stream.available() >= 4) {\n    const mode = stream.readBits(4);\n    if (mode === ModeByte.Terminator) {\n      return result;\n    } else if (mode === ModeByte.ECI) {\n      if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(7),\n        });\n      } else if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(14),\n        });\n      } else if (stream.readBits(1) === 0) {\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: stream.readBits(21),\n        });\n      } else {\n        // ECI data seems corrupted\n        result.chunks.push({\n          type: Mode.ECI,\n          assignmentNumber: -1,\n        });\n      }\n    } else if (mode === ModeByte.Numeric) {\n      const numericResult = decodeNumeric(stream, size);\n      result.text += numericResult.text;\n      result.bytes.push(...numericResult.bytes);\n      result.chunks.push({\n        type: Mode.Numeric,\n        text: numericResult.text,\n      });\n    } else if (mode === ModeByte.Alphanumeric) {\n      const alphanumericResult = decodeAlphanumeric(stream, size);\n      result.text += alphanumericResult.text;\n      result.bytes.push(...alphanumericResult.bytes);\n      result.chunks.push({\n        type: Mode.Alphanumeric,\n        text: alphanumericResult.text,\n      });\n    } else if (mode === ModeByte.Byte) {\n      const byteResult = decodeByte(stream, size);\n      result.text += byteResult.text;\n      result.bytes.push(...byteResult.bytes);\n      result.chunks.push({\n        type: Mode.Byte,\n        bytes: byteResult.bytes,\n        text: byteResult.text,\n      });\n    } else if (mode === ModeByte.Kanji) {\n      const kanjiResult = decodeKanji(stream, size);\n      result.text += kanjiResult.text;\n      result.bytes.push(...kanjiResult.bytes);\n      result.chunks.push({\n        type: Mode.Kanji,\n        bytes: kanjiResult.bytes,\n        text: kanjiResult.text,\n      });\n    } else if (mode === ModeByte.StructuredAppend) {\n      result.chunks.push({\n        type: Mode.StructuredAppend,\n        currentSequence: stream.readBits(4),\n        totalSequence: stream.readBits(4),\n        parity: stream.readBits(8),\n      });\n    }\n  }\n\n  // If there is no data left, or the remaining bits are all 0, then that counts as a termination marker\n  if (stream.available() === 0 || stream.readBits(stream.available()) === 0) {\n    return result;\n  }\n}\n", "import GenericGF, { addOrSubtractGF } from \"./GenericGF\";\n\nexport default class GenericGFPoly {\n  private field: GenericGF;\n  private coefficients: Uint8ClampedArray;\n\n  constructor(field: GenericGF, coefficients: Uint8ClampedArray) {\n    if (coefficients.length === 0) {\n      throw new Error(\"No coefficients.\");\n    }\n    this.field = field;\n    const coefficientsLength = coefficients.length;\n    if (coefficientsLength > 1 && coefficients[0] === 0) {\n      // Leading term must be non-zero for anything except the constant polynomial \"0\"\n      let firstNonZero = 1;\n      while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n        firstNonZero++;\n      }\n      if (firstNonZero === coefficientsLength) {\n        this.coefficients = field.zero.coefficients;\n      } else {\n        this.coefficients = new Uint8ClampedArray(coefficientsLength - firstNonZero);\n        for (let i = 0; i < this.coefficients.length; i++) {\n          this.coefficients[i] = coefficients[firstNonZero + i];\n        }\n      }\n    } else {\n      this.coefficients = coefficients;\n    }\n  }\n\n  public degree() {\n    return this.coefficients.length - 1;\n  }\n\n  public isZero() {\n    return this.coefficients[0] === 0;\n  }\n\n  public getCoefficient(degree: number) {\n    return this.coefficients[this.coefficients.length - 1 - degree];\n  }\n\n  public addOrSubtract(other: GenericGFPoly) {\n    if (this.isZero()) {\n      return other;\n    }\n    if (other.isZero()) {\n      return this;\n    }\n\n    let smallerCoefficients = this.coefficients;\n    let largerCoefficients = other.coefficients;\n    if (smallerCoefficients.length > largerCoefficients.length) {\n      [smallerCoefficients, largerCoefficients] = [largerCoefficients, smallerCoefficients];\n    }\n    const sumDiff = new Uint8ClampedArray(largerCoefficients.length);\n    const lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n    for (let i = 0; i < lengthDiff; i++) {\n      sumDiff[i] = largerCoefficients[i];\n    }\n\n    for (let i = lengthDiff; i < largerCoefficients.length; i++) {\n      sumDiff[i] = addOrSubtractGF(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n    }\n\n    return new GenericGFPoly(this.field, sumDiff);\n  }\n\n  public multiply(scalar: number) {\n    if (scalar === 0) {\n      return this.field.zero;\n    }\n    if (scalar === 1) {\n      return this;\n    }\n    const size = this.coefficients.length;\n    const product = new Uint8ClampedArray(size);\n    for (let i = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], scalar);\n    }\n\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public multiplyPoly(other: GenericGFPoly): GenericGFPoly {\n    if (this.isZero() || other.isZero()) {\n      return this.field.zero;\n    }\n    const aCoefficients = this.coefficients;\n    const aLength = aCoefficients.length;\n    const bCoefficients = other.coefficients;\n    const bLength = bCoefficients.length;\n    const product = new Uint8ClampedArray(aLength + bLength - 1);\n    for (let i = 0; i < aLength; i++) {\n      const aCoeff = aCoefficients[i];\n      for (let j = 0; j < bLength; j++) {\n        product[i + j] = addOrSubtractGF(product[i + j],\n          this.field.multiply(aCoeff, bCoefficients[j]));\n      }\n    }\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public multiplyByMonomial(degree: number, coefficient: number) {\n    if (degree < 0) {\n      throw new Error(\"Invalid degree less than 0\");\n    }\n    if (coefficient === 0) {\n      return this.field.zero;\n    }\n    const size = this.coefficients.length;\n    const product = new Uint8ClampedArray(size + degree);\n    for (let i = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], coefficient);\n    }\n    return new GenericGFPoly(this.field, product);\n  }\n\n  public evaluateAt(a: number) {\n    let result = 0;\n    if (a === 0) {\n      // Just return the x^0 coefficient\n      return this.getCoefficient(0);\n    }\n    const size = this.coefficients.length;\n    if (a === 1) {\n      // Just the sum of the coefficients\n      this.coefficients.forEach((coefficient) => {\n        result = addOrSubtractGF(result, coefficient);\n      });\n      return result;\n    }\n    result = this.coefficients[0];\n    for (let i = 1; i < size; i++) {\n      result = addOrSubtractGF(this.field.multiply(a, result), this.coefficients[i]);\n    }\n    return result;\n  }\n}\n", "import GenericGFPoly from \"./GenericGFPoly\";\n\nexport function addOrSubtractGF(a: number, b: number) {\n  return a ^ b; // tslint:disable-line:no-bitwise\n}\n\nexport default class GenericGF {\n  public primitive: number;\n  public size: number;\n  public generatorBase: number;\n  public zero: GenericGFPoly;\n  public one: GenericGFPoly;\n\n  private expTable: number[];\n  private logTable: number[];\n\n  constructor(primitive: number, size: number, genBase: number) {\n    this.primitive = primitive;\n    this.size = size;\n    this.generatorBase = genBase;\n    this.expTable = new Array(this.size);\n    this.logTable = new Array(this.size);\n\n    let x = 1;\n    for (let i = 0; i < this.size; i++) {\n      this.expTable[i] = x;\n      x = x * 2;\n      if (x >= this.size) {\n        x = (x ^ this.primitive) & (this.size - 1); // tslint:disable-line:no-bitwise\n      }\n    }\n\n    for (let i = 0; i < this.size - 1; i++) {\n      this.logTable[this.expTable[i]] = i;\n    }\n    this.zero = new GenericGFPoly(this, Uint8ClampedArray.from([0]));\n    this.one = new GenericGFPoly(this, Uint8ClampedArray.from([1]));\n  }\n\n  public multiply(a: number, b: number) {\n    if (a === 0 || b === 0) {\n      return 0;\n    }\n    return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n  }\n\n  public inverse(a: number) {\n    if (a === 0) {\n      throw new Error(\"Can't invert 0\");\n    }\n    return this.expTable[this.size - this.logTable[a] - 1];\n  }\n\n  public buildMonomial(degree: number, coefficient: number): GenericGFPoly {\n    if (degree < 0) {\n      throw new Error(\"Invalid monomial degree less than 0\");\n    }\n    if (coefficient === 0) {\n      return this.zero;\n    }\n    const coefficients = new Uint8ClampedArray(degree + 1);\n    coefficients[0] = coefficient;\n    return new GenericGFPoly(this, coefficients);\n  }\n\n  public log(a: number) {\n    if (a === 0) {\n      throw new Error(\"Can't take log(0)\");\n    }\n    return this.logTable[a];\n  }\n\n  public exp(a: number) {\n    return this.expTable[a];\n  }\n}\n", "import GenericGF, { addOrSubtractGF } from \"./GenericGF\";\nimport GenericGFPoly from \"./GenericGFPoly\";\n\nfunction runEuclideanAlgorithm(field: GenericGF, a: GenericGFPoly, b: GenericGFPoly, R: number): GenericGFPoly[] {\n  // Assume a's degree is >= b's\n  if (a.degree() < b.degree()) {\n    [a, b] = [b, a];\n  }\n\n  let rLast = a;\n  let r = b;\n  let tLast = field.zero;\n  let t = field.one;\n\n  // Run Euclidean algorithm until r's degree is less than R/2\n  while (r.degree() >= R / 2) {\n    const rLastLast = rLast;\n    const tLastLast = tLast;\n    rLast = r;\n    tLast = t;\n\n    // Divide rLastLast by rLast, with quotient in q and remainder in r\n    if (rLast.isZero()) {\n      // Euclidean algorithm already terminated?\n      return null;\n    }\n    r = rLastLast;\n    let q = field.zero;\n    const denominatorLeadingTerm = rLast.getCoefficient(rLast.degree());\n    const dltInverse = field.inverse(denominatorLeadingTerm);\n    while (r.degree() >= rLast.degree() && !r.isZero()) {\n      const degreeDiff = r.degree() - rLast.degree();\n      const scale = field.multiply(r.getCoefficient(r.degree()), dltInverse);\n      q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n      r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n    }\n\n    t = q.multiplyPoly(tLast).addOrSubtract(tLastLast);\n\n    if (r.degree() >= rLast.degree()) {\n      return null;\n    }\n  }\n\n  const sigmaTildeAtZero = t.getCoefficient(0);\n  if (sigmaTildeAtZero === 0) {\n    return null;\n  }\n\n  const inverse = field.inverse(sigmaTildeAtZero);\n  return [t.multiply(inverse), r.multiply(inverse)];\n}\n\nfunction findErrorLocations(field: GenericGF, errorLocator: GenericGFPoly): number[] {\n  // This is a direct application of Chien's search\n  const numErrors = errorLocator.degree();\n  if (numErrors === 1) {\n    return [errorLocator.getCoefficient(1)];\n  }\n  const result: number[] = new Array(numErrors);\n  let errorCount = 0;\n  for (let i = 1; i < field.size && errorCount < numErrors; i++) {\n    if (errorLocator.evaluateAt(i) === 0) {\n      result[errorCount] = field.inverse(i);\n      errorCount++;\n    }\n  }\n  if (errorCount !== numErrors) {\n    return null;\n  }\n  return result;\n}\n\nfunction findErrorMagnitudes(field: GenericGF, errorEvaluator: GenericGFPoly, errorLocations: number[]): number[] {\n  // This is directly applying Forney's Formula\n  const s = errorLocations.length;\n  const result: number[] = new Array(s);\n  for (let i = 0; i < s; i++) {\n    const xiInverse = field.inverse(errorLocations[i]);\n    let denominator = 1;\n    for (let j = 0; j < s; j++) {\n      if (i !== j) {\n        denominator = field.multiply(denominator, addOrSubtractGF(1, field.multiply(errorLocations[j], xiInverse)));\n      }\n    }\n    result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n    if (field.generatorBase !== 0) {\n      result[i] = field.multiply(result[i], xiInverse);\n    }\n  }\n  return result;\n}\n\nexport function decode(bytes: number[], twoS: number) {\n  const outputBytes = new Uint8ClampedArray(bytes.length);\n  outputBytes.set(bytes);\n\n  const field = new GenericGF(0x011D, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n  const poly = new GenericGFPoly(field, outputBytes);\n\n  const syndromeCoefficients = new Uint8ClampedArray(twoS);\n  let error = false;\n  for (let s = 0; s < twoS; s++) {\n    const evaluation = poly.evaluateAt(field.exp(s + field.generatorBase));\n    syndromeCoefficients[syndromeCoefficients.length - 1 - s] = evaluation;\n    if (evaluation !== 0) {\n      error = true;\n    }\n  }\n  if (!error) {\n    return outputBytes;\n  }\n\n  const syndrome = new GenericGFPoly(field, syndromeCoefficients);\n\n  const sigmaOmega = runEuclideanAlgorithm(field, field.buildMonomial(twoS, 1), syndrome, twoS);\n  if (sigmaOmega === null) {\n    return null;\n  }\n\n  const errorLocations = findErrorLocations(field, sigmaOmega[0]);\n  if (errorLocations == null) {\n    return null;\n  }\n\n  const errorMagnitudes = findErrorMagnitudes(field, sigmaOmega[1], errorLocations);\n  for (let i = 0; i < errorLocations.length; i++) {\n    const position = outputBytes.length - 1 - field.log(errorLocations[i]);\n    if (position < 0) {\n      return null;\n    }\n    outputBytes[position] = addOrSubtractGF(outputBytes[position], errorMagnitudes[i]);\n  }\n\n  return outputBytes;\n}\n", "export interface Version {\n  infoBits: number;\n  versionNumber: number;\n  alignmentPatternCenters: number[];\n  errorCorrectionLevels: Array<{\n    ecCodewordsPerBlock: number;\n    ecBlocks: Array<{\n      numBlocks: number;\n      dataCodewordsPerBlock: number;\n    }>\n  }>;\n}\n\nexport const VERSIONS: Version[] = [\n  {\n    infoBits: null,\n    versionNumber: 1,\n    alignmentPatternCenters: [],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 7,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 19 }],\n      },\n      {\n        ecCodewordsPerBlock: 10,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n      },\n      {\n        ecCodewordsPerBlock: 13,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 13 }],\n      },\n      {\n        ecCodewordsPerBlock: 17,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 9 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 2,\n    alignmentPatternCenters: [6, 18],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 10,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 34 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 28 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 22 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 16 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 3,\n    alignmentPatternCenters: [6, 22],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 15,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 55 }],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 44 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 17 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 13 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 4,\n    alignmentPatternCenters: [6, 26],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 80 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 32 }],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 24 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 9 }],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 5,\n    alignmentPatternCenters: [6, 30],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 1, dataCodewordsPerBlock: 108 }],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 43 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n          { numBlocks: 2, dataCodewordsPerBlock: 16 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 11 },\n          { numBlocks: 2, dataCodewordsPerBlock: 12 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: null,\n    versionNumber: 6,\n    alignmentPatternCenters: [6, 34],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 68 }],\n      },\n      {\n        ecCodewordsPerBlock: 16,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 27 }],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 19 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 15 }],\n      },\n    ],\n  },\n  {\n    infoBits: 0x07C94,\n    versionNumber: 7,\n    alignmentPatternCenters: [6, 22, 38],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 78 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 31 }],\n      },\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 4, dataCodewordsPerBlock: 15 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 13 },\n          { numBlocks: 1, dataCodewordsPerBlock: 14 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x085BC,\n    versionNumber: 8,\n    alignmentPatternCenters: [6, 24, 42],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 97 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 38 },\n          { numBlocks: 2, dataCodewordsPerBlock: 39 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 18 },\n          { numBlocks: 2, dataCodewordsPerBlock: 19 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 14 },\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x09A99,\n    versionNumber: 9,\n    alignmentPatternCenters: [6, 26, 46],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [{ numBlocks: 2, dataCodewordsPerBlock: 116 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 36 },\n          { numBlocks: 2, dataCodewordsPerBlock: 37 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 16 },\n          { numBlocks: 4, dataCodewordsPerBlock: 17 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 12 },\n          { numBlocks: 4, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0A4D3,\n    versionNumber: 10,\n    alignmentPatternCenters: [6, 28, 50],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 18,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 68 },\n          { numBlocks: 2, dataCodewordsPerBlock: 69 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 43 },\n          { numBlocks: 1, dataCodewordsPerBlock: 44 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 19 },\n          { numBlocks: 2, dataCodewordsPerBlock: 20 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 15 },\n          { numBlocks: 2, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0BBF6,\n    versionNumber: 11,\n    alignmentPatternCenters: [6, 30, 54],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 81 }],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 50 },\n          { numBlocks: 4, dataCodewordsPerBlock: 51 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 22 },\n          { numBlocks: 4, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 12 },\n          { numBlocks: 8, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0C762,\n    versionNumber: 12,\n    alignmentPatternCenters: [6, 32, 58],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 92 },\n          { numBlocks: 2, dataCodewordsPerBlock: 93 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 36 },\n          { numBlocks: 2, dataCodewordsPerBlock: 37 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 20 },\n          { numBlocks: 6, dataCodewordsPerBlock: 21 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 14 },\n          { numBlocks: 4, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0D847,\n    versionNumber: 13,\n    alignmentPatternCenters: [6, 34, 62],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 4, dataCodewordsPerBlock: 107 }],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 37 },\n          { numBlocks: 1, dataCodewordsPerBlock: 38 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 20 },\n          { numBlocks: 4, dataCodewordsPerBlock: 21 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 11 },\n          { numBlocks: 4, dataCodewordsPerBlock: 12 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0E60D,\n    versionNumber: 14,\n    alignmentPatternCenters: [6, 26, 46, 66],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 115 },\n          { numBlocks: 1, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 40 },\n          { numBlocks: 5, dataCodewordsPerBlock: 41 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 20,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 16 },\n          { numBlocks: 5, dataCodewordsPerBlock: 17 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 12 },\n          { numBlocks: 5, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x0F928,\n    versionNumber: 15,\n    alignmentPatternCenters: [6, 26, 48, 70],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 22,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 87 },\n          { numBlocks: 1, dataCodewordsPerBlock: 88 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 41 },\n          { numBlocks: 5, dataCodewordsPerBlock: 42 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 24 },\n          { numBlocks: 7, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 12 },\n          { numBlocks: 7, dataCodewordsPerBlock: 13 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x10B78,\n    versionNumber: 16,\n    alignmentPatternCenters: [6, 26, 50, 74],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 98 },\n          { numBlocks: 1, dataCodewordsPerBlock: 99 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 45 },\n          { numBlocks: 3, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 19 },\n          { numBlocks: 2, dataCodewordsPerBlock: 20 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 15 },\n          { numBlocks: 13, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1145D,\n    versionNumber: 17,\n    alignmentPatternCenters: [6, 30, 54, 78],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 107 },\n          { numBlocks: 5, dataCodewordsPerBlock: 108 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 46 },\n          { numBlocks: 1, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 22 },\n          { numBlocks: 15, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 17, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x12A17,\n    versionNumber: 18,\n    alignmentPatternCenters: [6, 30, 56, 82],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 120 },\n          { numBlocks: 1, dataCodewordsPerBlock: 121 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 9, dataCodewordsPerBlock: 43 },\n          { numBlocks: 4, dataCodewordsPerBlock: 44 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 22 },\n          { numBlocks: 1, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 14 },\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x13532,\n    versionNumber: 19,\n    alignmentPatternCenters: [6, 30, 58, 86],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 113 },\n          { numBlocks: 4, dataCodewordsPerBlock: 114 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 44 },\n          { numBlocks: 11, dataCodewordsPerBlock: 45 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 21 },\n          { numBlocks: 4, dataCodewordsPerBlock: 22 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 9, dataCodewordsPerBlock: 13 },\n          { numBlocks: 16, dataCodewordsPerBlock: 14 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x149A6,\n    versionNumber: 20,\n    alignmentPatternCenters: [6, 34, 62, 90],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 107 },\n          { numBlocks: 5, dataCodewordsPerBlock: 108 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 41 },\n          { numBlocks: 13, dataCodewordsPerBlock: 42 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 24 },\n          { numBlocks: 5, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 15 },\n          { numBlocks: 10, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x15683,\n    versionNumber: 21,\n    alignmentPatternCenters: [6, 28, 50, 72, 94],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 116 },\n          { numBlocks: 4, dataCodewordsPerBlock: 117 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 42 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 22 },\n          { numBlocks: 6, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 16 },\n          { numBlocks: 6, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x168C9,\n    versionNumber: 22,\n    alignmentPatternCenters: [6, 26, 50, 74, 98],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 111 },\n          { numBlocks: 7, dataCodewordsPerBlock: 112 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 46 }],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 24 },\n          { numBlocks: 16, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 24,\n        ecBlocks: [{ numBlocks: 34, dataCodewordsPerBlock: 13 }],\n      },\n    ],\n  },\n  {\n    infoBits: 0x177EC,\n    versionNumber: 23,\n    alignmentPatternCenters: [6, 30, 54, 74, 102],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 121 },\n          { numBlocks: 5, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 47 },\n          { numBlocks: 14, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 16, dataCodewordsPerBlock: 15 },\n          { numBlocks: 14, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x18EC4,\n    versionNumber: 24,\n    alignmentPatternCenters: [6, 28, 54, 80, 106],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 117 },\n          { numBlocks: 4, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 45 },\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 24 },\n          { numBlocks: 16, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 30, dataCodewordsPerBlock: 16 },\n          { numBlocks: 2, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x191E1,\n    versionNumber: 25,\n    alignmentPatternCenters: [6, 32, 58, 84, 110],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 26,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 106 },\n          { numBlocks: 4, dataCodewordsPerBlock: 107 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 47 },\n          { numBlocks: 13, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 24 },\n          { numBlocks: 22, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 15 },\n          { numBlocks: 13, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1AFAB,\n    versionNumber: 26,\n    alignmentPatternCenters: [6, 30, 58, 86, 114],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 114 },\n          { numBlocks: 2, dataCodewordsPerBlock: 115 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 46 },\n          { numBlocks: 4, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 28, dataCodewordsPerBlock: 22 },\n          { numBlocks: 6, dataCodewordsPerBlock: 23 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 33, dataCodewordsPerBlock: 16 },\n          { numBlocks: 4, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1B08E,\n    versionNumber: 27,\n    alignmentPatternCenters: [6, 34, 62, 90, 118],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 122 },\n          { numBlocks: 4, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 45 },\n          { numBlocks: 3, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 8, dataCodewordsPerBlock: 23 },\n          { numBlocks: 26, dataCodewordsPerBlock: 24 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 15 },\n          { numBlocks: 28, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1CC1A,\n    versionNumber: 28,\n    alignmentPatternCenters: [6, 26, 50, 74, 98, 122],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 117 },\n          { numBlocks: 10, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 3, dataCodewordsPerBlock: 45 },\n          { numBlocks: 23, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 24 },\n          { numBlocks: 31, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 15 },\n          { numBlocks: 31, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1D33F,\n    versionNumber: 29,\n    alignmentPatternCenters: [6, 30, 54, 78, 102, 126],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 7, dataCodewordsPerBlock: 116 },\n          { numBlocks: 7, dataCodewordsPerBlock: 117 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 21, dataCodewordsPerBlock: 45 },\n          { numBlocks: 7, dataCodewordsPerBlock: 46 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 1, dataCodewordsPerBlock: 23 },\n          { numBlocks: 37, dataCodewordsPerBlock: 24 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n          { numBlocks: 26, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1ED75,\n    versionNumber: 30,\n    alignmentPatternCenters: [6, 26, 52, 78, 104, 130],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 5, dataCodewordsPerBlock: 115 },\n          { numBlocks: 10, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 47 },\n          { numBlocks: 10, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 15, dataCodewordsPerBlock: 24 },\n          { numBlocks: 25, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 23, dataCodewordsPerBlock: 15 },\n          { numBlocks: 25, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x1F250,\n    versionNumber: 31,\n    alignmentPatternCenters: [6, 30, 56, 82, 108, 134],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 115 },\n          { numBlocks: 3, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 46 },\n          { numBlocks: 29, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 42, dataCodewordsPerBlock: 24 },\n          { numBlocks: 1, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 23, dataCodewordsPerBlock: 15 },\n          { numBlocks: 28, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x209D5,\n    versionNumber: 32,\n    alignmentPatternCenters: [6, 34, 60, 86, 112, 138],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [{ numBlocks: 17, dataCodewordsPerBlock: 115 }],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 46 },\n          { numBlocks: 23, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 24 },\n          { numBlocks: 35, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 15 },\n          { numBlocks: 35, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x216F0,\n    versionNumber: 33,\n    alignmentPatternCenters: [6, 30, 58, 86, 114, 142],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 115 },\n          { numBlocks: 1, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n          { numBlocks: 21, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 29, dataCodewordsPerBlock: 24 },\n          { numBlocks: 19, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 11, dataCodewordsPerBlock: 15 },\n          { numBlocks: 46, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x228BA,\n    versionNumber: 34,\n    alignmentPatternCenters: [6, 34, 62, 90, 118, 146],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 115 },\n          { numBlocks: 6, dataCodewordsPerBlock: 116 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 14, dataCodewordsPerBlock: 46 },\n          { numBlocks: 23, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 44, dataCodewordsPerBlock: 24 },\n          { numBlocks: 7, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 59, dataCodewordsPerBlock: 16 },\n          { numBlocks: 1, dataCodewordsPerBlock: 17 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x2379F,\n    versionNumber: 35,\n    alignmentPatternCenters: [6, 30, 54, 78, 102, 126, 150],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 121 },\n          { numBlocks: 7, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 12, dataCodewordsPerBlock: 47 },\n          { numBlocks: 26, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 39, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 22, dataCodewordsPerBlock: 15 },\n          { numBlocks: 41, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x24B0B,\n    versionNumber: 36,\n    alignmentPatternCenters: [ 6, 24, 50, 76, 102, 128, 154 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 121 },\n          { numBlocks: 14, dataCodewordsPerBlock: 122 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 6, dataCodewordsPerBlock: 47 },\n          { numBlocks: 34, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 46, dataCodewordsPerBlock: 24 },\n          { numBlocks: 10, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 2, dataCodewordsPerBlock: 15 },\n          { numBlocks: 64, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x2542E,\n    versionNumber: 37,\n    alignmentPatternCenters: [ 6, 28, 54, 80, 106, 132, 158 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 17, dataCodewordsPerBlock: 122 },\n          { numBlocks: 4, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 29, dataCodewordsPerBlock: 46 },\n          { numBlocks: 14, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 49, dataCodewordsPerBlock: 24 },\n          { numBlocks: 10, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 24, dataCodewordsPerBlock: 15 },\n          { numBlocks: 46, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x26A64,\n    versionNumber: 38,\n    alignmentPatternCenters: [ 6, 32, 58, 84, 110, 136, 162 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 4, dataCodewordsPerBlock: 122 },\n          { numBlocks: 18, dataCodewordsPerBlock: 123 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 13, dataCodewordsPerBlock: 46 },\n          { numBlocks: 32, dataCodewordsPerBlock: 47 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 48, dataCodewordsPerBlock: 24 },\n          { numBlocks: 14, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 42, dataCodewordsPerBlock: 15 },\n          { numBlocks: 32, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x27541,\n    versionNumber: 39,\n    alignmentPatternCenters: [ 6, 26, 54, 82, 110, 138, 166 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 20, dataCodewordsPerBlock: 117 },\n          { numBlocks: 4, dataCodewordsPerBlock: 118 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 40, dataCodewordsPerBlock: 47 },\n          { numBlocks: 7, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 43, dataCodewordsPerBlock: 24 },\n          { numBlocks: 22, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 10, dataCodewordsPerBlock: 15 },\n          { numBlocks: 67, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n  {\n    infoBits: 0x28C69,\n    versionNumber: 40,\n    alignmentPatternCenters: [ 6, 30, 58, 86, 114, 142, 170 ],\n    errorCorrectionLevels: [\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 19, dataCodewordsPerBlock: 118 },\n          { numBlocks: 6, dataCodewordsPerBlock: 119 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 28,\n        ecBlocks: [\n          { numBlocks: 18, dataCodewordsPerBlock: 47 },\n          { numBlocks: 31, dataCodewordsPerBlock: 48 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 34, dataCodewordsPerBlock: 24 },\n          { numBlocks: 34, dataCodewordsPerBlock: 25 },\n        ],\n      },\n      {\n        ecCodewordsPerBlock: 30,\n        ecBlocks: [\n          { numBlocks: 20, dataCodewordsPerBlock: 15 },\n          { numBlocks: 61, dataCodewordsPerBlock: 16 },\n        ],\n      },\n    ],\n  },\n];\n", "import { BitMatrix } from \"../BitMatrix\";\nimport { Point } from \"../Point\";\nimport { decode as decodeData, DecodedQR } from \"./decodeData\";\nimport { decode as rsDecode } from \"./reedsolomon\";\nimport { Version, VERSIONS } from \"./version\";\n\n// tslint:disable:no-bitwise\nfunction numBitsDiffering(x: number, y: number) {\n  let z = x ^ y;\n  let bitCount = 0;\n  while (z) {\n    bitCount++;\n    z &= z - 1;\n  }\n  return bitCount;\n}\n\nfunction pushBit(bit: any, byte: number) {\n  return (byte << 1) | bit;\n}\n// tslint:enable:no-bitwise\n\nconst FORMAT_INFO_TABLE = [\n  { bits: 0x5412, formatInfo: { errorCorrectionLevel: 1, dataMask: 0 } },\n  { bits: 0x5125, formatInfo: { errorCorrectionLevel: 1, dataMask: 1 } },\n  { bits: 0x5E7C, formatInfo: { errorCorrectionLevel: 1, dataMask: 2 } },\n  { bits: 0x5B4B, formatInfo: { errorCorrectionLevel: 1, dataMask: 3 } },\n  { bits: 0x45F9, formatInfo: { errorCorrectionLevel: 1, dataMask: 4 } },\n  { bits: 0x40CE, formatInfo: { errorCorrectionLevel: 1, dataMask: 5 } },\n  { bits: 0x4F97, formatInfo: { errorCorrectionLevel: 1, dataMask: 6 } },\n  { bits: 0x4AA0, formatInfo: { errorCorrectionLevel: 1, dataMask: 7 } },\n  { bits: 0x77C4, formatInfo: { errorCorrectionLevel: 0, dataMask: 0 } },\n  { bits: 0x72F3, formatInfo: { errorCorrectionLevel: 0, dataMask: 1 } },\n  { bits: 0x7DAA, formatInfo: { errorCorrectionLevel: 0, dataMask: 2 } },\n  { bits: 0x789D, formatInfo: { errorCorrectionLevel: 0, dataMask: 3 } },\n  { bits: 0x662F, formatInfo: { errorCorrectionLevel: 0, dataMask: 4 } },\n  { bits: 0x6318, formatInfo: { errorCorrectionLevel: 0, dataMask: 5 } },\n  { bits: 0x6C41, formatInfo: { errorCorrectionLevel: 0, dataMask: 6 } },\n  { bits: 0x6976, formatInfo: { errorCorrectionLevel: 0, dataMask: 7 } },\n  { bits: 0x1689, formatInfo: { errorCorrectionLevel: 3, dataMask: 0 } },\n  { bits: 0x13BE, formatInfo: { errorCorrectionLevel: 3, dataMask: 1 } },\n  { bits: 0x1CE7, formatInfo: { errorCorrectionLevel: 3, dataMask: 2 } },\n  { bits: 0x19D0, formatInfo: { errorCorrectionLevel: 3, dataMask: 3 } },\n  { bits: 0x0762, formatInfo: { errorCorrectionLevel: 3, dataMask: 4 } },\n  { bits: 0x0255, formatInfo: { errorCorrectionLevel: 3, dataMask: 5 } },\n  { bits: 0x0D0C, formatInfo: { errorCorrectionLevel: 3, dataMask: 6 } },\n  { bits: 0x083B, formatInfo: { errorCorrectionLevel: 3, dataMask: 7 } },\n  { bits: 0x355F, formatInfo: { errorCorrectionLevel: 2, dataMask: 0 } },\n  { bits: 0x3068, formatInfo: { errorCorrectionLevel: 2, dataMask: 1 } },\n  { bits: 0x3F31, formatInfo: { errorCorrectionLevel: 2, dataMask: 2 } },\n  { bits: 0x3A06, formatInfo: { errorCorrectionLevel: 2, dataMask: 3 } },\n  { bits: 0x24B4, formatInfo: { errorCorrectionLevel: 2, dataMask: 4 } },\n  { bits: 0x2183, formatInfo: { errorCorrectionLevel: 2, dataMask: 5 } },\n  { bits: 0x2EDA, formatInfo: { errorCorrectionLevel: 2, dataMask: 6 } },\n  { bits: 0x2BED, formatInfo: { errorCorrectionLevel: 2, dataMask: 7 } },\n];\n\nconst DATA_MASKS = [\n  (p: Point) => ((p.y + p.x) % 2) === 0,\n  (p: Point) => (p.y % 2) === 0,\n  (p: Point) => p.x % 3 === 0,\n  (p: Point) => (p.y + p.x) % 3 === 0,\n  (p: Point) => (Math.floor(p.y / 2) + Math.floor(p.x / 3)) % 2 === 0,\n  (p: Point) => ((p.x * p.y) % 2) + ((p.x * p.y) % 3) === 0,\n  (p: Point) => ((((p.y * p.x) % 2) + (p.y * p.x) % 3) % 2) === 0,\n  (p: Point) => ((((p.y + p.x) % 2) + (p.y * p.x) % 3) % 2) === 0,\n];\n\ninterface FormatInformation {\n  errorCorrectionLevel: number;\n  dataMask: number;\n}\n\nfunction buildFunctionPatternMask(version: Version): BitMatrix {\n  const dimension = 17 + 4 * version.versionNumber;\n  const matrix = BitMatrix.createEmpty(dimension, dimension);\n\n  matrix.setRegion(0, 0, 9, 9, true); // Top left finder pattern + separator + format\n  matrix.setRegion(dimension - 8, 0, 8, 9, true); // Top right finder pattern + separator + format\n  matrix.setRegion(0, dimension - 8, 9, 8, true); // Bottom left finder pattern + separator + format\n\n  // Alignment patterns\n  for (const x of version.alignmentPatternCenters) {\n    for (const y of version.alignmentPatternCenters) {\n      if (!(x === 6 && y === 6 || x === 6 && y === dimension - 7 || x === dimension - 7 && y === 6)) {\n        matrix.setRegion(x - 2, y - 2, 5, 5, true);\n      }\n    }\n  }\n\n  matrix.setRegion(6, 9, 1, dimension - 17, true); // Vertical timing pattern\n  matrix.setRegion(9, 6, dimension - 17, 1, true); // Horizontal timing pattern\n\n  if (version.versionNumber > 6) {\n    matrix.setRegion(dimension - 11, 0, 3, 6, true); // Version info, top right\n    matrix.setRegion(0, dimension - 11, 6, 3, true); // Version info, bottom left\n  }\n\n  return matrix;\n}\n\nfunction readCodewords(matrix: BitMatrix, version: Version, formatInfo: FormatInformation) {\n  const dataMask = DATA_MASKS[formatInfo.dataMask];\n  const dimension = matrix.height;\n\n  const functionPatternMask = buildFunctionPatternMask(version);\n\n  const codewords: number[] = [];\n  let currentByte = 0;\n  let bitsRead = 0;\n\n  // Read columns in pairs, from right to left\n  let readingUp = true;\n  for (let columnIndex = dimension - 1; columnIndex > 0; columnIndex -= 2) {\n    if (columnIndex === 6) { // Skip whole column with vertical alignment pattern;\n      columnIndex--;\n    }\n    for (let i = 0; i < dimension; i++) {\n      const y = readingUp ? dimension - 1 - i : i;\n      for (let columnOffset = 0; columnOffset < 2; columnOffset++) {\n        const x = columnIndex - columnOffset;\n        if (!functionPatternMask.get(x, y)) {\n          bitsRead++;\n          let bit = matrix.get(x, y);\n          if (dataMask({y, x})) {\n            bit = !bit;\n          }\n          currentByte = pushBit(bit, currentByte);\n          if (bitsRead === 8) { // Whole bytes\n            codewords.push(currentByte);\n            bitsRead = 0;\n            currentByte = 0;\n          }\n        }\n      }\n    }\n    readingUp = !readingUp;\n  }\n  return codewords;\n}\n\nfunction readVersion(matrix: BitMatrix): Version {\n  const dimension = matrix.height;\n\n  const provisionalVersion = Math.floor((dimension - 17) / 4);\n  if (provisionalVersion <= 6) { // 6 and under dont have version info in the QR code\n    return VERSIONS[provisionalVersion - 1];\n  }\n\n  let topRightVersionBits = 0;\n  for (let y = 5; y >= 0; y--) {\n    for (let x = dimension - 9; x >= dimension - 11; x--) {\n      topRightVersionBits = pushBit(matrix.get(x, y), topRightVersionBits);\n    }\n  }\n\n  let bottomLeftVersionBits = 0;\n  for (let x = 5; x >= 0; x--) {\n    for (let y = dimension - 9; y >= dimension - 11; y--) {\n      bottomLeftVersionBits = pushBit(matrix.get(x, y), bottomLeftVersionBits);\n    }\n  }\n\n  let bestDifference = Infinity;\n  let bestVersion: Version;\n  for (const version of VERSIONS) {\n    if (version.infoBits === topRightVersionBits || version.infoBits === bottomLeftVersionBits) {\n      return version;\n    }\n\n    let difference = numBitsDiffering(topRightVersionBits, version.infoBits);\n    if (difference < bestDifference) {\n      bestVersion = version;\n      bestDifference = difference;\n    }\n\n    difference = numBitsDiffering(bottomLeftVersionBits, version.infoBits);\n    if (difference < bestDifference) {\n      bestVersion = version;\n      bestDifference = difference;\n    }\n  }\n  // We can tolerate up to 3 bits of error since no two version info codewords will\n  // differ in less than 8 bits.\n  if (bestDifference <= 3) {\n    return bestVersion;\n  }\n}\n\nfunction readFormatInformation(matrix: BitMatrix) {\n  let topLeftFormatInfoBits = 0;\n  for (let x = 0; x <= 8; x++) {\n    if (x !== 6) { // Skip timing pattern bit\n      topLeftFormatInfoBits = pushBit(matrix.get(x, 8), topLeftFormatInfoBits);\n    }\n  }\n  for (let y = 7; y >= 0; y--) {\n    if (y !== 6) { // Skip timing pattern bit\n      topLeftFormatInfoBits = pushBit(matrix.get(8, y), topLeftFormatInfoBits);\n    }\n  }\n\n  const dimension = matrix.height;\n  let topRightBottomRightFormatInfoBits = 0;\n  for (let y = dimension - 1; y >= dimension - 7; y--) { // bottom left\n    topRightBottomRightFormatInfoBits = pushBit(matrix.get(8, y), topRightBottomRightFormatInfoBits);\n  }\n  for (let x = dimension - 8; x < dimension; x++) { // top right\n    topRightBottomRightFormatInfoBits = pushBit(matrix.get(x, 8), topRightBottomRightFormatInfoBits);\n  }\n\n  let bestDifference = Infinity;\n  let bestFormatInfo = null;\n  for (const {bits, formatInfo} of FORMAT_INFO_TABLE) {\n    if (bits === topLeftFormatInfoBits || bits === topRightBottomRightFormatInfoBits) {\n      return formatInfo;\n    }\n    let difference = numBitsDiffering(topLeftFormatInfoBits, bits);\n    if (difference < bestDifference) {\n      bestFormatInfo = formatInfo;\n      bestDifference = difference;\n    }\n    if (topLeftFormatInfoBits !== topRightBottomRightFormatInfoBits) { // also try the other option\n      difference = numBitsDiffering(topRightBottomRightFormatInfoBits, bits);\n      if (difference < bestDifference) {\n        bestFormatInfo = formatInfo;\n        bestDifference = difference;\n      }\n    }\n  }\n  // Hamming distance of the 32 masked codes is 7, by construction, so <= 3 bits differing means we found a match\n  if (bestDifference <= 3) {\n    return bestFormatInfo;\n  }\n  return null;\n}\n\nfunction getDataBlocks(codewords: number[], version: Version, ecLevel: number) {\n  const ecInfo = version.errorCorrectionLevels[ecLevel];\n  const dataBlocks: Array<{\n    numDataCodewords: number;\n    codewords: number[];\n  }> = [];\n\n  let totalCodewords = 0;\n  ecInfo.ecBlocks.forEach(block => {\n    for (let i = 0; i < block.numBlocks; i++) {\n      dataBlocks.push({ numDataCodewords: block.dataCodewordsPerBlock, codewords: [] });\n      totalCodewords += block.dataCodewordsPerBlock + ecInfo.ecCodewordsPerBlock;\n    }\n  });\n\n  // In some cases the QR code will be malformed enough that we pull off more or less than we should.\n  // If we pull off less there's nothing we can do.\n  // If we pull off more we can safely truncate\n  if (codewords.length < totalCodewords) {\n    return null;\n  }\n  codewords = codewords.slice(0, totalCodewords);\n\n  const shortBlockSize = ecInfo.ecBlocks[0].dataCodewordsPerBlock;\n  // Pull codewords to fill the blocks up to the minimum size\n  for (let i = 0; i < shortBlockSize; i++) {\n    for (const dataBlock of dataBlocks) {\n      dataBlock.codewords.push(codewords.shift());\n    }\n  }\n\n  // If there are any large blocks, pull codewords to fill the last element of those\n  if (ecInfo.ecBlocks.length > 1) {\n    const smallBlockCount = ecInfo.ecBlocks[0].numBlocks;\n    const largeBlockCount = ecInfo.ecBlocks[1].numBlocks;\n    for (let i = 0; i < largeBlockCount; i++) {\n      dataBlocks[smallBlockCount + i].codewords.push(codewords.shift());\n    }\n  }\n\n  // Add the rest of the codewords to the blocks. These are the error correction codewords.\n  while (codewords.length > 0) {\n    for (const dataBlock of dataBlocks) {\n      dataBlock.codewords.push(codewords.shift());\n    }\n  }\n\n  return dataBlocks;\n}\n\nfunction decodeMatrix(matrix: BitMatrix) {\n  const version = readVersion(matrix);\n  if (!version) {\n    return null;\n  }\n\n  const formatInfo = readFormatInformation(matrix);\n  if (!formatInfo) {\n    return null;\n  }\n\n  const codewords = readCodewords(matrix, version, formatInfo);\n  const dataBlocks = getDataBlocks(codewords, version, formatInfo.errorCorrectionLevel);\n  if (!dataBlocks) {\n    return null;\n  }\n\n  // Count total number of data bytes\n  const totalBytes = dataBlocks.reduce((a, b) => a + b.numDataCodewords, 0);\n  const resultBytes = new Uint8ClampedArray(totalBytes);\n\n  let resultIndex = 0;\n  for (const dataBlock of dataBlocks) {\n    const correctedBytes = rsDecode(dataBlock.codewords, dataBlock.codewords.length - dataBlock.numDataCodewords);\n    if (!correctedBytes) {\n      return null;\n    }\n    for (let i = 0; i < dataBlock.numDataCodewords; i++) {\n      resultBytes[resultIndex++] = correctedBytes[i];\n    }\n  }\n\n  try {\n    return decodeData(resultBytes, version.versionNumber);\n  } catch {\n    return null;\n  }\n}\n\nexport function decode(matrix: BitMatrix): DecodedQR {\n  if (matrix == null) {\n    return null;\n  }\n  const result = decodeMatrix(matrix);\n  if (result) {\n    return result;\n  }\n  // Decoding didn't work, try mirroring the QR across the topLeft -> bottomRight line.\n  for (let x = 0; x < matrix.width; x++) {\n    for (let y = x + 1; y < matrix.height; y++) {\n      if (matrix.get(x, y) !== matrix.get(y, x)) {\n        matrix.set(x, y, !matrix.get(x, y));\n        matrix.set(y, x, !matrix.get(y, x));\n      }\n    }\n  }\n  return decodeMatrix(matrix);\n}\n", "import {BitMatrix} from \"../BitMatrix\";\nimport {Point, QRLocation} from \"../locator\";\n\ninterface PerspectiveTransform {\n  a11: number;\n  a21: number;\n  a31: number;\n  a12: number;\n  a22: number;\n  a32: number;\n  a13: number;\n  a23: number;\n  a33: number;\n}\n\nfunction squareToQuadrilateral(p1: Point, p2: Point, p3: Point, p4: Point): PerspectiveTransform {\n  const dx3 = p1.x - p2.x + p3.x - p4.x;\n  const dy3 = p1.y - p2.y + p3.y - p4.y;\n  if (dx3 === 0 && dy3 === 0) { // Affine\n    return {\n      a11: p2.x - p1.x,\n      a12: p2.y - p1.y,\n      a13: 0,\n      a21: p3.x - p2.x,\n      a22: p3.y - p2.y,\n      a23: 0,\n      a31: p1.x,\n      a32: p1.y,\n      a33: 1,\n    };\n  } else {\n    const dx1 = p2.x - p3.x;\n    const dx2 = p4.x - p3.x;\n    const dy1 = p2.y - p3.y;\n    const dy2 = p4.y - p3.y;\n    const denominator = dx1 * dy2 - dx2 * dy1;\n    const a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n    const a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n    return {\n      a11: p2.x - p1.x + a13 * p2.x,\n      a12: p2.y - p1.y + a13 * p2.y,\n      a13,\n      a21: p4.x - p1.x + a23 * p4.x,\n      a22: p4.y - p1.y + a23 * p4.y,\n      a23,\n      a31: p1.x,\n      a32: p1.y,\n      a33: 1,\n    };\n  }\n}\n\nfunction quadrilateralToSquare(p1: Point, p2: Point, p3: Point, p4: Point): PerspectiveTransform {\n  // Here, the adjoint serves as the inverse:\n  const sToQ = squareToQuadrilateral(p1, p2, p3, p4);\n  return {\n    a11: sToQ.a22 * sToQ.a33 - sToQ.a23 * sToQ.a32,\n    a12: sToQ.a13 * sToQ.a32 - sToQ.a12 * sToQ.a33,\n    a13: sToQ.a12 * sToQ.a23 - sToQ.a13 * sToQ.a22,\n    a21: sToQ.a23 * sToQ.a31 - sToQ.a21 * sToQ.a33,\n    a22: sToQ.a11 * sToQ.a33 - sToQ.a13 * sToQ.a31,\n    a23: sToQ.a13 * sToQ.a21 - sToQ.a11 * sToQ.a23,\n    a31: sToQ.a21 * sToQ.a32 - sToQ.a22 * sToQ.a31,\n    a32: sToQ.a12 * sToQ.a31 - sToQ.a11 * sToQ.a32,\n    a33: sToQ.a11 * sToQ.a22 - sToQ.a12 * sToQ.a21,\n  };\n}\n\nfunction times(a: PerspectiveTransform, b: PerspectiveTransform): PerspectiveTransform {\n  return {\n    a11: a.a11 * b.a11 + a.a21 * b.a12 + a.a31 * b.a13,\n    a12: a.a12 * b.a11 + a.a22 * b.a12 + a.a32 * b.a13,\n    a13: a.a13 * b.a11 + a.a23 * b.a12 + a.a33 * b.a13,\n    a21: a.a11 * b.a21 + a.a21 * b.a22 + a.a31 * b.a23,\n    a22: a.a12 * b.a21 + a.a22 * b.a22 + a.a32 * b.a23,\n    a23: a.a13 * b.a21 + a.a23 * b.a22 + a.a33 * b.a23,\n    a31: a.a11 * b.a31 + a.a21 * b.a32 + a.a31 * b.a33,\n    a32: a.a12 * b.a31 + a.a22 * b.a32 + a.a32 * b.a33,\n    a33: a.a13 * b.a31 + a.a23 * b.a32 + a.a33 * b.a33,\n  };\n}\n\nexport function extract(image: BitMatrix, location: QRLocation) {\n  const qToS = quadrilateralToSquare(\n    {x: 3.5, y: 3.5},\n    {x: location.dimension - 3.5, y: 3.5},\n    {x: location.dimension - 6.5, y: location.dimension - 6.5},\n    {x: 3.5, y: location.dimension - 3.5},\n  );\n  const sToQ = squareToQuadrilateral(location.topLeft, location.topRight, location.alignmentPattern, location.bottomLeft);\n  const transform = times(sToQ, qToS);\n\n  const matrix = BitMatrix.createEmpty(location.dimension, location.dimension);\n  const mappingFunction = (x: number, y: number) => {\n    const denominator = transform.a13 * x + transform.a23 * y + transform.a33;\n    return {\n      x: (transform.a11 * x + transform.a21 * y + transform.a31) / denominator,\n      y: (transform.a12 * x + transform.a22 * y + transform.a32) / denominator,\n    };\n  };\n\n  for (let y = 0; y < location.dimension; y++) {\n    for (let x = 0; x < location.dimension; x++) {\n      const xValue = x + 0.5;\n      const yValue = y + 0.5;\n      const sourcePixel = mappingFunction(xValue, yValue);\n      matrix.set(x, y, image.get(Math.floor(sourcePixel.x), Math.floor(sourcePixel.y)));\n    }\n  }\n\n  return {\n    matrix,\n    mappingFunction,\n  };\n}\n", "import { BitMatrix } from \"../BitMatrix\";\n\nconst MAX_FINDERPATTERNS_TO_SEARCH = 5;\nconst MIN_QUAD_RATIO = 0.5;\nconst MAX_QUAD_RATIO = 1.5;\n\nexport interface Point {\n  x: number;\n  y: number;\n}\n\nexport interface QRLocation {\n  topRight: Point;\n  bottomLeft: Point;\n  topLeft: Point;\n  alignmentPattern: Point;\n  dimension: number;\n}\n\nconst distance = (a: Point, b: Point) => Math.sqrt((b.x - a.x) ** 2 + (b.y - a.y) ** 2);\n\nfunction sum(values: number[]) {\n  return values.reduce((a, b) => a + b);\n}\n\n// Takes three finder patterns and organizes them into topLeft, topRight, etc\nfunction reorderFinderPatterns(pattern1: Point, pattern2: Point, pattern3: Point) {\n  // Find distances between pattern centers\n  const oneTwoDistance = distance(pattern1, pattern2);\n  const twoThreeDistance = distance(pattern2, pattern3);\n  const oneThreeDistance = distance(pattern1, pattern3);\n\n  let bottomLeft: Point;\n  let topLeft: Point;\n  let topRight: Point;\n\n  // Assume one closest to other two is B; A and C will just be guesses at first\n  if (twoThreeDistance >= oneTwoDistance && twoThreeDistance >= oneThreeDistance) {\n    [bottomLeft, topLeft, topRight] = [pattern2, pattern1, pattern3];\n  } else if (oneThreeDistance >= twoThreeDistance && oneThreeDistance >= oneTwoDistance) {\n    [bottomLeft, topLeft, topRight] = [pattern1, pattern2, pattern3];\n  } else {\n    [bottomLeft, topLeft, topRight] = [pattern1, pattern3, pattern2];\n  }\n\n  // Use cross product to figure out whether bottomLeft (A) and topRight (C) are correct or flipped in relation to topLeft (B)\n  // This asks whether BC x BA has a positive z component, which is the arrangement we want. If it's negative, then\n  // we've got it flipped around and should swap topRight and bottomLeft.\n  if (((topRight.x - topLeft.x) * (bottomLeft.y - topLeft.y)) - ((topRight.y - topLeft.y) * (bottomLeft.x - topLeft.x)) < 0) {\n    [bottomLeft, topRight] = [topRight, bottomLeft];\n  }\n\n  return { bottomLeft, topLeft, topRight };\n}\n\n// Computes the dimension (number of modules on a side) of the QR Code based on the position of the finder patterns\nfunction computeDimension(topLeft: Point, topRight: Point, bottomLeft: Point, matrix: BitMatrix) {\n  const moduleSize = (\n    sum(countBlackWhiteRun(topLeft, bottomLeft, matrix, 5)) / 7 + // Divide by 7 since the ratio is 1:1:3:1:1\n    sum(countBlackWhiteRun(topLeft, topRight, matrix, 5)) / 7 +\n    sum(countBlackWhiteRun(bottomLeft, topLeft, matrix, 5)) / 7 +\n    sum(countBlackWhiteRun(topRight, topLeft, matrix, 5)) / 7\n  ) / 4;\n\n  if (moduleSize < 1) {\n    throw new Error(\"Invalid module size\");\n  }\n\n  const topDimension = Math.round(distance(topLeft, topRight) / moduleSize);\n  const sideDimension = Math.round(distance(topLeft, bottomLeft) / moduleSize);\n  let dimension = Math.floor((topDimension + sideDimension) / 2) + 7;\n  switch (dimension % 4) {\n    case 0:\n      dimension++;\n      break;\n    case 2:\n      dimension--;\n      break;\n  }\n  return { dimension, moduleSize };\n}\n\n// Takes an origin point and an end point and counts the sizes of the black white run from the origin towards the end point.\n// Returns an array of elements, representing the pixel size of the black white run.\n// Uses a variant of http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\nfunction countBlackWhiteRunTowardsPoint(origin: Point, end: Point, matrix: BitMatrix, length: number) {\n  const switchPoints: Point[] = [{x: Math.floor(origin.x), y: Math.floor(origin.y)}];\n  const steep = Math.abs(end.y - origin.y) > Math.abs(end.x - origin.x);\n\n  let fromX: number;\n  let fromY: number;\n  let toX: number;\n  let toY: number;\n  if (steep) {\n    fromX = Math.floor(origin.y);\n    fromY = Math.floor(origin.x);\n    toX = Math.floor(end.y);\n    toY = Math.floor(end.x);\n  } else {\n    fromX = Math.floor(origin.x);\n    fromY = Math.floor(origin.y);\n    toX = Math.floor(end.x);\n    toY = Math.floor(end.y);\n  }\n\n  const dx = Math.abs(toX - fromX);\n  const dy = Math.abs(toY - fromY);\n  let error = Math.floor(-dx / 2);\n  const xStep = fromX < toX ? 1 : -1;\n  const yStep = fromY < toY ? 1 : -1;\n\n  let currentPixel = true;\n  // Loop up until x == toX, but not beyond\n  for (let x = fromX, y = fromY; x !== toX + xStep; x += xStep) {\n    // Does current pixel mean we have moved white to black or vice versa?\n    // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n    // color, advance to next state or end if we are in state 2 already\n    const realX = steep ? y : x;\n    const realY = steep ? x : y;\n    if (matrix.get(realX, realY) !== currentPixel) {\n      currentPixel = !currentPixel;\n      switchPoints.push({x: realX, y: realY});\n      if (switchPoints.length === length + 1) {\n        break;\n      }\n    }\n    error += dy;\n    if (error > 0) {\n      if (y === toY) {\n        break;\n      }\n      y += yStep;\n      error -= dx;\n    }\n  }\n  const distances: number[] = [];\n  for (let i = 0; i < length; i++) {\n    if (switchPoints[i] && switchPoints[i + 1]) {\n      distances.push(distance(switchPoints[i], switchPoints[i + 1]));\n    } else {\n      distances.push(0);\n    }\n  }\n  return distances;\n}\n\n// Takes an origin point and an end point and counts the sizes of the black white run in the origin point\n// along the line that intersects with the end point. Returns an array of elements, representing the pixel sizes\n// of the black white run. Takes a length which represents the number of switches from black to white to look for.\nfunction countBlackWhiteRun(origin: Point, end: Point, matrix: BitMatrix, length: number) {\n  const rise = end.y - origin.y;\n  const run = end.x - origin.x;\n\n  const towardsEnd = countBlackWhiteRunTowardsPoint(origin, end, matrix, Math.ceil(length / 2));\n  const awayFromEnd = countBlackWhiteRunTowardsPoint(origin, {x: origin.x - run, y: origin.y - rise}, matrix, Math.ceil(length / 2));\n\n  const middleValue = towardsEnd.shift() + awayFromEnd.shift() - 1; // Substract one so we don't double count a pixel\n  return awayFromEnd.concat(middleValue).concat(...towardsEnd);\n}\n\n// Takes in a black white run and an array of expected ratios. Returns the average size of the run as well as the \"error\" -\n// that is the amount the run diverges from the expected ratio\nfunction scoreBlackWhiteRun(sequence: number[], ratios: number[]) {\n  const averageSize = sum(sequence) / sum(ratios);\n  let error = 0;\n  ratios.forEach((ratio, i) => {\n    error += (sequence[i] - ratio * averageSize) ** 2;\n  });\n\n  return { averageSize, error };\n}\n\n// Takes an X,Y point and an array of sizes and scores the point against those ratios.\n// For example for a finder pattern takes the ratio list of 1:1:3:1:1 and checks horizontal, vertical and diagonal ratios\n// against that.\nfunction scorePattern(point: Point, ratios: number[], matrix: BitMatrix) {\n  try {\n    const horizontalRun = countBlackWhiteRun(point, {x: -1, y: point.y}, matrix, ratios.length);\n    const verticalRun = countBlackWhiteRun(point, {x: point.x, y: -1}, matrix, ratios.length);\n\n    const topLeftPoint = {\n      x: Math.max(0, point.x - point.y) - 1,\n      y: Math.max(0, point.y - point.x) - 1,\n    };\n    const topLeftBottomRightRun = countBlackWhiteRun(point, topLeftPoint, matrix, ratios.length);\n\n    const bottomLeftPoint = {\n      x: Math.min(matrix.width, point.x + point.y) + 1,\n      y: Math.min(matrix.height, point.y + point.x) + 1,\n    };\n    const bottomLeftTopRightRun = countBlackWhiteRun(point, bottomLeftPoint, matrix, ratios.length);\n\n    const horzError = scoreBlackWhiteRun(horizontalRun, ratios);\n    const vertError = scoreBlackWhiteRun(verticalRun, ratios);\n    const diagDownError = scoreBlackWhiteRun(topLeftBottomRightRun, ratios);\n    const diagUpError = scoreBlackWhiteRun(bottomLeftTopRightRun, ratios);\n\n    const ratioError = Math.sqrt(horzError.error * horzError.error +\n      vertError.error * vertError.error +\n      diagDownError.error * diagDownError.error +\n      diagUpError.error * diagUpError.error);\n\n    const avgSize = (horzError.averageSize + vertError.averageSize + diagDownError.averageSize + diagUpError.averageSize) / 4;\n\n    const sizeError = ((horzError.averageSize - avgSize) ** 2 +\n      (vertError.averageSize - avgSize) ** 2 +\n      (diagDownError.averageSize - avgSize) ** 2 +\n      (diagUpError.averageSize - avgSize) ** 2) / avgSize;\n    return ratioError + sizeError;\n  } catch {\n    return Infinity;\n  }\n}\n\nfunction recenterLocation(matrix: BitMatrix, p: Point): Point {\n  let leftX = Math.round(p.x);\n  while (matrix.get(leftX, Math.round(p.y))) {\n    leftX--;\n  }\n  let rightX = Math.round(p.x);\n  while (matrix.get(rightX, Math.round(p.y))) {\n    rightX++;\n  }\n  const x = (leftX + rightX) / 2;\n\n  let topY = Math.round(p.y);\n  while (matrix.get(Math.round(x), topY)) {\n    topY--;\n  }\n  let bottomY = Math.round(p.y);\n  while (matrix.get(Math.round(x), bottomY)) {\n    bottomY++;\n  }\n  const y = (topY + bottomY) / 2;\n\n  return { x, y };\n}\n\ninterface Quad {\n  top: {\n    startX: number;\n    endX: number;\n    y: number;\n  };\n  bottom: {\n    startX: number;\n    endX: number;\n    y: number;\n  };\n}\n\nexport function locate(matrix: BitMatrix): QRLocation[] {\n  const finderPatternQuads: Quad[] = [];\n  let activeFinderPatternQuads: Quad[] = [];\n  const alignmentPatternQuads: Quad[] = [];\n  let activeAlignmentPatternQuads: Quad[] = [];\n\n  for (let y = 0; y <= matrix.height; y++) {\n    let length = 0;\n    let lastBit = false;\n    let scans = [0, 0, 0, 0, 0];\n\n    for (let x = -1; x <= matrix.width; x++) {\n      const v = matrix.get(x, y);\n      if (v === lastBit) {\n        length++;\n      } else {\n        scans = [scans[1], scans[2], scans[3], scans[4], length];\n        length = 1;\n        lastBit = v;\n\n        // Do the last 5 color changes ~ match the expected ratio for a finder pattern? 1:1:3:1:1 of b:w:b:w:b\n        const averageFinderPatternBlocksize = sum(scans) / 7;\n        const validFinderPattern =\n          Math.abs(scans[0] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[1] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[2] - 3 * averageFinderPatternBlocksize) < 3 * averageFinderPatternBlocksize &&\n          Math.abs(scans[3] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          Math.abs(scans[4] - averageFinderPatternBlocksize) < averageFinderPatternBlocksize &&\n          !v; // And make sure the current pixel is white since finder patterns are bordered in white\n\n        // Do the last 3 color changes ~ match the expected ratio for an alignment pattern? 1:1:1 of w:b:w\n        const averageAlignmentPatternBlocksize = sum(scans.slice(-3)) / 3;\n        const validAlignmentPattern =\n          Math.abs(scans[2] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          Math.abs(scans[3] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          Math.abs(scans[4] - averageAlignmentPatternBlocksize) < averageAlignmentPatternBlocksize &&\n          v; // Is the current pixel black since alignment patterns are bordered in black\n\n        if (validFinderPattern) {\n          // Compute the start and end x values of the large center black square\n          const endX = x - scans[3] - scans[4];\n          const startX = endX - scans[2];\n\n          const line = { startX, endX, y };\n          // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n          // that line as the starting point.\n          const matchingQuads = activeFinderPatternQuads.filter(q =>\n            (startX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (endX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (startX <= q.bottom.startX && endX >= q.bottom.endX && (\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO\n            )),\n          );\n          if (matchingQuads.length > 0) {\n            matchingQuads[0].bottom = line;\n          } else {\n            activeFinderPatternQuads.push({ top: line, bottom: line });\n          }\n        }\n        if (validAlignmentPattern) {\n          // Compute the start and end x values of the center black square\n          const endX = x - scans[4];\n          const startX = endX - scans[3];\n\n          const line = { startX, y, endX };\n          // Is there a quad directly above the current spot? If so, extend it with the new line. Otherwise, create a new quad with\n          // that line as the starting point.\n          const matchingQuads = activeAlignmentPatternQuads.filter(q =>\n            (startX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (endX >= q.bottom.startX && startX <= q.bottom.endX) ||\n            (startX <= q.bottom.startX && endX >= q.bottom.endX && (\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) < MAX_QUAD_RATIO &&\n              (scans[2] / (q.bottom.endX - q.bottom.startX)) > MIN_QUAD_RATIO\n            )),\n          );\n          if (matchingQuads.length > 0) {\n            matchingQuads[0].bottom = line;\n          } else {\n            activeAlignmentPatternQuads.push({ top: line, bottom: line });\n          }\n        }\n      }\n    }\n    finderPatternQuads.push(...activeFinderPatternQuads.filter(q => q.bottom.y !== y && q.bottom.y - q.top.y >= 2));\n    activeFinderPatternQuads = activeFinderPatternQuads.filter(q => q.bottom.y === y);\n\n    alignmentPatternQuads.push(...activeAlignmentPatternQuads.filter(q => q.bottom.y !== y));\n    activeAlignmentPatternQuads = activeAlignmentPatternQuads.filter(q => q.bottom.y === y);\n\n  }\n\n  finderPatternQuads.push(...activeFinderPatternQuads.filter(q => q.bottom.y - q.top.y >= 2));\n  alignmentPatternQuads.push(...activeAlignmentPatternQuads);\n\n  // Refactored from cozmo/jsQR to (hopefully) circumvent an issue in Safari 13+ on both Mac and iOS (also including\n  // iOS Chrome and other Safari iOS derivatives). Safari was very occasionally and apparently not deterministically\n  // throwing a \"RangeError: Array size is not a small enough positive integer.\" exception seemingly within the second\n  // .map of the original code (here the second for-loop). This second .map contained a nested .map call over the same\n  // array instance which was the chained result from previous calls to .map, .filter and .sort which potentially caused\n  // this bug in Safari?\n  // Also see https://github.com/cozmo/jsQR/issues/157 and https://bugs.webkit.org/show_bug.cgi?id=211619#c3\n  const scoredFinderPatternPositions: Array<Point & { size: number, score: number }> = [];\n  for (const quad of finderPatternQuads) {\n    if (quad.bottom.y - quad.top.y < 2) {\n      // All quads must be at least 2px tall since the center square is larger than a block\n      continue;\n    }\n\n    // calculate quad center\n    const x = (quad.top.startX + quad.top.endX + quad.bottom.startX + quad.bottom.endX) / 4;\n    const y = (quad.top.y + quad.bottom.y + 1) / 2;\n    if (!matrix.get(Math.round(x), Math.round(y))) {\n      continue;\n    }\n\n    const lengths = [quad.top.endX - quad.top.startX, quad.bottom.endX - quad.bottom.startX, quad.bottom.y - quad.top.y + 1];\n    const size = sum(lengths) / lengths.length;\n    // Initial scoring of finder pattern quads by looking at their ratios, not taking into account position\n    const score = scorePattern({x: Math.round(x), y: Math.round(y)}, [1, 1, 3, 1, 1], matrix);\n    scoredFinderPatternPositions.push({ score, x, y, size });\n  }\n  if (scoredFinderPatternPositions.length < 3) {\n    // A QR code has 3 finder patterns, therefore we need at least 3 candidates.\n    return null;\n  }\n  scoredFinderPatternPositions.sort((a, b) => a.score - b.score);\n\n  // Now take the top finder pattern options and try to find 2 other options with a similar size.\n  const finderPatternGroups: Array<{ points: [Point, Point, Point], score: number }> = [];\n  for (let i = 0; i < Math.min(scoredFinderPatternPositions.length, MAX_FINDERPATTERNS_TO_SEARCH); ++i) {\n    const point = scoredFinderPatternPositions[i];\n    const otherPoints: typeof scoredFinderPatternPositions = [];\n\n    for (const otherPoint of scoredFinderPatternPositions) {\n      if (otherPoint === point) {\n        continue;\n      }\n      otherPoints.push({\n        ...otherPoint,\n        score: otherPoint.score + ((otherPoint.size - point.size) ** 2) / point.size, // score similarity of sizes\n      });\n    }\n    otherPoints.sort((a, b) => a.score - b.score);\n\n    finderPatternGroups.push({\n      points: [point, otherPoints[0], otherPoints[1]], // note that otherPoints.length >= 2 as scoredFinderPatternPositions.length >= 3\n      score: point.score + otherPoints[0].score + otherPoints[1].score, // total combined score of the three points in the group\n    });\n  }\n  finderPatternGroups.sort((a, b) => a.score - b.score);\n  const bestFinderPatternGroup = finderPatternGroups[0];\n\n  const { topRight, topLeft, bottomLeft } = reorderFinderPatterns(...bestFinderPatternGroup.points);\n  const alignment = findAlignmentPattern(matrix, alignmentPatternQuads, topRight, topLeft, bottomLeft);\n  const result: QRLocation[] = [];\n  if (alignment) {\n    result.push({\n      alignmentPattern: { x: alignment.alignmentPattern.x, y: alignment.alignmentPattern.y },\n      bottomLeft: {x: bottomLeft.x, y: bottomLeft.y },\n      dimension: alignment.dimension,\n      topLeft: {x: topLeft.x, y: topLeft.y },\n      topRight: {x: topRight.x, y: topRight.y },\n    });\n  }\n\n  // We normally use the center of the quads as the location of the tracking points, which is optimal for most cases and will account\n  // for a skew in the image. However, In some cases, a slight skew might not be real and instead be caused by image compression\n  // errors and/or low resolution. For those cases, we'd be better off centering the point exactly in the middle of the black area. We\n  // compute and return the location data for the naively centered points as it is little additional work and allows for multiple\n  // attempts at decoding harder images.\n  const midTopRight = recenterLocation(matrix, topRight);\n  const midTopLeft = recenterLocation(matrix, topLeft);\n  const midBottomLeft = recenterLocation(matrix, bottomLeft);\n  const centeredAlignment = findAlignmentPattern(matrix, alignmentPatternQuads, midTopRight, midTopLeft, midBottomLeft);\n  if (centeredAlignment) {\n    result.push({\n      alignmentPattern: { x: centeredAlignment.alignmentPattern.x, y: centeredAlignment.alignmentPattern.y },\n      bottomLeft: { x: midBottomLeft.x, y: midBottomLeft. y },\n      topLeft: { x: midTopLeft.x, y: midTopLeft. y },\n      topRight: { x: midTopRight.x, y: midTopRight. y },\n      dimension: centeredAlignment.dimension,\n    });\n  }\n\n  if (result.length === 0) {\n    return null;\n  }\n\n  return result;\n}\n\nfunction findAlignmentPattern(matrix: BitMatrix, alignmentPatternQuads: Quad[], topRight: Point, topLeft: Point, bottomLeft: Point) {\n  // Now that we've found the three finder patterns we can determine the blockSize and the size of the QR code.\n  // We'll use these to help find the alignment pattern but also later when we do the extraction.\n  let dimension: number;\n  let moduleSize: number;\n  try {\n    ({ dimension, moduleSize } = computeDimension(topLeft, topRight, bottomLeft, matrix));\n  } catch (e) {\n    return null;\n  }\n\n  // Now find the alignment pattern\n  const bottomRightFinderPattern = { // Best guess at where a bottomRight finder pattern would be\n    x: topRight.x - topLeft.x + bottomLeft.x,\n    y: topRight.y - topLeft.y + bottomLeft.y,\n  };\n  const modulesBetweenFinderPatterns = ((distance(topLeft, bottomLeft) + distance(topLeft, topRight)) / 2 / moduleSize);\n  const correctionToTopLeft = 1 - (3 / modulesBetweenFinderPatterns);\n  const expectedAlignmentPattern = {\n    x: topLeft.x + correctionToTopLeft * (bottomRightFinderPattern.x - topLeft.x),\n    y: topLeft.y + correctionToTopLeft * (bottomRightFinderPattern.y - topLeft.y),\n  };\n\n  const alignmentPatterns = alignmentPatternQuads\n    .map(q => {\n      const x = (q.top.startX + q.top.endX + q.bottom.startX + q.bottom.endX) / 4;\n      const y = (q.top.y + q.bottom.y + 1) / 2;\n      if (!matrix.get(Math.floor(x), Math.floor(y))) {\n        return;\n      }\n\n      const sizeScore = scorePattern({x: Math.floor(x), y: Math.floor(y)}, [1, 1, 1], matrix);\n      const score = sizeScore + distance({x, y}, expectedAlignmentPattern);\n      return { x, y, score };\n    })\n    .filter(v => !!v)\n    .sort((a, b) => a.score - b.score);\n\n  // If there are less than 15 modules between finder patterns it's a version 1 QR code and as such has no alignmemnt pattern\n  // so we can only use our best guess.\n  const alignmentPattern = modulesBetweenFinderPatterns >= 15 && alignmentPatterns.length ? alignmentPatterns[0] : expectedAlignmentPattern;\n\n  return { alignmentPattern, dimension };\n}\n", "import {binarize} from \"./binarizer\";\nimport {BitMatrix} from \"./BitMatrix\";\nimport {Chunks} from \"./decoder/decodeData\";\nimport {decode} from \"./decoder/decoder\";\nimport { Version } from \"./decoder/version\";\nimport {extract} from \"./extractor\";\nimport {locate, Point} from \"./locator\";\n\nexport interface QRCode {\n  binaryData: number[];\n  data: string;\n  chunks: Chunks;\n  version: number;\n  location: {\n    topRightCorner: Point;\n    topLeftCorner: Point;\n    bottomRightCorner: Point;\n    bottomLeftCorner: Point;\n\n    topRightFinderPattern: Point;\n    topLeftFinderPattern: Point;\n    bottomLeftFinderPattern: Point;\n\n    bottomRightAlignmentPattern?: Point;\n  };\n  matrix: BitMatrix;\n}\n\nfunction scan(matrix: BitMatrix): QRCode | null {\n  const locations = locate(matrix);\n  if (!locations) {\n    return null;\n  }\n\n  for (const location of locations) {\n    const extracted = extract(matrix, location);\n    const decoded = decode(extracted.matrix);\n    if (decoded) {\n      return {\n        binaryData: decoded.bytes,\n        data: decoded.text,\n        chunks: decoded.chunks,\n        version: decoded.version,\n        location: {\n          topRightCorner: extracted.mappingFunction(location.dimension, 0),\n          topLeftCorner: extracted.mappingFunction(0, 0),\n          bottomRightCorner: extracted.mappingFunction(location.dimension, location.dimension),\n          bottomLeftCorner: extracted.mappingFunction(0, location.dimension),\n\n          topRightFinderPattern: location.topRight,\n          topLeftFinderPattern: location.topLeft,\n          bottomLeftFinderPattern: location.bottomLeft,\n\n          bottomRightAlignmentPattern: location.alignmentPattern,\n        },\n        matrix: extracted.matrix,\n      };\n    }\n  }\n  return null;\n}\n\nexport interface Options {\n  inversionAttempts?: \"dontInvert\" | \"onlyInvert\" | \"attemptBoth\" | \"invertFirst\";\n  greyScaleWeights?: GreyscaleWeights;\n  canOverwriteImage?: boolean;\n}\n\nexport interface GreyscaleWeights {\n  red: number;\n  green: number;\n  blue: number;\n  useIntegerApproximation?: boolean;\n}\n\nconst defaultOptions: Options = {\n  inversionAttempts: \"attemptBoth\",\n  greyScaleWeights: {\n    red: 0.2126,\n    green: 0.7152,\n    blue: 0.0722,\n    useIntegerApproximation: false,\n  },\n  canOverwriteImage: true,\n};\n\nfunction mergeObject(target: any, src: any) {\n  Object.keys(src).forEach(opt => { // Sad implementation of Object.assign since we target es5 not es6\n    target[opt] = src[opt];\n  });\n}\n\nfunction jsQR(data: Uint8ClampedArray, width: number, height: number, providedOptions: Options = {}): QRCode | null {\n  const options = Object.create(null);\n  mergeObject(options, defaultOptions);\n  mergeObject(options, providedOptions);\n\n  const tryInvertedFirst = options.inversionAttempts === \"onlyInvert\" || options.inversionAttempts === \"invertFirst\";\n  const shouldInvert = options.inversionAttempts === \"attemptBoth\" || tryInvertedFirst;\n  const {binarized, inverted} = binarize(data, width, height, shouldInvert, options.greyScaleWeights,\n      options.canOverwriteImage);\n  let result = scan(tryInvertedFirst ? inverted : binarized);\n  if (!result && (options.inversionAttempts === \"attemptBoth\" || options.inversionAttempts === \"invertFirst\")) {\n    result = scan(tryInvertedFirst ? binarized : inverted);\n  }\n  return result;\n}\n\n(jsQR as any).default = jsQR;\nexport default jsQR;\n", "// @ts-ignore jsqr-es6 does not provide types currently\nimport jsQR from '../node_modules/jsqr-es6/dist/jsQR.js';\n\ntype GrayscaleWeights = {\n    red: number,\n    green: number,\n    blue: number,\n    useIntegerApproximation: boolean,\n};\n\nlet inversionAttempts: 'dontInvert' | 'onlyInvert' | 'attemptBoth' = 'dontInvert';\nlet grayscaleWeights: GrayscaleWeights = {\n    // weights for quick luma integer approximation (https://en.wikipedia.org/wiki/YUV#Full_swing_for_BT.601)\n    red: 77,\n    green: 150,\n    blue: 29,\n    useIntegerApproximation: true,\n};\n\nself.onmessage = event => {\n    const id = event['data']['id'];\n    const type = event['data']['type'];\n    const data = event['data']['data'];\n\n    switch (type) {\n        case 'decode':\n            decode(data, id);\n            break;\n        case 'grayscaleWeights':\n            setGrayscaleWeights(data);\n            break;\n        case 'inversionMode':\n            setInversionMode(data);\n            break;\n        case 'close':\n            // close after earlier messages in the event loop finished processing\n            self.close();\n            break;\n    }\n};\n\nfunction decode(data: { data: Uint8ClampedArray, width: number, height: number }, requestId: number): void {\n    const rgbaData = data['data'];\n    const width = data['width'];\n    const height = data['height'];\n    const result = jsQR(rgbaData, width, height, {\n        inversionAttempts: inversionAttempts,\n        greyScaleWeights: grayscaleWeights,\n    });\n    if (!result) {\n        (self as unknown as Worker).postMessage({\n            id: requestId,\n            type: 'qrResult',\n            data: null,\n        });\n        return;\n    }\n\n    (self as unknown as Worker).postMessage({\n        id: requestId,\n        type: 'qrResult',\n        data: result.data,\n        // equivalent to cornerPoints of native BarcodeDetector\n        cornerPoints: [\n            result.location.topLeftCorner,\n            result.location.topRightCorner,\n            result.location.bottomRightCorner,\n            result.location.bottomLeftCorner,\n        ],\n    });\n}\n\nfunction setGrayscaleWeights(data: GrayscaleWeights) {\n    // update grayscaleWeights in a closure compiler compatible fashion\n    grayscaleWeights.red = data['red'];\n    grayscaleWeights.green = data['green'];\n    grayscaleWeights.blue = data['blue'];\n    grayscaleWeights.useIntegerApproximation = data['useIntegerApproximation'];\n}\n\nfunction setInversionMode(inversionMode: 'original' | 'invert' | 'both') {\n    switch (inversionMode) {\n        case 'original':\n            inversionAttempts = 'dontInvert';\n            break;\n        case 'invert':\n            inversionAttempts = 'onlyInvert';\n            break;\n        case 'both':\n            inversionAttempts = 'attemptBoth';\n            break;\n        default:\n            throw new Error('Invalid inversion mode');\n    }\n}\n"], "mappings": ";;;mBASEA,MAAAA,IAAYC,OACV,IAAKC,gBACAC,IAAcC,KAAAA,CAAnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["constructor", "data", "width", "height", "length"]}