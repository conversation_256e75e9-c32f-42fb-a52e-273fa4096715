import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '@/lib/customSupabaseClient';

const SETTINGS_DB_KEY = 'gulfAcademySettings';

const SettingsContext = React.createContext();

const initialSettings = {
  aboutUs: '<h1>من نحن</h1><p>أكاديمية الخليج هي منصة تعليمية رائدة...</p>',
  termsAndPrivacy: '<h1>شروط الاستخدام</h1><p>هذه هي شروط الاستخدام...</p><h1>سياسة الخصوصية</h1><p>هذه هي سياسة الخصوصية...</p>',
  faqs: [
    { id: 1, question: 'كيف يمكنني حجز حصة؟', answer: 'يمكنك حجز حصة بسهولة من خلال لوحة التحكم الخاصة بك بعد تسجيل الدخول.' },
    { id: 2, question: 'هل يمكنني إلغاء الحجز؟', answer: 'نعم، يمكنك إلغاء الحجز قبل 24 ساعة من موعد الحصة.' },
  ],
  inquiries: [],
  pricingPackages: [
    { id: uuidv4(), name: 'باقة 4 ساعات', hours: 4, price: 100, features: ['ميزة 1', 'ميزة 2'] },
    { id: uuidv4(), name: 'باقة 8 ساعات', hours: 8, price: 180, features: ['ميزة 1', 'ميزة 2', 'ميزة 3'] },
    { id: uuidv4(), name: 'باقة 12 ساعة', hours: 12, price: 250, features: ['ميزة 1', 'ميزة 2', 'ميزة 3', 'ميزة 4'] },
    { id: uuidv4(), name: 'باقة 24 ساعة', hours: 24, price: 480, features: ['كل الميزات'] },
    { id: uuidv4(), name: 'باقة 36 ساعة', hours: 36, price: 680, features: ['كل الميزات', 'دعم إضافي'] },
  ],
  zoom: {
    connected: false,
    accountId: '',
    clientId: '',
    clientSecret: '',
  },
  promotionalVideo: {
    id: null,
    link: null,
    name: null,
  },
  academyLogoUrl: null,
};

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(() => {
    try {
      const storedSettings = localStorage.getItem(SETTINGS_DB_KEY);
      if (storedSettings) {
        const parsed = JSON.parse(storedSettings);
        const mergedSettings = {
          ...initialSettings,
          ...parsed,
          pricingPackages: parsed.pricingPackages || initialSettings.pricingPackages,
          zoom: { ...initialSettings.zoom, ...(parsed.zoom || {}) },
          promotionalVideo: parsed.promotionalVideo || initialSettings.promotionalVideo,
          academyLogoUrl: parsed.academyLogoUrl || initialSettings.academyLogoUrl,
        };
        // Clean up old onMeeting settings if they exist
        delete mergedSettings.onMeeting;
        return mergedSettings;
      }
    } catch (error) {
      console.error("Failed to parse settings from localStorage", error);
    }
    return initialSettings;
  });

  useEffect(() => {
    try {
      localStorage.setItem(SETTINGS_DB_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error("Failed to save settings to localStorage", error);
    }
  }, [settings]);

  const updateSettings = (newSettings) => {
    setSettings(prevSettings => {
      const updated = { ...prevSettings, ...newSettings, zoom: { ...prevSettings.zoom, ...newSettings.zoom }};
      return updated;
    });
  };

  const addInquiry = (inquiry) => {
    const newInquiries = [...settings.inquiries, { ...inquiry, id: inquiry.id || uuidv4() }];
    updateSettings({ inquiries: newInquiries });
  };
  
  const updateInquiry = (updatedInquiry) => {
    const newInquiries = settings.inquiries.map(inq => inq.id === updatedInquiry.id ? updatedInquiry : inq);
    updateSettings({ inquiries: newInquiries });
  };

  const uploadAcademyLogo = async (file) => {
      if (!file) return { error: { message: "No file provided" } };
      const filePath = `public/academy_logo.${file.name.split('.').pop()}`;

      const { error: deleteError } = await supabase.storage.from('study_materials').remove([`public/academy_logo.png`, `public/academy_logo.jpg`, `public/academy_logo.jpeg`, `public/academy_logo.svg`]);

      const { data, error } = await supabase.storage
          .from('study_materials')
          .upload(filePath, file, {
              cacheControl: '3600',
              upsert: true
          });
      
      if (error) {
          return { error };
      }
      
      const { data: publicUrlData } = supabase.storage
          .from('study_materials')
          .getPublicUrl(data.path);

      updateSettings({ academyLogoUrl: publicUrlData.publicUrl });
      return { data: publicUrlData };
  };


  const value = { settings, setSettings, updateSettings, addInquiry, updateInquiry, uploadAcademyLogo };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};