import { useToast } from '@/components/ui/use-toast';
import React from 'react';

export const useSocialShare = () => {
    const { toast } = useToast();

    const share = async ({ title, text, url, files }) => {
        if (navigator.share) {
            try {
                const shareData = {
                    title,
                    text,
                    url,
                };
                if (files && navigator.canShare({ files })) {
                    shareData.files = files;
                }
                await navigator.share(shareData);
                toast({ title: 'تمت المشاركة بنجاح!' });
            } catch (error) {
                console.error('Error sharing:', error);
                if (error.name !== 'AbortError') {
                    toast({ title: 'خطأ في المشاركة', description: 'لم يتم إكمال عملية المشاركة.', variant: 'destructive' });
                }
            }
        } else {
            const emailUrl = `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(text + '\n\n' + url)}`;
            const whatsappUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(title + '\n' + text + '\n' + url)}`;
            
            toast({
                title: 'المشاركة غير مدعومة',
                description: 'متصفحك لا يدعم المشاركة المباشرة. يمكنك استخدام الروابط التالية:',
                duration: 9000,
                action: (
                    <div className="flex flex-col gap-2">
                        <a href={emailUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400">مشاركة عبر البريد الإلكتروني</a>
                        <a href={whatsappUrl} target="_blank" rel="noopener noreferrer" className="text-green-400">مشاركة عبر واتساب</a>
                    </div>
                )
            });
        }
    };

    const shareImage = async (imageUrl, title, text) => {
        try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const file = new File([blob], 'card.png', { type: blob.type });

            share({
                title: title,
                text: text,
                files: [file],
            });
        } catch (error) {
            console.error('Error fetching image for sharing:', error);
            toast({
                title: 'خطأ في تحميل الصورة',
                description: 'لا يمكن مشاركة الصورة حالياً. حاول المشاركة بدون صورة.',
                variant: 'destructive',
            });
            share({ title, text, url: window.location.href });
        }
    };
    
    return { share, shareImage };
};