import React, { useState, useMemo } from 'react';
import { useBooking } from '@/contexts/BookingContext';
import { CheckCircle, Calendar as CalendarIcon, Printer } from 'lucide-react';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import AmiriFont from '@/assets/fonts/Amiri-Regular.js';

const FinishedLessonsContent = () => {
  const { bookings } = useBooking();
  const { toast } = useToast();
  const [dateRange, setDateRange] = useState({ from: null, to: null });
  const [filterType, setFilterType] = useState('all');

  const handleFilterChange = (value) => {
    setFilterType(value);
    const now = new Date();
    switch (value) {
      case 'today':
        setDateRange({ from: startOfDay(now), to: endOfDay(now) });
        break;
      case 'this_week':
        setDateRange({ from: startOfWeek(now, { locale: arSA }), to: endOfWeek(now, { locale: arSA }) });
        break;
      case 'this_month':
        setDateRange({ from: startOfMonth(now), to: endOfMonth(now) });
        break;
      case 'this_year':
        setDateRange({ from: startOfYear(now), to: endOfYear(now) });
        break;
      case 'all':
      default:
        setDateRange({ from: null, to: null });
        break;
    }
  };

  const finishedSessions = useMemo(() => {
    const allFinished = bookings
      .flatMap(b => 
        (b.sessions || [])
          .filter(s => s.status === 'completed')
          .map(s => ({
            ...s,
            bookingId: b.id,
            studentNames: b.studentEmails?.map(email => b.studentNames[email]).join(', ') || b.studentName,
            teacherName: b.teacherName,
            subject: b.subjects.map(sub => sub.subject).join(', '),
          }))
      );

    if (!dateRange.from || !dateRange.to) {
      return allFinished.sort((a, b) => parseISO(b.date) - parseISO(a.date));
    }

    const filtered = allFinished.filter(session => {
      const sessionDate = parseISO(session.date);
      return sessionDate >= dateRange.from && sessionDate <= dateRange.to;
    });
    return filtered.sort((a, b) => parseISO(b.date) - parseISO(a.date));
  }, [bookings, dateRange]);

  const handleExportPDF = () => {
    if (finishedSessions.length === 0) {
      toast({ title: "لا توجد بيانات للتصدير", variant: "destructive" });
      return;
    }
    const doc = new jsPDF();
    doc.addFileToVFS('Amiri-Regular.ttf', AmiriFont);
    doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
    doc.setFont('Amiri');
    doc.setR2L(true);
    doc.setFontSize(18);
    doc.text("تقرير الدروس المنجزة", 105, 15, { align: 'center' });

    const tableColumn = ["المادة", "الطلاب", "المعلم", "تاريخ الإنجاز"];
    const tableRows = [];

    finishedSessions.forEach(session => {
      const sessionData = [
        session.subject,
        session.studentNames,
        session.teacherName,
        format(parseISO(session.date), 'yyyy/MM/dd', { locale: arSA }),
      ];
      tableRows.push(sessionData);
    });

    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 25,
      styles: { font: 'Amiri', halign: 'center' },
      headStyles: { fillColor: [22, 160, 133] },
      didParseCell: function (data) {
        data.cell.text = [data.cell.text.join(' ').split(' ').reverse().join(' ')];
      },
    });
    doc.save("الدروس_المنجزة.pdf");
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex flex-col sm:flex-row items-center justify-between mb-6 gap-4">
          <h3 className="text-2xl font-bold text-white flex items-center gap-3">
            <CheckCircle className="text-green-400" />
            الدروس المنتهية
          </h3>
          <div className="flex items-center gap-2 flex-wrap justify-center">
            <Select value={filterType} onValueChange={handleFilterChange}>
              <SelectTrigger className="w-[180px] bg-secondary border-border text-white">
                <SelectValue placeholder="فلترة حسب التاريخ" />
              </SelectTrigger>
              <SelectContent className="bg-secondary border-border text-white">
                <SelectItem value="all">كل الأوقات</SelectItem>
                <SelectItem value="today">اليوم</SelectItem>
                <SelectItem value="this_week">هذا الأسبوع</SelectItem>
                <SelectItem value="this_month">هذا الشهر</SelectItem>
                <SelectItem value="this_year">هذه السنة</SelectItem>
              </SelectContent>
            </Select>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[280px] justify-start text-left font-normal bg-secondary border-border text-white hover:bg-white/10 hover:text-white">
                  <CalendarIcon className="ml-2 h-4 w-4" />
                  {dateRange.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y", { locale: arSA })} - {format(dateRange.to, "LLL dd, y", { locale: arSA })}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y", { locale: arSA })
                    )
                  ) : (
                    <span>اختر نطاقاً زمنياً</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 bg-secondary border-border text-white" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange?.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                  locale={arSA}
                  classNames={{
                    day_selected: "bg-primary text-primary-foreground hover:bg-primary/80",
                    day: "text-white",
                    head_cell: "text-white/70",
                    caption_label: "text-white",
                    nav_button: "text-white hover:bg-white/10",
                    day_disabled: "text-white/30",
                  }}
                />
              </PopoverContent>
            </Popover>
            <Button onClick={handleExportPDF} variant="outline" className="bg-primary/20 border-primary text-primary hover:bg-primary/30">
              <Printer className="ml-2 h-4 w-4" />
              تصدير PDF
            </Button>
          </div>
        </div>
        {finishedSessions.length === 0 ? (
          <div className="text-center py-12 text-white/70">
            <p className="mt-4">لا توجد دروس منتهية في النطاق الزمني المحدد.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="py-3 px-4 text-white font-medium">المادة</th>
                  <th className="py-3 px-4 text-white font-medium">الطلاب</th>
                  <th className="py-3 px-4 text-white font-medium">المعلم</th>
                  <th className="py-3 px-4 text-white font-medium">تاريخ الإنجاز</th>
                </tr>
              </thead>
              <tbody>
                {finishedSessions.map((session) => (
                  <tr key={session.sessionId} className="border-b border-white/10">
                    <td className="py-3 px-4 text-white">{session.subject}</td>
                    <td className="py-3 px-4 text-white/70">{session.studentNames}</td>
                    <td className="py-3 px-4 text-white/70">{session.teacherName}</td>
                    <td className="py-3 px-4 text-white/70">{format(parseISO(session.date), 'EEEE, d MMMM yyyy', { locale: arSA })}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinishedLessonsContent;