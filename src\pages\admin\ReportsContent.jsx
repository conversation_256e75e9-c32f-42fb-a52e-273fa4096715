
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/customSupabaseClient';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { Bar<PERSON><PERSON>2, Filter, Share2, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSocialShare } from '@/lib/socialShare.jsx';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

const ReportsContent = () => {
    const { users } = useAuth();
    const { toast } = useToast();
    const { share } = useSocialShare();
    const [logs, setLogs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState({ student: 'all', teacher: 'all' });

    const students = useMemo(() => users.filter(u => u.userType === 'student'), [users]);
    const teachers = useMemo(() => users.filter(u => u.userType === 'teacher'), [users]);

    const fetchLogs = useCallback(async () => {
        setLoading(true);
        const { data, error } = await supabase.from('session_logs').select('*');
        if (error) {
            toast({ title: 'خطأ', description: 'فشل في جلب سجلات الحصص.', variant: 'destructive' });
        } else {
            setLogs(data);
        }
        setLoading(false);
    }, [toast]);

    useEffect(() => {
        fetchLogs();
    }, [fetchLogs]);

    const filteredLogs = useMemo(() => {
        return logs.filter(log => {
            const studentMatch = filters.student === 'all' || log.student_email === filters.student;
            const teacherMatch = filters.teacher === 'all' || log.teacher_email === filters.teacher;
            return studentMatch && teacherMatch;
        });
    }, [logs, filters]);

    const getStudentName = (email) => students.find(s => s.email === email)?.name || email;
    const getTeacherName = (email) => teachers.find(t => t.email === email)?.name || email;

    const handleShare = () => {
        const reportText = `تقرير الحصص من أكاديمية الخليج:\n\n${filteredLogs.map(log => 
            `- الطالب: ${getStudentName(log.student_email)}, المعلم: ${getTeacherName(log.teacher_email)}, المدة: ${log.actual_session_duration_minutes || 0} دقيقة`
        ).join('\n')}`;
        share({title: 'تقرير الحصص', text: reportText});
    };

    const handleDeleteReport = async (logId) => {
        const { error } = await supabase.from('session_logs').delete().eq('id', logId);
        if (error) {
            toast({ title: 'فشل حذف التقرير', description: error.message, variant: 'destructive' });
        } else {
            toast({ title: 'تم حذف التقرير بنجاح' });
            fetchLogs();
        }
    };

    return (
        <div className="space-y-8">
            <div className="glass-effect rounded-xl p-6">
                <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
                    <h3 className="text-2xl font-bold text-white flex items-center gap-3"><BarChart2 /> تقارير الحصص</h3>
                    <div className="flex gap-2">
                        <Button onClick={handleShare} variant="outline" className="border-border text-white hover:bg-secondary/50"><Share2 className="h-4 w-4 ml-2" /> مشاركة</Button>
                    </div>
                </div>

                <div className="bg-secondary/50 rounded-lg p-4 mb-6 flex flex-wrap gap-4 items-center">
                    <Filter className="h-5 w-5 text-primary" />
                    <div className="flex-1 min-w-[200px]">
                        <Select value={filters.student} onValueChange={(v) => setFilters(f => ({...f, student: v}))}>
                            <SelectTrigger><SelectValue placeholder="فلترة حسب الطالب..." /></SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">كل الطلاب</SelectItem>
                                {students.map(s => <SelectItem key={s.email} value={s.email}>{s.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="flex-1 min-w-[200px]">
                        <Select value={filters.teacher} onValueChange={(v) => setFilters(f => ({...f, teacher: v}))}>
                            <SelectTrigger><SelectValue placeholder="فلترة حسب المعلم..." /></SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">كل المعلمين</SelectItem>
                                {teachers.map(t => <SelectItem key={t.email} value={t.email}>{t.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="overflow-x-auto">
                    <table className="w-full text-right whitespace-nowrap">
                        <thead>
                            <tr className="border-b border-white/20">
                                <th className="p-3 text-white font-medium">الطالب</th>
                                <th className="p-3 text-white font-medium">المعلم</th>
                                <th className="p-3 text-white font-medium">المادة/الصف/الدولة</th>
                                <th className="p-3 text-white font-medium">دخول الطالب</th>
                                <th className="p-3 text-white font-medium">خروج الطالب</th>
                                <th className="p-3 text-white font-medium">دخول المعلم</th>
                                <th className="p-3 text-white font-medium">خروج المعلم</th>
                                <th className="p-3 text-white font-medium">المدة الفعلية (د)</th>
                                <th className="p-3 text-white font-medium">ساعات الاشتراك</th>
                                <th className="p-3 text-white font-medium">المنجز</th>
                                <th className="p-3 text-white font-medium">المتبقي</th>
                                <th className="p-3 text-white font-medium">إجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                <tr><td colSpan="12" className="text-center p-8 text-white/70">جاري تحميل التقارير...</td></tr>
                            ) : filteredLogs.length === 0 ? (
                                <tr><td colSpan="12" className="text-center p-8 text-white/70">لا توجد بيانات لعرضها.</td></tr>
                            ) : (
                                filteredLogs.map(log => (
                                    <tr key={log.id} className="border-b border-white/10 text-sm hover:bg-secondary/30">
                                        <td className="p-3">{getStudentName(log.student_email)}</td>
                                        <td className="p-3">{getTeacherName(log.teacher_email)}</td>
                                        <td className="p-3">{log.subject || 'N/A'} / {log.grade || 'N/A'} / {log.country || 'N/A'}</td>
                                        <td className="p-3">{log.student_joined_at ? new Date(log.student_joined_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.student_left_at ? new Date(log.student_left_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.teacher_joined_at ? new Date(log.teacher_joined_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.teacher_left_at ? new Date(log.teacher_left_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3 text-primary font-semibold">{log.actual_session_duration_minutes || 0}</td>
                                        <td className="p-3">{log.student_subscription_total_hours || 0}</td>
                                        <td className="p-3 text-green-400">{log.student_subscription_completed_hours || 0}</td>
                                        <td className="p-3 text-yellow-400">{log.student_subscription_remaining_hours || 0}</td>
                                        <td className="p-3">
                                            <AlertDialog>
                                                <AlertDialogTrigger asChild>
                                                    <Button variant="destructive" size="icon" className="h-8 w-8">
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </AlertDialogTrigger>
                                                <AlertDialogContent className="bg-secondary border-border text-white">
                                                    <AlertDialogHeader>
                                                        <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
                                                        <AlertDialogDescription>
                                                            سيتم حذف هذا التقرير بشكل نهائي. لا يمكن التراجع عن هذا الإجراء.
                                                        </AlertDialogDescription>
                                                    </AlertDialogHeader>
                                                    <AlertDialogFooter>
                                                        <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                                        <AlertDialogAction onClick={() => handleDeleteReport(log.id)} className="bg-destructive hover:bg-destructive/90">
                                                            حذف
                                                        </AlertDialogAction>
                                                    </AlertDialogFooter>
                                                </AlertDialogContent>
                                            </AlertDialog>
                                        </td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default ReportsContent;
