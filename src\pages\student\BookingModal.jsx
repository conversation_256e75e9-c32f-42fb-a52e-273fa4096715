import React, { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { useCountry } from '@/contexts/CountryContext';
import { Book, User, Clock, Calendar as CalendarIcon, CheckCircle, PlusCircle, Trash2, MapPin, GraduationCap, Hash, ShieldAlert, UploadCloud, Users, Search } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { supabase } from '@/lib/customSupabaseClient';
import { v4 as uuidv4 } from 'uuid';
import { ScrollArea } from '@/components/ui/scroll-area';

const StudentMultiSelect = ({ options, selected, onChange, placeholder }) => {
    const [search, setSearch] = useState('');
    const filteredOptions = options.filter(option =>
        option.label.toLowerCase().includes(search.toLowerCase())
    );

    return (
        <div className="border border-border rounded-md p-2 space-y-2 bg-white/5">
            <div className="relative">
                <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-white/50" />
                <Input
                    type="text"
                    placeholder={placeholder}
                    value={search}
                    onChange={e => setSearch(e.target.value)}
                    className="bg-white/10 pr-10"
                />
            </div>
            <ScrollArea className="h-40">
                <div className="space-y-1 p-2">
                    {filteredOptions.map(option => (
                        <div key={option.value} className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                                id={`student-${option.value}`}
                                checked={selected.includes(option.value)}
                                onCheckedChange={(checked) => {
                                    const newSelected = checked
                                        ? [...selected, option.value]
                                        : selected.filter(val => val !== option.value);
                                    onChange(newSelected);
                                }}
                            />
                            <Label htmlFor={`student-${option.value}`} className="font-normal cursor-pointer w-full">
                                {option.label}
                            </Label>
                        </div>
                    ))}
                </div>
            </ScrollArea>
        </div>
    );
};


const BookingModal = ({ setIsOpen, existingBooking, isAdminEditing = false }) => {
  const { toast } = useToast();
  const { user, teachers, students: allStudents } = useAuth();
  const { createBookingRequest, updateBooking } = useBooking();
  const { countries } = useCountry();

  const [studentType, setStudentType] = useState('school');
  const [countryId, setCountryId] = useState('');
  const [grade, setGrade] = useState('');
  const [subject, setSubject] = useState('');
  const [totalHours, setTotalHours] = useState('');
  const [schedule, setSchedule] = useState([]);
  const [studyMaterialFile, setStudyMaterialFile] = useState(null);
  const [studyMaterialUrl, setStudyMaterialUrl] = useState('');
  const [teacherEmail, setTeacherEmail] = useState('');
  const [studentEmails, setStudentEmails] = useState([]);
  
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showTermsDialog, setShowTermsDialog] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (existingBooking) {
        setStudentType(existingBooking.studentType || 'school');
        setCountryId(existingBooking.country?.id || '');
        setGrade(existingBooking.grade || '');
        setSubject(existingBooking.subjects?.[0]?.subject || '');
        setTotalHours(existingBooking.totalHours || '');
        setSchedule(existingBooking.schedule || []);
        setStudyMaterialUrl(existingBooking.studyMaterialUrl || '');
        setTeacherEmail(existingBooking.teacherEmail || '');
        setStudentEmails(existingBooking.studentEmails || [existingBooking.studentEmail]);
    } else if (user) {
        setStudentEmails([user.email]);
    }
  }, [existingBooking, user]);

  const handleAddSession = () => setSchedule([...schedule, { date: '', time: '' }]);
  const handleRemoveSession = (index) => setSchedule(schedule.filter((_, i) => i !== index));

  const handleScheduleChange = (index, field, value) => {
    const newSchedule = [...schedule];
    newSchedule[index][field] = value;
    if (field === 'date') newSchedule[index].time = '';
    setSchedule(newSchedule);
  };

  const handleFileUpload = async (file) => {
    if (!file) return null;
    setIsUploading(true);
    try {
        const fileExt = file.name.split('.').pop();
        const fileName = `${uuidv4()}.${fileExt}`;
        const filePath = `${user.id}/${fileName}`;

        const { error: uploadError } = await supabase.storage
            .from('study_materials')
            .upload(filePath, file);

        if (uploadError) throw uploadError;

        const { data } = supabase.storage
            .from('study_materials')
            .getPublicUrl(filePath);
        
        toast({ title: "✅ تم رفع الملف بنجاح" });
        return data.publicUrl;
    } catch (error) {
        console.error('Error uploading file:', error);
        toast({ title: "❌ فشل رفع الملف", description: error.message, variant: "destructive" });
        return null;
    } finally {
        setIsUploading(false);
    }
  };

  const handleConfirmBooking = async () => {
    if (isAdminEditing && studentEmails.length > 4) {
        toast({ title: "خطأ", description: "لا يمكن إضافة أكثر من 4 طلاب للحصة الواحدة.", variant: "destructive" });
        return;
    }

    let finalStudyMaterialUrl = studyMaterialUrl;
    if (studyMaterialFile) {
        const uploadedUrl = await handleFileUpload(studyMaterialFile);
        if (!uploadedUrl) return; // Stop if upload fails
        finalStudyMaterialUrl = uploadedUrl;
    }

    const selectedCountry = countries.find(c => c.id === countryId);
    const studentNames = studentEmails.reduce((acc, email) => {
        const student = allStudents.find(s => s.email === email);
        if (student) acc[email] = student.name;
        return acc;
    }, {});

    const bookingDetails = {
      studentEmail: studentEmails[0], // Primary student
      studentName: studentNames[studentEmails[0]],
      studentEmails,
      studentNames,
      studentType,
      country: studentType === 'school' ? selectedCountry : null,
      grade: studentType === 'school' ? grade : null,
      subjects: [{ subject }],
      totalHours: parseInt(totalHours, 10),
      schedule,
      studyMaterialUrl: studentType === 'university' ? finalStudyMaterialUrl : null,
      teacherEmail: isAdminEditing ? teacherEmail : '',
      teacherName: isAdminEditing ? teachers.find(t => t.email === teacherEmail)?.name : '',
    };

    if (isAdminEditing && existingBooking) {
        updateBooking(existingBooking.id, bookingDetails);
        toast({ title: "✅ تم تعديل الحجز بنجاح" });
    } else {
        createBookingRequest(bookingDetails);
        toast({ title: "✅ تم إرسال طلب الحجز", description: "سيتم مراجعته من قبل الإدارة وتحديد معلم." });
    }
    setShowTermsDialog(false);
    setIsOpen(false);
  };

  const handleSubmit = () => {
    if (isAdminEditing) {
      handleConfirmBooking();
    } else {
      setShowTermsDialog(true);
    }
  };

  const isFormValid = useMemo(() => {
    if (studentType === 'school') {
        return countryId && subject && grade && totalHours && schedule.length > 0 && schedule.every(s => s.date && s.time);
    }
    if (studentType === 'university') {
        return subject && totalHours && schedule.length > 0 && schedule.every(s => s.date && s.time);
    }
    return false;
  }, [studentType, countryId, subject, grade, totalHours, schedule]);

  const attendancePolicyHTML = `
    <h3 class="font-bold text-lg mb-2">الالتزام بمواعيد الحصص الدراسية</h3>
    <ul class="list-decimal list-inside space-y-2">
      <li>يلتزم الطالب بالحضور في الموعد المحدد لكل حصة دراسية مباشرة (Live Class).</li>
      <li>يُسمح بتأخير أقصاه 10 دقائق من بدء الحصة، بعد ذلك يُعتبر الطالب "متأخرًا" وتحسب من وقت الحصة.</li>
      <li>إذا تجاوز التأخير 15 دقيقة بدون إشعار أو عذر مسبق، يُعتبر "غائبًا" ويُمنع من دخول الحصة.</li>
      <li>في حال تكرار الغياب أو التأخير دون عذر مقبول لأكثر من مرتين في الشهر، فإن المنصة تحتفظ بحقها في:
        <ul class="list-disc list-inside mt-2 mr-4 space-y-1">
          <li>عدم تعويض الحصص الفائتة.</li>
          <li>احتساب الحصة ضمن الاشتراك الشهري سواء تم حضورها أم لا.</li>
          <li>تجميد جزئي أو كلي للاشتراك بعد إخطار الطالب أو ولي أمره.</li>
        </ul>
      </li>
    </ul>
  `;

  const studentOptions = allStudents.map(s => ({ value: s.email, label: s.name }));

  return (
    <>
    <DialogContent className="bg-secondary border-border text-white max-w-3xl">
      <DialogHeader>
        <DialogTitle className="text-2xl">{isAdminEditing ? 'تعديل الحجز' : 'حجز جديد'}</DialogTitle>
        <DialogDescription>املأ التفاصيل التالية لتقديم طلب الحجز.</DialogDescription>
      </DialogHeader>
      <div className="py-4 space-y-6 max-h-[70vh] overflow-y-auto pr-2">
        
        <RadioGroup defaultValue={studentType} onValueChange={setStudentType} className="flex gap-4">
            <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="school" id="r1" />
                <Label htmlFor="r1">طالب مدرسة</Label>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
                <RadioGroupItem value="university" id="r2" />
                <Label htmlFor="r2">طالب جامعي</Label>
            </div>
        </RadioGroup>

        {isAdminEditing && (
            <div>
                <Label><Users className="inline h-4 w-4 ml-1"/>الطلاب المشاركون (حد أقصى 4)</Label>
                <StudentMultiSelect
                    options={studentOptions}
                    selected={studentEmails}
                    onChange={setStudentEmails}
                    placeholder="ابحث عن الطلاب لإضافتهم..."
                />
                {studentEmails.length > 4 && <p className="text-red-500 text-xs mt-1">لا يمكن اختيار أكثر من 4 طلاب.</p>}
            </div>
        )}

        {studentType === 'school' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div><Label><MapPin className="inline h-4 w-4 ml-1"/>الدولة</Label><Select value={countryId} onValueChange={setCountryId}><SelectTrigger className="bg-white/10"><SelectValue placeholder="اختر الدولة..." /></SelectTrigger><SelectContent className="bg-secondary text-white">{countries.map(c => <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>)}</SelectContent></Select></div>
                <div><Label><GraduationCap className="inline h-4 w-4 ml-1"/>الصف</Label><Input value={grade} onChange={(e) => setGrade(e.target.value)} className="bg-white/10" placeholder="اكتب المرحلة الدراسية/الصف" /></div>
            </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div><Label><Book className="inline h-4 w-4 ml-1"/>المادة المطلوبة</Label><Input value={subject} onChange={(e) => setSubject(e.target.value)} className="bg-white/10" placeholder="مثال: رياضيات، فيزياء..." /></div>
            <div><Label><Hash className="inline h-4 w-4 ml-1"/>عدد ساعات الحجز</Label><Input type="number" value={totalHours} onChange={(e) => setTotalHours(e.target.value)} className="bg-white/10" placeholder="مثال: 8" /></div>
        </div>

        {isAdminEditing && (
            <div>
                <Label><User className="inline h-4 w-4 ml-1"/>تحديد المعلم</Label>
                <Select value={teacherEmail} onValueChange={setTeacherEmail}>
                    <SelectTrigger className="bg-white/10"><SelectValue placeholder="اختر المعلم..." /></SelectTrigger>
                    <SelectContent className="bg-secondary text-white">{teachers.map(t => <SelectItem key={t.email} value={t.email}>{t.name}</SelectItem>)}</SelectContent>
                </Select>
            </div>
        )}

        <div className="space-y-3">
            <h4 className="font-semibold text-primary">الأوقات المفضلة</h4>
            {schedule.map((s, index) => (
                <div key={index} className="flex gap-2 items-end bg-white/5 p-3 rounded-lg">
                    <div className="flex-1"><Label>التاريخ</Label><Input type="date" value={s.date} onChange={e => handleScheduleChange(index, 'date', e.target.value)} className="bg-white/10" /></div>
                    <div className="flex-1"><Label>الوقت</Label><Input type="time" value={s.time} onChange={e => handleScheduleChange(index, 'time', e.target.value)} className="bg-white/10" /></div>
                    <Button variant="destructive" size="icon" onClick={() => handleRemoveSession(index)}><Trash2 className="h-4 w-4" /></Button>
                </div>
            ))}
            <Button variant="outline" onClick={handleAddSession} className="w-full border-dashed"><PlusCircle className="h-4 w-4 ml-2" />إضافة وقت مفضل آخر</Button>
        </div>

        {studentType === 'university' && (
            <div>
                <Label><UploadCloud className="inline h-4 w-4 ml-1"/>رفع المادة الدراسية (اختياري)</Label>
                <Input 
                    type="file" 
                    onChange={(e) => setStudyMaterialFile(e.target.files[0])} 
                    className="bg-white/10 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                />
                {studyMaterialUrl && !studyMaterialFile && (
                    <p className="text-xs text-green-400 mt-1">تم رفع ملف سابقاً. يمكنك اختيار ملف جديد لاستبداله.</p>
                )}
                <p className="text-xs text-white/60 mt-1">يمكنك رفع ملفاتك ليطلع عليها المعلم.</p>
            </div>
        )}

      </div>
      <DialogFooter>
        <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
        <Button onClick={handleSubmit} disabled={!isFormValid || isUploading || (isAdminEditing && studentEmails.length > 4)}>
            {isUploading ? 'جاري الرفع...' : <><CheckCircle className="ml-2 h-4 w-4"/> {isAdminEditing ? 'حفظ التعديلات' : 'إرسال طلب الحجز'}</>}
        </Button>
      </DialogFooter>
    </DialogContent>

    <Dialog open={showTermsDialog} onOpenChange={setShowTermsDialog}>
        <DialogContent className="bg-secondary border-border text-white">
            <DialogHeader>
                <DialogTitle className="flex items-center gap-2"><ShieldAlert className="text-yellow-400" /> شروط الحضور والمواظبة</DialogTitle>
                <DialogDescription>الرجاء قراءة الشروط التالية والموافقة عليها للمتابعة.</DialogDescription>
            </DialogHeader>
            <div className="prose prose-invert max-h-[50vh] overflow-y-auto p-4 my-4 bg-white/5 rounded-md border border-white/10"
                 dangerouslySetInnerHTML={{ __html: attendancePolicyHTML }}>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
                <Checkbox id="terms" checked={termsAccepted} onCheckedChange={setTermsAccepted} />
                <Label htmlFor="terms" className="cursor-pointer">قرأت الشروط وأوافق عليها</Label>
            </div>
            <DialogFooter>
                <Button variant="outline" onClick={() => setShowTermsDialog(false)}>إلغاء</Button>
                <Button onClick={handleConfirmBooking} disabled={!termsAccepted || isUploading}>
                    {isUploading ? 'جاري الرفع...' : <><CheckCircle className="ml-2 h-4 w-4"/> موافق وإرسال الطلب</>}
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
};

export default BookingModal;