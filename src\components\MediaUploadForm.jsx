import React, { useState, useRef, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Upload, Loader2, ChevronDown } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useCountry } from '@/contexts/CountryContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Checkbox } from '@/components/ui/checkbox';
import { supabase } from '@/lib/customSupabaseClient';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogTrigger } from '@/components/ui/dialog';
import { v4 as uuidv4 } from 'uuid';

const subjectsList = [ "التربية الإسلامية", "اللغة العربية", "اللغة الإنجليزية", "الرياضيات", "العلوم", "الدراسات الاجتماعية", "الجغرافيا", "التاريخ", "الفيزياء", "الكيمياء", "الأحياء", "مادة أخرى" ];
const grades = Array.from({ length: 12 }, (_, i) => `الصف ${i + 1}`);
const fileTypes = [ { value: "فيديو", label: "فيديو" }, { value: "ملف PDF", label: "ملف PDF" }, { value: "صورة", label: "صورة" }, { value: "صوت", label: "صوت" }, { value: "عرض تقديمي", label: "عرض تقديمي" }, { value: "مستند", label: "مستند" } ];

const MediaUploadForm = ({ onUploadSuccess }) => {
    const { toast } = useToast();
    const { user, students: allStudents } = useAuth();
    const { countries } = useCountry();
    const [isUploading, setIsUploading] = useState(false);
    const [fileToUpload, setFileToUpload] = useState(null);
    const [formData, setFormData] = useState({
        lesson_title: '',
        country_id: '',
        subject: '',
        other_subject_name: '',
        grade: '',
        file_type: '',
        assignedStudents: [],
    });
    const fileInputRef = useRef(null);

    const studentsInCountry = useMemo(() => {
        if (!formData.country_id) return [];
        return allStudents.filter(s => s.countryId === formData.country_id);
    }, [allStudents, formData.country_id]);


    const handleInputChange = (field, value) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };
    
    const handleCountryChange = (value) => {
         setFormData(prev => ({ ...prev, country_id: value, assignedStudents: [] }));
    }

    const handleStudentSelection = (studentEmail) => {
        setFormData(prev => ({
            ...prev,
            assignedStudents: prev.assignedStudents.includes(studentEmail)
                ? prev.assignedStudents.filter(s => s !== studentEmail)
                : [...prev.assignedStudents, studentEmail]
        }));
    };

    const handleFileChange = (event) => {
        if (event.target.files.length > 0) {
            setFileToUpload(event.target.files[0]);
        }
    };

    const resetForm = () => {
        setFormData({
            lesson_title: '',
            country_id: '',
            subject: '',
            other_subject_name: '',
            grade: '',
            file_type: '',
            assignedStudents: [],
        });
        setFileToUpload(null);
        if(fileInputRef.current) fileInputRef.current.value = "";
    }

    const handleSubmit = async () => {
        if (!fileToUpload) {
            toast({ title: 'الرجاء اختيار ملف أولاً', variant: 'destructive' });
            return;
        }
        if (!formData.lesson_title || !formData.country_id || !formData.subject || !formData.grade || !formData.file_type) {
            toast({ title: 'بيانات غير مكتملة', description: 'يرجى ملء جميع الحقول المطلوبة.', variant: 'destructive' });
            return;
        }
        if (formData.subject === 'مادة أخرى' && !formData.other_subject_name) {
            toast({ title: 'بيانات غير مكتملة', description: 'الرجاء كتابة اسم المادة الأخرى.', variant: 'destructive' });
            return;
        }

        setIsUploading(true);

        try {
            const fileExt = fileToUpload.name.split('.').pop();
            const fileName = `${uuidv4()}.${fileExt}`;
            const filePath = `public/${fileName}`;

            const { error: uploadError } = await supabase.storage
                .from('study_materials')
                .upload(filePath, fileToUpload);

            if (uploadError) {
                throw new Error(`Supabase storage error: ${uploadError.message}`);
            }

            const { data: { publicUrl } } = supabase.storage
                .from('study_materials')
                .getPublicUrl(filePath);
            
            const materialData = {
                drive_file_id: filePath, // Using this field to store the path in Supabase Storage
                view_link: publicUrl,
                name: fileToUpload.name,
                lesson_title: formData.lesson_title,
                country_id: formData.country_id,
                subject: formData.subject,
                other_subject_name: formData.subject === 'مادة أخرى' ? formData.other_subject_name : null,
                grade: formData.grade,
                file_type: formData.file_type,
                uploaded_by_email: user.email,
            };

            const { data: savedMaterial, error: saveError } = await supabase
                .from('study_materials')
                .insert(materialData)
                .select()
                .single();
            
            if (saveError) {
                throw new Error(`Supabase save error: ${saveError.message}`);
            }

            if (formData.assignedStudents.length > 0) {
                const newAssignments = formData.assignedStudents.map(email => ({
                    material_id: savedMaterial.id,
                    student_email: email,
                    assigned_by_email: user.email,
                }));
                const { error: insertError } = await supabase.from('material_assignments').insert(newAssignments);
                if (insertError) {
                    toast({ title: 'فشل تخصيص الطلاب للمادة', variant: 'destructive' });
                }
            }

            toast({ title: `🎉 تم رفع المادة بنجاح!` });
            if (onUploadSuccess) onUploadSuccess();
            resetForm();
            setIsUploading(false);
            return true;

        } catch (error) {
            console.error("Upload process failed:", error);
            toast({ title: '❌ فشل رفع الملف', description: error.message, variant: 'destructive' });
            setIsUploading(false);
        }
    };

    return (
        <Dialog onOpenChange={(isOpen) => !isOpen && resetForm()}>
            <DialogTrigger asChild>
                <Button className="w-full md:w-auto">
                    <Upload className="h-4 w-4 ml-2" />
                    رفع مادة جديدة
                </Button>
            </DialogTrigger>
            <DialogContent className="bg-secondary border-border text-white max-w-2xl">
                <DialogHeader>
                    <DialogTitle>رفع مادة تعليمية جديدة</DialogTitle>
                    <DialogDescription>املأ التفاصيل أدناه وقم بإرفاق الملف.</DialogDescription>
                </DialogHeader>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                    <div>
                        <Label className="text-white/80 mb-2 block">الدولة</Label>
                        <Select value={formData.country_id} onValueChange={handleCountryChange}>
                            <SelectTrigger><SelectValue placeholder="اختر الدولة..." /></SelectTrigger>
                            <SelectContent>{countries.map(c => <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>)}</SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label className="text-white/80 mb-2 block">الصف الدراسي</Label>
                        <Select value={formData.grade} onValueChange={(v) => handleInputChange('grade', v)}>
                            <SelectTrigger><SelectValue placeholder="اختر الصف..." /></SelectTrigger>
                            <SelectContent>{grades.map(g => <SelectItem key={g} value={g}>{g}</SelectItem>)}</SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label className="text-white/80 mb-2 block">المادة</Label>
                        <Select value={formData.subject} onValueChange={(v) => handleInputChange('subject', v)}>
                            <SelectTrigger><SelectValue placeholder="اختر المادة..." /></SelectTrigger>
                            <SelectContent>{subjectsList.map(s => <SelectItem key={s} value={s}>{s}</SelectItem>)}</SelectContent>
                        </Select>
                    </div>
                    {formData.subject === 'مادة أخرى' && (
                         <div>
                            <Label className="text-white/80 mb-2 block">اسم المادة الأخرى</Label>
                            <Input value={formData.other_subject_name} onChange={(e) => handleInputChange('other_subject_name', e.target.value)} placeholder="اكتب اسم المادة..." />
                         </div>
                    )}
                    <div>
                        <Label className="text-white/80 mb-2 block">نوع الملف</Label>
                        <Select value={formData.file_type} onValueChange={(v) => handleInputChange('file_type', v)}>
                            <SelectTrigger><SelectValue placeholder="اختر نوع الملف..." /></SelectTrigger>
                            <SelectContent>{fileTypes.map(ft => <SelectItem key={ft.value} value={ft.value}>{ft.label}</SelectItem>)}</SelectContent>
                        </Select>
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="lessonTitle" className="text-white/80 mb-2 block">عنوان الدرس</Label>
                      <Input id="lessonTitle" value={formData.lesson_title} onChange={(e) => handleInputChange('lesson_title', e.target.value)} placeholder="أدخل عنوان الدرس..." />
                    </div>
                    <div className="md:col-span-2">
                        <Label className="text-white/80 mb-2 block">تخصيص لطالب/طلاب (اختياري)</Label>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" className="w-full justify-between" disabled={!formData.country_id}>
                                    <span>{formData.assignedStudents.length > 0 ? `${formData.assignedStudents.length} طلاب محددون` : "اختر طالب أو أكثر..."}</span><ChevronDown className="h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width] bg-secondary border-border text-white max-h-60 overflow-y-auto">
                                {studentsInCountry.length > 0 ? studentsInCountry.map(student => (
                                    <DropdownMenuItem key={student.email} onSelect={(e) => e.preventDefault()} onClick={() => handleStudentSelection(student.email)} className="flex items-center gap-2 cursor-pointer">
                                        <Checkbox checked={formData.assignedStudents.includes(student.email)} />
                                        <span>{student.name} ({student.email})</span>
                                    </DropdownMenuItem>
                                )) : <DropdownMenuItem disabled>لا يوجد طلاب في هذه الدولة</DropdownMenuItem>}
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>
                    <div className="md:col-span-2">
                        <Label htmlFor="fileInput" className="text-white/80 mb-2 block">إرفاق الملف</Label>
                        <Input id="fileInput" type="file" ref={fileInputRef} onChange={handleFileChange} />
                    </div>
                </div>

                <DialogFooter>
                    <Button onClick={handleSubmit} disabled={isUploading}>
                        {isUploading ? <Loader2 className="animate-spin h-4 w-4 mr-2" /> : <Upload className="h-4 w-4 mr-2" />}
                        رفع الملف
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default MediaUploadForm;