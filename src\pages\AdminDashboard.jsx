import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet';
import { useAuth } from '@/contexts/AuthContext';
import AdminSidebar from '@/pages/admin/AdminSidebar';
import DashboardContent from '@/pages/admin/DashboardContent';
import UsersContent from '@/pages/admin/UsersContent';
import BookingsContent from '@/pages/admin/BookingsContent';
import ReportsContent from '@/pages/admin/ReportsContent';
import SettingsContent from '@/pages/admin/SettingsContent';
import PricingContent from '@/pages/admin/PricingContent.jsx';
import MediaLibraryContent from '@/pages/admin/MediaLibraryContent';
import LiveChatContent from '@/pages/admin/LiveChatContent';
import ExamsContentAdmin from '@/pages/admin/ExamsContentAdmin';
import InquiriesContent from '@/pages/admin/InquiriesContent';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  if (!user) {
    return null; 
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardContent setActiveTab={setActiveTab} />;
      case 'users':
        return <UsersContent />;
      case 'bookings':
        return <BookingsContent />;
      case 'reports':
        return <ReportsContent />;
      case 'pricing':
        return <PricingContent />;
       case 'exams':
        return <ExamsContentAdmin />;
      case 'media-library':
        return <MediaLibraryContent />;
      case 'live-chat':
        return <LiveChatContent />;
      case 'inquiries':
        return <InquiriesContent />;
      case 'settings':
        return <SettingsContent />;
      default:
        return <DashboardContent setActiveTab={setActiveTab} />;
    }
  };

  return (
    <>
      <Helmet>
        <title>لوحة تحكم الإدارة - Gulf Academy</title>
        <meta name="description" content="لوحة تحكم الإدارة في منصة Gulf Academy - إدارة شاملة للمنصة والمستخدمين" />
      </Helmet>
      <div className="min-h-screen gradient-bg">
        <div className="flex">
          <AdminSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
          <main className="flex-1 p-8 overflow-y-auto h-screen">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">لوحة تحكم الإدارة</h1>
              <p className="text-white/70">إدارة شاملة لمنصة Gulf Academy</p>
            </div>
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {renderContent()}
            </motion.div>
          </main>
        </div>
      </div>
    </>
  );
};

export default AdminDashboard;