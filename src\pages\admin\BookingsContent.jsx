import React, { useState } from 'react';
    import { useBooking } from '@/contexts/BookingContext';
    import { useAuth } from '@/contexts/AuthContext';
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
    import { Check, X, Trash2, Clock, UserX, AlertTriangle, Settings, Info } from 'lucide-react';
    import EditBookingModal from './EditBookingModal';
    import BookingDetailsModal from './BookingDetailsModal';
    import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
    import { useToast } from '@/components/ui/use-toast';
    import BookingModal from '@/pages/student/BookingModal';
    import { Dialog } from '@/components/ui/dialog';


    const statusMap = {
      pending: { label: 'قيد المراجعة', variant: 'secondary' },
      approved: { label: 'تمت الموافقة', variant: 'success' },
      rejected: { label: 'تم الرفض', variant: 'destructive' },
      completed: { label: 'مكتمل', variant: 'default' },
    };

    const BookingsContent = () => {
      const { bookings, approveBooking, rejectBooking, deleteBooking, updateBooking } = useBooking();
      const { teachers } = useAuth();
      const { toast } = useToast();
      const [activeTab, setActiveTab] = useState('pending');
      const [selectedBooking, setSelectedBooking] = useState(null);
      const [isEditModalOpen, setIsEditModalOpen] = useState(false);
      const [isAdminBookingModalOpen, setIsAdminBookingModalOpen] = useState(false);
      const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
      const [teacherAssignments, setTeacherAssignments] = useState({});

      const filteredBookings = bookings.filter(b => {
          if (activeTab === 'pending') return b.status === 'pending';
          if (activeTab === 'requests') return b.postponementRequest?.status === 'pending' || b.teacherChangeRequest?.status === 'pending' || b.deletionRequest?.status === 'pending' || b.linkProblemRequest?.status === 'pending';
          if (activeTab === 'active') return b.status === 'approved' && !b.postponementRequest && !b.teacherChangeRequest && !b.deletionRequest && !b.linkProblemRequest;
          if (activeTab === 'finished') return b.status === 'completed' || b.status === 'rejected';
          return true;
      }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      const handleManageClick = (booking) => {
        setSelectedBooking(booking);
        setIsEditModalOpen(true);
      };

      const handleDetailsClick = (booking) => {
        setSelectedBooking(booking);
        setIsDetailsModalOpen(true);
      };

      const handleAdminEditBookingClick = (booking) => {
        setSelectedBooking(booking);
        setIsAdminBookingModalOpen(true);
      };

      const handleApproveClick = async (bookingId) => {
        const teacherEmail = teacherAssignments[bookingId];
        const bookingToUpdate = bookings.find(b => b.id === bookingId);
        
        if (!teacherEmail && !bookingToUpdate.teacherEmail) {
            toast({ title: "خطأ", description: "الرجاء تحديد معلم أولاً للموافقة على الحجز.", variant: "destructive" });
            return;
        }

        let updatedData = { ...bookingToUpdate };
        if (teacherEmail) {
          const teacher = teachers.find(t => t.email === teacherEmail);
          updatedData = {
              ...updatedData,
              teacherEmail: teacher.email,
              teacherName: teacher.name,
          };
          updateBooking(bookingId, updatedData);
        }
        
        await approveBooking(bookingId);
      };

      const getRequestBadge = (booking) => {
        if (booking.linkProblemRequest?.status === 'pending') return <Badge variant="destructive" className="flex items-center gap-1"><AlertTriangle className="h-3 w-3"/>مشكلة في الرابط</Badge>;
        if (booking.postponementRequest?.status === 'pending') return <Badge variant="warning" className="flex items-center gap-1"><Clock className="h-3 w-3"/>طلب تأجيل</Badge>;
        if (booking.teacherChangeRequest?.status === 'pending') return <Badge variant="info" className="flex items-center gap-1"><UserX className="h-3 w-3"/>طلب تغيير معلم</Badge>;
        if (booking.deletionRequest?.status === 'pending') return <Badge variant="destructive" className="flex items-center gap-1"><Trash2 className="h-3 w-3"/>طلب إلغاء</Badge>;
        return null;
      };

      const renderBookingCard = (booking) => {
        const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;
        return (
        <div key={booking.id} className="bg-secondary/50 rounded-lg p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2 flex-wrap">
                <h4 className="text-lg font-bold text-white">{studentNames}</h4>
                <Badge variant={statusMap[booking.status]?.variant || 'default'}>{statusMap[booking.status]?.label}</Badge>
                {getRequestBadge(booking)}
            </div>
            <p className="text-sm text-white/80">المعلم: {booking.teacherName || 'لم يحدد بعد'}</p>
            <p className="text-sm text-white/80">المادة: {booking.subjects.map(s => s.subject).join(', ')}</p>
            <p className="text-sm text-white/80">الباقة: {booking.totalHours} ساعات</p>
          </div>
          <div className="flex items-center gap-2 flex-wrap">
            {booking.status === 'pending' && (
              <>
                <div className="w-40">
                    <Select onValueChange={(value) => setTeacherAssignments({...teacherAssignments, [booking.id]: value})}>
                        <SelectTrigger className="bg-background text-white text-xs h-9"><SelectValue placeholder="اختر معلماً..." /></SelectTrigger>
                        <SelectContent className="bg-secondary text-white">{teachers.map(t => <SelectItem key={t.email} value={t.email}>{t.name}</SelectItem>)}</SelectContent>
                    </Select>
                </div>
                <Button size="sm" className="bg-green-600 hover:bg-green-700" onClick={() => handleApproveClick(booking.id)}><Check className="h-4 w-4 ml-1" /> موافقة</Button>
                <Button size="sm" variant="destructive" onClick={() => rejectBooking(booking.id)}><X className="h-4 w-4 ml-1" /> رفض</Button>
              </>
            )}
            {activeTab === 'requests' && (
                <Button size="sm" variant="outline" onClick={() => handleManageClick(booking)}><Settings className="h-4 w-4 ml-1" /> إدارة الطلب</Button>
            )}
             <Button size="sm" variant="ghost" onClick={() => handleAdminEditBookingClick(booking)}>تعديل</Button>
            <Button size="sm" variant="ghost" onClick={() => handleDetailsClick(booking)}><Info className="h-4 w-4" /></Button>
            <Button size="sm" variant="ghost" onClick={() => deleteBooking(booking.id)} className="text-red-500 hover:bg-red-500/10 hover:text-red-400"><Trash2 className="h-4 w-4" /></Button>
          </div>
        </div>
      )};

      return (
        <>
          <div className="glass-effect rounded-xl p-6">
            <h3 className="text-2xl font-bold text-white mb-6">إدارة الحجوزات والطلبات</h3>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 bg-secondary/30 mb-4">
                <TabsTrigger value="pending">حجوزات جديدة</TabsTrigger>
                <TabsTrigger value="requests">طلبات إجرائية</TabsTrigger>
                <TabsTrigger value="active">اشتراكات فعالة</TabsTrigger>
                <TabsTrigger value="finished">الأرشيف</TabsTrigger>
              </TabsList>
              
              <TabsContent value="pending">
                <div className="space-y-4">{filteredBookings.length > 0 ? filteredBookings.map(renderBookingCard) : <p className="text-center text-white/70 py-8">لا توجد حجوزات جديدة.</p>}</div>
              </TabsContent>
              <TabsContent value="requests">
                 <div className="space-y-4">{filteredBookings.length > 0 ? filteredBookings.map(renderBookingCard) : <p className="text-center text-white/70 py-8">لا توجد طلبات جديدة.</p>}</div>
              </TabsContent>
              <TabsContent value="active">
                 <div className="space-y-4">{filteredBookings.length > 0 ? filteredBookings.map(renderBookingCard) : <p className="text-center text-white/70 py-8">لا توجد اشتراكات فعالة.</p>}</div>
              </TabsContent>
              <TabsContent value="finished">
                 <div className="space-y-4">{filteredBookings.length > 0 ? filteredBookings.map(renderBookingCard) : <p className="text-center text-white/70 py-8">الأرشيف فارغ.</p>}</div>
              </TabsContent>
            </Tabs>
          </div>
          
          {selectedBooking && isEditModalOpen && (
            <EditBookingModal 
              booking={selectedBooking} 
              isOpen={isEditModalOpen} 
              onClose={() => setIsEditModalOpen(false)} 
            />
          )}
          {selectedBooking && isDetailsModalOpen && (
            <BookingDetailsModal
              booking={selectedBooking}
              isOpen={isDetailsModalOpen}
              onClose={() => setIsDetailsModalOpen(false)}
            />
          )}
          <Dialog open={isAdminBookingModalOpen} onOpenChange={setIsAdminBookingModalOpen}>
            {selectedBooking && <BookingModal setIsOpen={setIsAdminBookingModalOpen} existingBooking={selectedBooking} isAdminEditing={true} />}
          </Dialog>
        </>
      );
    };

    export default BookingsContent;