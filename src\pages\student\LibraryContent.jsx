
import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { BookOpen, Search, Filter, Folder, Eye, Video, FileText, Image, Mic } from 'lucide-react';
import { supabase } from '@/lib/customSupabaseClient';
import { useToast } from '@/components/ui/use-toast';

const fileTypeIcons = { 'فيديو': <Video />, 'ملف PDF': <FileText />, 'صورة': <Image />, 'صوت': <Mic />, 'عرض تقديمي': <FileText />, 'مستند': <FileText /> };

const LibraryContent = ({ setViewerUrl, setIsViewerOpen }) => {
  const { user: currentUser } = useAuth();
  const [library, setLibrary] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeSubject, setActiveSubject] = useState(null);
  const [activeType, setActiveType] = useState(null);
  const { toast } = useToast();
  
  const fetchLibrary = useCallback(async () => {
    setLoading(true);

    const { data: assignedMaterials, error: assignedError } = await supabase.rpc('get_student_materials', { p_student_email: currentUser.email });

    if (assignedError) {
      console.error("Error fetching student materials:", assignedError);
      setLoading(false);
      return;
    }
    
    const groupedMedia = assignedMaterials.reduce((acc, item) => {
      const { subject, file_type } = item;
      if (!acc[subject]) acc[subject] = {};
      if (!acc[subject][file_type]) acc[subject][file_type] = [];
      acc[subject][file_type].push(item);
      return acc;
    }, {});

    setLibrary(groupedMedia);
    if (Object.keys(groupedMedia).length > 0) {
      setActiveSubject(Object.keys(groupedMedia)[0]);
    }
    setLoading(false);
  }, [currentUser.email]);

  useEffect(() => {
    fetchLibrary();
  }, [fetchLibrary]);

  const handleItemClick = (item) => {
    const viewableLink = item.view_link.includes('drive.google.com') 
      ? item.view_link.replace('/view', '/preview').replace('?usp=drivesdk', '')
      : item.view_link;
    setViewerUrl(viewableLink);
    setIsViewerOpen(true);
  };
  
  const handleFeatureClick = (feature) => {
    toast({
      title: "ميزة قيد التطوير",
      description: `نحن نعمل بجد على تفعيل ميزة "${feature}".`,
    });
  };
  
  const getIcon = (type) => {
    const icon = fileTypeIcons[type] || <Eye />;
    return React.cloneElement(icon, { className: "h-8 w-8 text-primary" });
  };

  const renderContent = () => {
    if (loading) {
      return <p className="text-center py-12 text-white/70">جاري تحميل مكتبتك...</p>;
    }
    if (Object.keys(library).length === 0) {
      return (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-white/30" />
          <h3 className="mt-4 text-lg font-semibold text-white">مكتبتك فارغة</h3>
          <p className="mt-2 text-sm text-white/60">لم يقم أي معلم بإضافة مواد لك بعد.</p>
        </div>
      );
    }

    if (activeSubject && activeType) {
      return (
        <div>
          <Button variant="ghost" onClick={() => setActiveType(null)} className="mb-4 text-white/80 hover:text-white">&larr; العودة إلى مجلدات {activeSubject}</Button>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {library[activeSubject][activeType].map(item => (
              <div key={item.id} onClick={() => handleItemClick(item)} className="bg-secondary/50 rounded-lg p-6 hover-lift cursor-pointer block">
                <div className="text-center">
                  <div className="bg-gradient-to-br from-primary/50 to-blue-500/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">{getIcon(item.file_type)}</div>
                  <h4 className="font-bold text-white mb-2 truncate">{item.lesson_title || item.name}</h4>
                  <p className="text-muted-foreground/70 text-xs">تاريخ الإضافة: {new Date(item.created_at).toLocaleDateString('ar-SA')}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    }

    if (activeSubject) {
      return (
        <div>
          <Button variant="ghost" onClick={() => setActiveSubject(null)} className="mb-4 text-white/80 hover:text-white">&larr; العودة إلى المواد</Button>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {Object.keys(library[activeSubject]).map(type => (
              <div key={type} onClick={() => setActiveType(type)} className="bg-secondary/50 rounded-lg p-6 hover-lift cursor-pointer flex flex-col items-center justify-center aspect-square">
                <Folder className="h-16 w-16 text-primary mb-4" />
                <h4 className="font-bold text-white text-center">{type}</h4>
                <p className="text-xs text-white/60 mt-1">{library[activeSubject][type].length} ملفات</p>
              </div>
            ))}
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {Object.keys(library).map(subject => (
          <div key={subject} onClick={() => setActiveSubject(subject)} className="bg-secondary/50 rounded-lg p-6 hover-lift cursor-pointer flex flex-col items-center justify-center aspect-square">
            <Folder className="h-16 w-16 text-primary mb-4" />
            <h4 className="font-bold text-white text-center">{subject}</h4>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">مكتبتي الخاصة</h3>
          <div className="flex gap-4">
            <Button size="sm" variant="outline" className="border-border text-white hover:bg-secondary/50" onClick={() => handleFeatureClick('البحث')}><Search className="h-4 w-4 ml-2" />بحث</Button>
            <Button size="sm" variant="outline" className="border-border text-white hover:bg-secondary/50" onClick={() => handleFeatureClick('الفلترة')}><Filter className="h-4 w-4 ml-2" />فلترة</Button>
          </div>
        </div>
        {renderContent()}
      </div>
    </div>
  );
};

export default LibraryContent;
