import React, { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { FileText } from 'lucide-react';
import { format, parseISO, isValid } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ReportsContent = () => {
  const { user: student, users } = useAuth();
  const { sessionLogs, bookings } = useBooking();

  const studentLogs = useMemo(() => {
    const studentBookingIds = bookings
      .filter(b => b.studentEmails?.includes(student.email))
      .map(b => b.id);

    return sessionLogs
      .filter(log => studentBookingIds.includes(log.booking_id))
      .sort((a, b) => new Date(b.scheduled_start_time) - new Date(a.scheduled_start_time));
  }, [bookings, sessionLogs, student.email]);
    
  const formatTime = (timeValue) => {
    if (!timeValue) return '-';
    try {
      const parsedDate = parseISO(timeValue);
      if (!isValid(parsedDate)) return '-';
      return format(parsedDate, 'p', { locale: arSA });
    } catch (e) {
      return '-';
    }
  };

  const formatDate = (dateValue) => {
    if (!dateValue) return '-';
    try {
      const parsedDate = parseISO(dateValue);
      if (!isValid(parsedDate)) return '-';
      return format(parsedDate, 'yyyy-MM-dd', { locale: arSA });
    } catch (e) {
      return '-';
    }
  };

  const formatDuration = (minutes) => {
    if (minutes === null || typeof minutes === 'undefined') return 'لم تسجل';
    if (minutes < 1) return 'أقل من دقيقة';
    return `${minutes} دقيقة`;
  };
  
  const getTeacherName = (email) => {
      return users.find(t => t.email === email && t.userType === 'teacher')?.name || email;
  }

  return (
    <>
      <div className="space-y-8">
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6 flex-wrap gap-4">
            <h3 className="text-2xl font-bold text-white flex items-center gap-2"><FileText className="h-8 w-8 text-primary" />تقارير الحصص</h3>
          </div>
          <p className="text-white/70 mb-6">
            تابع تفاصيل حصصك والمدة التي قضيتها في كل منها.
          </p>
          <div className="overflow-x-auto">
            <table className="w-full text-right min-w-[1000px]">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="py-3 px-2 text-white font-medium">المعلم</th>
                  <th className="py-3 px-2 text-white font-medium">التاريخ</th>
                  <th className="py-3 px-2 text-white font-medium">وقت دخولي</th>
                  <th className="py-3 px-2 text-white font-medium">وقت خروجي</th>
                  <th className="py-3 px-2 text-white font-medium">مدتي في الحصة</th>
                  <th className="py-3 px-2 text-white font-medium">مدة الحصة الكاملة</th>
                  <th className="py-3 px-2 text-white font-medium">الحالة</th>
                </tr>
              </thead>
              <tbody>
                {studentLogs.length > 0 ? studentLogs.map(log => (
                  <tr key={log.id} className="border-b border-white/10 hover:bg-white/5 text-sm">
                    <td className="py-3 px-2 text-white">{getTeacherName(log.teacher_email)}</td>
                    <td className="py-3 px-2 text-white/80">{formatDate(log.scheduled_start_time)}</td>
                    <td className="py-3 px-2 text-green-400">{formatTime(log.student_joined_at)}</td>
                    <td className="py-3 px-2 text-red-400">{formatTime(log.student_left_at)}</td>
                    <td className="py-3 px-2 text-white/80">{formatDuration(log.student_duration_minutes)}</td>
                    <td className="py-3 px-2 text-yellow-400 font-bold">{formatDuration(log.teacher_duration_minutes)}</td>
                    <td className="py-3 px-2 text-center">
                      {log.status === 'completed' && <span className="text-green-400">مكتملة</span>}
                      {log.status === 'ongoing' && <span className="text-yellow-400">جارية</span>}
                      {log.status === 'scheduled' && <span className="text-blue-400">مجدولة</span>}
                    </td>
                  </tr>
                )) : (
                  <tr>
                    <td colSpan="7" className="text-center py-8 text-white/70">ليس لديك حصص مسجلة لعرضها.</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default ReportsContent;