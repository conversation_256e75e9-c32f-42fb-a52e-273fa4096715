import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { CountryProvider } from '@/contexts/CountryContext';
import { ChatProvider } from '@/contexts/ChatContext';
import { BookingProvider } from '@/contexts/BookingContext';
import { ExamProvider } from '@/contexts/ExamContext';
import { SettingsProvider } from '@/contexts/SettingsContext';
import { NotificationsProvider } from '@/contexts/NotificationsContext';
import HomePage from '@/pages/HomePage';
import StudentDashboard from '@/pages/student/StudentDashboard';
import TeacherDashboard from '@/pages/teacher/TeacherDashboard';
import AdminDashboard from '@/pages/admin/AdminDashboard';
import LoginPage from '@/pages/LoginPage';
import AccessCodeLogin from '@/pages/AccessCodeLogin';
import RegisterPage from '@/pages/RegisterPage';
import TakeExamPage from '@/pages/student/TakeExamPage';
import { Toaster as ShadcnToaster } from '@/components/ui/toaster';
import { Toaster as SonnerToaster } from 'sonner';
import FloatingChatButton from '@/components/FloatingChatButton';
import AdminExamsResults from '@/pages/admin/AdminExamsResults';
import AboutPage from '@/pages/AboutPage';
import FaqPage from '@/pages/FaqPage';
import PrivacyPage from '@/pages/PrivacyPage';
import SessionReminderHandler from '@/components/SessionReminderHandler';

const ProtectedRoute = ({ children, role }) => {
  const { user } = useAuth();
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  if (role && user.userType !== role) {
    return <Navigate to="/" replace />;
  }
  return children;
};

function AppContent() {
  const { user } = useAuth();

  return (
    <>
      <Helmet>
        <title>Gulf Academy - منصة التعليم الإلكتروني الرائدة في الخليج</title>
        <meta name="description" content="منصة Gulf Academy للتعليم الإلكتروني - احجز دروسك الخصوصية مع أفضل المعلمين في السعودية وقطر والكويت وعمان" />
      </Helmet>
      
      {user && <SessionReminderHandler />}

      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/login/access-code" element={<AccessCodeLogin />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/faq" element={<FaqPage />} />
        <Route path="/privacy" element={<PrivacyPage />} />
        
        <Route 
          path="/student/*" 
          element={
            <ProtectedRoute role="student">
              <StudentDashboard />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/student/exam/:examId" 
          element={
            <ProtectedRoute role="student">
              <TakeExamPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/teacher/*" 
          element={
            <ProtectedRoute role="teacher">
              <TeacherDashboard />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/admin/*" 
          element={
            <ProtectedRoute role="admin">
              <AdminDashboard />
            </ProtectedRoute>
          } 
        />
         <Route 
          path="/admin/exam-results/:examId"
          element={
            <ProtectedRoute role="admin">
              <AdminExamsResults />
            </ProtectedRoute>
          }
        />
      </Routes>
      <ShadcnToaster />
      <SonnerToaster position="top-center" richColors />
      {!user || user.userType !== 'admin' ? (
        <FloatingChatButton />
      ) : null}
    </>
  );
}

function App() {
  return (
    <AuthProvider>
      <CountryProvider>
        <SettingsProvider>
           <NotificationsProvider>
              <BookingProvider>
                 <ExamProvider>
                    <ChatProvider>
                      <AppContent />
                    </ChatProvider>
                 </ExamProvider>
              </BookingProvider>
           </NotificationsProvider>
        </SettingsProvider>
      </CountryProvider>
    </AuthProvider>
  );
}

export default App;