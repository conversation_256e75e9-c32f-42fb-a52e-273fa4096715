import React from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Button } from '@/components/ui/button';
import { BookOpen, Users, Award, Clock, Star, Globe, ChevronLeft, LogOut } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useCountry } from '@/contexts/CountryContext';
import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';
import CountrySelector from '@/components/CountrySelector';
import Footer from '@/components/Footer';
import NotificationsDropdown from '@/components/NotificationsDropdown';

const HomePage = () => {
  const { toast } = useToast();
  const { country: selectedCountry, hasSelectedCountry } = useCountry();
  const { user, logout } = useAuth();
  const { settings } = useSettings();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
    toast({
      title: 'تم تسجيل الخروج بنجاح',
      description: 'نأمل أن نراك قريباً!',
    });
  };

  const handleFeatureClick = (featureName) => {
    toast({
      title: `ميزة "${featureName}" قيد التطوير`,
      description: "نحن نعمل بجد لإتاحتها قريباً. شكراً لصبرك! 🚀",
    });
  };

  const handlePromoVideoClick = () => {
    if (settings.promotionalVideo?.link) {
      window.open(settings.promotionalVideo.link, '_blank');
    } else {
      toast({
        title: "الفيديو التعريفي غير متوفر حالياً",
        description: "يرجى المحاولة مرة أخرى في وقت لاحق.",
        variant: "destructive"
      });
    }
  };

  const features = [
    { icon: BookOpen, title: 'دروس تفاعلية', description: 'دروس مباشرة عبر Zoom مع أفضل المعلمين' },
    { icon: Users, title: 'معلمون مؤهلون', description: 'فريق من المعلمين المتخصصين والمعتمدين' },
    { icon: Clock, title: 'مرونة في المواعيد', description: 'احجز في الوقت الذي يناسبك' },
    { icon: Award, title: 'شهادات معتمدة', description: 'احصل على شهادات إتمام معتمدة' }
  ];

  const stats = [
    { number: '10,000+', label: 'طالب مسجل' },
    { number: '500+', label: 'معلم متخصص' },
    { number: '50,000+', label: 'حصة مكتملة' },
    { number: '4.9', label: 'تقييم المنصة' }
  ];

  return (
    <>
      <Helmet>
        <title>Gulf Academy - الصفحة الرئيسية</title>
        <meta name="description" content="منصة Gulf Academy للتعليم الإلكتروني - احجز دروسك الخصوصية مع أفضل المعلمين" />
      </Helmet>

      {!hasSelectedCountry && <CountrySelector />}

      <div className="min-h-screen gradient-bg text-foreground">
        <header className="glass-effect border-b border-border sticky top-0 z-40">
          <div className="container mx-auto px-4 py-4">
            <nav className="flex items-center justify-between">
              <Link to="/" className="flex flex-col items-center gap-1">
                <img  alt="Gulf Academy Logo" class="h-20 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
              </Link>

              <div className="hidden md:flex items-center gap-8">
                <Link to="/" className="text-white hover:text-primary transition-colors">الرئيسية</Link>
                <Link to="/about" className="text-white hover:text-primary transition-colors">من نحن</Link>
                <Link to="/faq" className="text-white hover:text-primary transition-colors">الأسئلة الشائعة</Link>
              </div>

              <div className="flex items-center gap-2">
                {selectedCountry && (
                  <div className="flex items-center gap-2 bg-secondary/50 rounded-lg px-3 py-2">
                    <span className="text-white text-sm">{selectedCountry.name}</span>
                  </div>
                )}
                {user ? (
                   <>
                    <NotificationsDropdown />
                    <Link to={`/${user.userType}`}>
                      <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-primary-foreground">
                        لوحة التحكم
                      </Button>
                    </Link>
                    <Button onClick={handleLogout} variant="ghost" className="text-white hover:bg-secondary/50">
                      <LogOut className="h-4 w-4 ml-2" />
                      خروج
                    </Button>
                   </>
                ) : (
                  <>
                  <Link to="/login">
                    <Button variant="ghost" className="text-white hover:bg-secondary/50">
                      تسجيل الدخول
                    </Button>
                  </Link>
                  <Link to="/register">
                    <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
                      انضم الآن
                    </Button>
                  </Link>
                  </>
                )}
              </div>
            </nav>
          </div>
        </header>

        <main>
          <section className="py-20">
            <div className="container mx-auto px-4">
              <div className="grid lg:grid-cols-2 gap-12 items-center">
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  className="text-center lg:text-right"
                >
                  <h1 className="text-3xl lg:text-4xl font-bold text-white mb-6 leading-tight -translate-y-4">
                    مستقبل التعليم <br/>
                    <span className="text-gradient">في الخليج</span>
                  </h1>
                  <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                    انضم إلى أكثر من 10,000 طالب واحصل على دروس خصوصية تفاعلية مع أفضل المعلمين في المنطقة
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <Link to="/register">
                      <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 text-lg px-8 py-4">
                        ابدأ رحلتك التعليمية
                        <ChevronLeft className="mr-2 h-5 w-5" />
                      </Button>
                    </Link>
                    <Button 
                      size="lg" 
                      variant="outline" 
                      className="border-primary text-primary hover:bg-primary hover:text-primary-foreground text-lg px-8 py-4"
                      onClick={handlePromoVideoClick}
                    >
                      شاهد الفيديو التعريفي
                    </Button>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="relative"
                >
                  <div className="relative z-10">
                    <img  class="w-full h-auto rounded-2xl shadow-2xl animate-float" alt="طالبة مبتسمة تدرس عن بعد باستخدام حاسوب محمول وسماعات رأس" src="https://images.unsplash.com/photo-1664575196044-195f135295df" />
                  </div>
                  <div className="absolute -top-4 -right-4 w-72 h-72 bg-primary/10 rounded-full animate-pulse-slow"></div>
                  <div className="absolute -bottom-4 -left-4 w-64 h-64 bg-blue-500/10 rounded-full animate-pulse-slow"></div>
                </motion.div>
              </div>
            </div>
          </section>

          <section className="py-16">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                {stats.map((stat, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="text-center stats-card rounded-xl p-6"
                  >
                    <div className="text-3xl lg:text-4xl font-bold text-white mb-2">{stat.number}</div>
                    <div className="text-muted-foreground">{stat.label}</div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          <section className="py-20">
            <div className="container mx-auto px-4">
              <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8 }}
                  className="text-center mb-16"
              >
                <h2 className="text-4xl font-bold text-white mb-6">لماذا <span className="text-gradient">Gulf Academy</span>؟</h2>
                <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                  نقدم تجربة تعليمية متميزة تجمع بين أحدث التقنيات وأفضل المعلمين
                </p>
              </motion.div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="glass-effect rounded-xl p-6 text-center hover-lift cursor-pointer"
                    onClick={() => handleFeatureClick(feature.title)}
                  >
                    <div className="bg-gradient-to-br from-primary/50 to-blue-500/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-3">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          <section className="py-20">
            <div className="container mx-auto px-4">
              <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8 }}
                className="glass-effect rounded-2xl p-12 text-center"
              >
                <h2 className="text-4xl font-bold text-white mb-6">ابدأ رحلتك التعليمية اليوم</h2>
                <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                  انضم إلى آلاف الطلاب الذين حققوا أهدافهم التعليمية معنا
                </p>
                <Link to="/register">
                  <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 text-lg px-12 py-4">
                    سجل الآن مجاناً
                    <ChevronLeft className="mr-2 h-5 w-5" />
                  </Button>
                </Link>
              </motion.div>
            </div>
          </section>
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default HomePage;