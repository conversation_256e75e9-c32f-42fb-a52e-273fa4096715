import React, { createContext, useContext, useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

const ExamContext = createContext();

export const useExam = () => useContext(ExamContext);

const EXAMS_DB_KEY = 'gulfAcademyExams';
const EXAM_RESULTS_DB_KEY = 'gulfAcademyExamResults';

export const ExamProvider = ({ children }) => {
  const [exams, setExams] = useState([]);
  const [results, setResults] = useState([]);

  useEffect(() => {
    const storedExams = JSON.parse(localStorage.getItem(EXAMS_DB_KEY) || '[]');
    setExams(storedExams);
    const storedResults = JSON.parse(localStorage.getItem(EXAM_RESULTS_DB_KEY) || '[]');
    setResults(storedResults);
  }, []);

  const createExam = (examData) => {
    const newExam = {
      ...examData,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      assignedStudents: [],
    };
    const updatedExams = [...exams, newExam];
    setExams(updatedExams);
    localStorage.setItem(EXAMS_DB_KEY, JSON.stringify(updatedExams));
    return newExam;
  };

  const updateExam = (updatedExam) => {
    const updatedExams = exams.map(exam => exam.id === updatedExam.id ? updatedExam : exam);
    setExams(updatedExams);
    localStorage.setItem(EXAMS_DB_KEY, JSON.stringify(updatedExams));
  };

  const deleteExam = (examId) => {
    const updatedExams = exams.filter(exam => exam.id !== examId);
    setExams(updatedExams);
    localStorage.setItem(EXAMS_DB_KEY, JSON.stringify(updatedExams));
  };

  const getExamById = (examId) => {
    return exams.find(exam => exam.id === examId);
  };

  const submitExam = (submission) => {
    const newResult = {
      id: uuidv4(),
      ...submission,
      submittedAt: new Date().toISOString(),
    };
    const updatedResults = [...results, newResult];
    setResults(updatedResults);
    localStorage.setItem(EXAM_RESULTS_DB_KEY, JSON.stringify(updatedResults));
    return newResult;
  };

  const getResultsForStudent = (studentEmail) => {
    return results.filter(result => result.studentEmail === studentEmail);
  };

  const value = {
    exams,
    createExam,
    updateExam,
    deleteExam,
    getExamById,
    submitExam,
    results,
    getResultsForStudent,
  };

  return <ExamContext.Provider value={value}>{children}</ExamContext.Provider>;
};