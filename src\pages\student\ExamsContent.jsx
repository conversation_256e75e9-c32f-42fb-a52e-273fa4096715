import React from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useExam } from '@/contexts/ExamContext';
import { useNavigate } from 'react-router-dom';
import { CheckSquare, BarChart2 } from 'lucide-react';

const ExamsContent = () => {
  const { user } = useAuth();
  const { exams, getResultsForStudent } = useExam();
  const navigate = useNavigate();

  const myExams = exams.filter(exam => exam.assignedStudents?.includes(user.email));
  const myResults = getResultsForStudent(user.email);

  const getExamStatus = (examId) => {
    const result = myResults.find(r => r.examId === examId);
    if (result) {
      const totalPoints = result.totalPoints;
      return `مكتمل (${result.score}/${totalPoints})`;
    }
    return 'متاح للحل';
  };

  const getStatusClass = (examId) => {
    return myResults.some(r => r.examId === examId) ? 'bg-purple-500/20 text-purple-300' : 'bg-blue-500/20 text-blue-300';
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <h3 className="text-2xl font-bold text-white mb-6">اختباراتي</h3>
        <div className="space-y-4">
          {myExams.length === 0 ? (
            <div className="text-center py-12 text-white/70">
              <CheckSquare className="mx-auto h-12 w-12" />
              <p className="mt-4">لا توجد اختبارات مخصصة لك حالياً.</p>
            </div>
          ) : (
            myExams.map((exam) => {
              const result = myResults.find(r => r.examId === exam.id);
              return (
                <div key={exam.id} className="bg-secondary/50 rounded-lg p-4 flex items-center justify-between">
                  <div>
                    <p className="font-bold text-white">{exam.title}</p>
                    <p className="text-muted-foreground text-sm">{exam.questions.length} سؤال</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className={`px-3 py-1 rounded-full text-xs ${getStatusClass(exam.id)}`}>
                      {getExamStatus(exam.id)}
                    </span>
                    {result ? (
                      <Button size="sm" variant="ghost" className="text-white/70 hover:text-white"><BarChart2 className="h-4 w-4" /> عرض النتيجة</Button>
                    ) : (
                      <Button size="sm" className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => navigate(`/student/exam/${exam.id}`)}>
                        ابدأ الاختبار
                      </Button>
                    )}
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default ExamsContent;