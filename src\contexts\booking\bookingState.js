import { v4 as uuidv4 } from 'uuid';
import { isBefore, parseISO, format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const TEACHER_AVAILABILITY_DB_KEY = 'gulfAcademyTeacherAvailability';
const BOOKINGS_DB_KEY = 'gulfAcademyBookings';

export const loadInitialData = () => {
    try {
        const storedAvailability = JSON.parse(localStorage.getItem(TEACHER_AVAILABILITY_DB_KEY) || '{}');
        const storedBookings = JSON.parse(localStorage.getItem(BOOKINGS_DB_KEY) || '[]');
        return { availability: storedAvailability, bookings: storedBookings };
    } catch (error) {
        console.error("Error loading data from localStorage", error);
        localStorage.removeItem(TEACHER_AVAILABILITY_DB_KEY);
        localStorage.removeItem(BOOKINGS_DB_KEY);
        return { availability: {}, bookings: [] };
    }
};

export const createBookingRequestState = (bookings, bookingDetails, createNotification) => {
    const newBooking = {
      ...bookingDetails,
      id: uuidv4(),
      status: 'pending',
      createdAt: new Date().toISOString(),
      sessions: [],
      sessionsCompleted: 0,
      completedDuration: 0,
    };
    
    createNotification('admin', `طلب حجز جديد من الطالب ${newBooking.studentName}.`, 'booking', '/admin/bookings');
    return [...bookings, newBooking];
};

export const updateBookingState = (bookings, bookingId, updatedData, createNotification) => {
    return bookings.map(b => {
        if (b.id === bookingId) {
            const approvedBooking = { ...b, ...updatedData };
            const mainSubject = approvedBooking.subjects[0]?.subject || 'مادة جديدة';
            createNotification(approvedBooking.studentEmail, `تم تعديل حجزك لمادة ${mainSubject} من قبل الإدارة.`, 'info', '/student/upcoming-sessions');
            createNotification(approvedBooking.teacherEmail, `تم تعديل حجز الطالب ${approvedBooking.studentName}.`, 'info', '/teacher/upcoming-sessions');
            return approvedBooking;
        }
        return b;
    });
};

export const getAvailableSlotsState = (availability, bookings, teacherEmail, date) => {
    if (!teacherEmail || !date) return [];
    
    const teacherSchedule = availability[teacherEmail];
    if (!teacherSchedule) return [];

    const dayOfWeek = format(date, 'EEEE', { locale: arSA });
    const daySchedule = teacherSchedule[dayOfWeek];
    if (!daySchedule || !daySchedule.startTime || !daySchedule.endTime) return [];
    
    const bookedSlotsOnDate = bookings
        .filter(b => b.teacherEmail === teacherEmail && (b.status === 'approved' || b.status === 'pending'))
        .flatMap(b => b.schedule)
        .filter(s => s.date === format(date, 'yyyy-MM-dd'))
        .map(s => s.time);

    const slots = [];
    try {
        let currentTime = parseISO(`1970-01-01T${daySchedule.startTime}:00.000Z`);
        const endTime = parseISO(`1970-01-01T${daySchedule.endTime}:00.000Z`);

        while (isBefore(currentTime, endTime)) {
            const timeStr = format(currentTime, 'HH:mm');
            if (!bookedSlotsOnDate.includes(timeStr)) {
                slots.push(timeStr);
            }
            currentTime.setMinutes(currentTime.getMinutes() + 30);
        }
    } catch (e) {
        console.error("Error parsing time for availability:", e, daySchedule);
        return [];
    }
    return slots;
};