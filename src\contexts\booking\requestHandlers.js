export const handleSubscriptionDeletionState = (bookings, bookingId, approved, createNotification) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking) return bookings;

    if (approved) {
        const mainSubject = booking.subjects[0]?.subject || 'مادة جديدة';
        createNotification(booking.studentEmail, `تمت الموافقة على طلب إلغاء اشتراكك في مادة ${mainSubject}.`, 'success', '/student');
        createNotification(booking.teacherEmail, `تم إلغاء اشتراك الطالب ${booking.studentName} في مادة ${mainSubject}.`, 'info', '/teacher');
        return bookings.filter(b => b.id !== bookingId);
    } else {
        createNotification(booking.studentEmail, `تم رفض طلب إلغاء اشتراكك.`, 'error', '/student/upcoming-sessions');
        return bookings.map(b => 
            b.id === bookingId ? { ...b, deletionRequest: { ...b.deletionRequest, status: 'rejected' } } : b
        );
    }
};

export const handlePostponementRequestState = (bookings, bookingId, approved, newDate, newTime, createNotification) => {
    return bookings.map(b => {
        if (b.id === bookingId && b.postponementRequest) {
            if (approved && newDate && newTime) {
                createNotification(b.studentEmail, `تمت الموافقة على طلب تأجيل الحصة. الموعد الجديد: ${newDate} الساعة ${newTime}`, 'success', '/student/upcoming-sessions');
                createNotification(b.teacherEmail, `تمت الموافقة على طلب تأجيل الحصة للطالب ${b.studentName}. الموعد الجديد: ${newDate} الساعة ${newTime}`, 'success', '/teacher/upcoming-sessions');
                
                const firstScheduledSessionIndex = b.sessions.findIndex(s => s.status === 'scheduled');
                if (firstScheduledSessionIndex !== -1) {
                    const updatedSessions = [...b.sessions];
                    updatedSessions[firstScheduledSessionIndex] = {
                        ...updatedSessions[firstScheduledSessionIndex],
                        date: newDate,
                        time: newTime,
                    };
                    return {...b, postponementRequest: null, sessions: updatedSessions };
                }
                return {...b, postponementRequest: null };
            } else {
                createNotification(b.studentEmail, 'تم رفض طلب تأجيل الحصة.', 'error', '/student/upcoming-sessions');
                createNotification(b.teacherEmail, 'تم رفض طلب تأجيل الحصة.', 'error', '/teacher/upcoming-sessions');
                return {...b, postponementRequest: {...b.postponementRequest, status: 'rejected'}};
            }
        }
        return b;
    });
};

export const handleTeacherChangeRequestState = (bookings, bookingId, approved, newTeacher, createNotification) => {
    return bookings.map(b => {
        if (b.id === bookingId && b.teacherChangeRequest) {
            if (approved && newTeacher) {
                const oldTeacherEmail = b.teacherEmail;
                createNotification(b.studentEmail, `تمت الموافقة على طلب تغيير المعلم إلى ${newTeacher.name}.`, 'success', '/student/upcoming-sessions');
                createNotification(oldTeacherEmail, `تم نقل اشتراك الطالب ${b.studentName} إلى معلم آخر.`, 'info', '/teacher');
                createNotification(newTeacher.email, `تم اسناد اشتراك الطالب ${b.studentName} إليك.`, 'info', '/teacher/upcoming-sessions');
                return { ...b, teacherEmail: newTeacher.email, teacherName: newTeacher.name, teacherChangeRequest: null };
            } else {
                createNotification(b.studentEmail, 'تم رفض طلب تغيير المعلم.', 'error', '/student/upcoming-sessions');
                return { ...b, teacherChangeRequest: { ...b.teacherChangeRequest, status: 'rejected' } };
            }
        }
        return b;
    });
};