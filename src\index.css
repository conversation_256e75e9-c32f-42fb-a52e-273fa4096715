@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 222 47% 11%; /* Dark Blue */
  --foreground: 210 40% 98%; /* White */
  --card: 222 47% 11%;
  --card-foreground: 210 40% 98%;
  --popover: 222 47% 11%;
  --popover-foreground: 210 40% 98%;
  --primary: 43 74% 49%; /* Gold */
  --primary-foreground: 222 47% 11%;
  --secondary: 217 33% 17%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217 33% 17%;
  --muted-foreground: 215 20% 65%;
  --accent: 217 33% 17%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 210 40% 98%;
  --border: 217 33% 17%;
  --input: 217 33% 17%;
  --ring: 43 74% 49%;
  --radius: 0.5rem;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  font-family: 'Cairo', sans-serif;
  color: hsl(var(--foreground));
  direction: rtl;
}

.dashboard-container {
  font-size: 14px;
}

.dashboard-container h1, .dashboard-container h2, .dashboard-container h3, .dashboard-container h4, .dashboard-container h5, .dashboard-container h6 {
  font-size: inherit;
}

.font-cairo-playful {
    font-family: 'Playfair Display', serif;
}


.gradient-bg {
  background-color: hsl(var(--background));
  background-image: radial-gradient(circle at 1px 1px, hsl(var(--secondary)) 1px, transparent 0);
  background-size: 2rem 2rem;
}

.glass-effect {
  background: hsla(var(--secondary), 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid hsla(var(--foreground), 0.1);
}

.text-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)), #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px hsla(var(--primary), 0.1);
}

.country-card {
  background: hsla(var(--secondary), 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid hsla(var(--foreground), 0.1);
  transition: all 0.3s ease;
}

.country-card:hover {
  background: hsla(var(--secondary), 0.8);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px hsla(var(--primary), 0.1);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
}

.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.sidebar-gradient {
  background: hsl(var(--secondary));
}

.stats-card {
  background: hsla(var(--secondary), 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid hsla(var(--foreground), 0.1);
}

.calendar-day {
  transition: all 0.2s ease;
}

.calendar-day:hover {
  background: hsla(var(--primary), 0.1);
  transform: scale(1.05);
}

.calendar-day.available {
  background: hsla(var(--primary), 0.2);
  border: 1px solid hsla(var(--primary), 0.4);
}

.calendar-day.selected {
  background: hsla(var(--primary), 0.8);
  border: 1px solid hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.time-slot {
  transition: all 0.2s ease;
}

.time-slot:hover {
  background: hsla(var(--primary), 0.1);
  transform: scale(1.02);
}

.time-slot.available {
  background: hsla(var(--primary), 0.1);
  border: 1px solid hsla(var(--primary), 0.3);
}

.time-slot.selected {
  background: hsl(var(--primary));
  border: 1px solid hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.chat-widget {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 50;
}

/* Prose styles for content pages */
.prose {
  font-size: 12px; /* Default font size for prose content */
}

.prose p {
  margin-bottom: 1em;
}

.prose ul, .prose ol {
  margin-bottom: 1em;
  padding-right: 1.5em; /* Adjust for RTL list markers */
}

.prose li {
  margin-bottom: 0.5em;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: bold;
}

.prose-invert {
  color: hsl(var(--foreground));
}

.prose-invert h1, .prose-invert h2, .prose-invert h3, .prose-invert h4, .prose-invert h5, .prose-invert h6 {
  color: hsl(var(--primary));
}

.prose-invert a {
  color: hsl(var(--primary));
}

.prose-invert a:hover {
  color: hsla(var(--primary), 0.8);
}