import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/customSupabaseClient';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { ShoppingBag, Users, List } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const SalesReportsContent = () => {
    const { user: teacher } = useAuth();
    const { toast } = useToast();
    const [salesData, setSalesData] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchSalesData = useCallback(async () => {
        setLoading(true);
        const { data, error } = await supabase
            .from('sales_reports')
            .select('*')
            .eq('assigned_by_email', teacher.email);
            
        if (error) {
            toast({ title: 'خطأ', description: 'فشل في جلب تقارير البيع.', variant: 'destructive' });
        } else {
            setSalesData(data);
        }
        setLoading(false);
    }, [toast, teacher.email]);

    useEffect(() => {
        fetchSalesData();
    }, [fetchSalesData]);

    const stats = useMemo(() => {
        const totalAssignments = salesData.length;
        const activatedStudents = new Set(salesData.map(d => d.student_email)).size;
        return { totalAssignments, activatedStudents };
    }, [salesData]);

    const salesBySubject = useMemo(() => {
        const data = salesData.reduce((acc, sale) => {
            const subjectName = sale.material_name || 'غير محدد';
            if (!acc[subjectName]) {
                acc[subjectName] = { name: subjectName, count: 0 };
            }
            acc[subjectName].count += 1;
            return acc;
        }, {});
        return Object.values(data);
    }, [salesData]);

    return (
        <div className="space-y-8">
            <div className="glass-effect rounded-xl p-6">
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                    <ShoppingBag />
                    تقارير تفعيل المواد من المكتبة
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-blue-500/20 p-3 rounded-full"><Users className="h-6 w-6 text-blue-300"/></div>
                        <div><p className="text-sm text-white/70">طلاب تم تفعيل مواد لهم</p><p className="text-2xl font-bold">{stats.activatedStudents}</p></div>
                    </div>
                     <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-green-500/20 p-3 rounded-full"><List className="h-6 w-6 text-green-300"/></div>
                        <div><p className="text-sm text-white/70">إجمالي المواد المفعلة</p><p className="text-2xl font-bold">{stats.totalAssignments}</p></div>
                    </div>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 className="text-xl font-bold text-white mb-4">تفاصيل التفعيلات</h4>
                        <div className="overflow-auto max-h-[400px]">
                            <table className="w-full text-right">
                                <thead>
                                    <tr className="border-b border-white/20"><th className="p-2">الطالب</th><th className="p-2">المادة</th></tr>
                                </thead>
                                <tbody>
                                    {salesData.map(sale => (
                                        <tr key={sale.id} className="border-b border-white/10 text-sm"><td className="p-2">{sale.student_name}</td><td className="p-2">{sale.material_name}</td></tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <h4 className="text-xl font-bold text-white mb-4">التفعيلات حسب المادة</h4>
                        <div style={{ width: '100%', height: 300 }}>
                            <ResponsiveContainer>
                                <BarChart data={salesBySubject}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                                    <XAxis dataKey="name" stroke="rgba(255,255,255,0.7)" />
                                    <YAxis stroke="rgba(255,255,255,0.7)" />
                                    <Tooltip contentStyle={{ backgroundColor: '#1f2937', border: 'none', color: '#fff' }}/>
                                    <Legend wrapperStyle={{ color: '#fff' }} />
                                    <Bar dataKey="count" fill="#8884d8" name="عدد التفعيلات" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SalesReportsContent;