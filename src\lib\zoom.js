
import { supabase } from './customSupabaseClient';

    export const generateZoomMeeting = async (booking) => {
      // This function is now deprecated and will not be used.
      // Logic has been moved to use Jitsi Meet directly in bookingActions.js
      console.warn("generateZoomMeeting is deprecated. Using Jitsi Meet instead.");
      throw new Error("Zoom integration is disabled. Please use Jitsi.");
    };
