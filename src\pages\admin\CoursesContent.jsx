import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useCountry } from '@/contexts/CountryContext';

const COURSES_DB_KEY = 'gulfAcademyCourses';

const CoursesContent = () => {
  const { toast } = useToast();
  const { countries } = useCountry();
  const [courses, setCourses] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);
  const [courseToDelete, setCourseToDelete] = useState(null);

  useEffect(() => {
    const storedCourses = JSON.parse(localStorage.getItem(COURSES_DB_KEY) || '[]');
    setCourses(storedCourses);
  }, []);

  const saveCourses = (updatedCourses) => {
    setCourses(updatedCourses);
    localStorage.setItem(COURSES_DB_KEY, JSON.stringify(updatedCourses));
  };

  const handleOpenModal = (course = null) => {
    setEditingCourse(course ? { ...course } : { id: Date.now().toString(), name: '', country: '', status: 'نشط' });
    setIsModalOpen(true);
  };

  const handleSave = () => {
    if (!editingCourse.name || !editingCourse.country) {
      toast({ title: "بيانات غير مكتملة", variant: "destructive" });
      return;
    }
    const existingCourse = courses.find(c => c.id === editingCourse.id);
    let updatedCourses;
    if (existingCourse) {
      updatedCourses = courses.map(c => c.id === editingCourse.id ? editingCourse : c);
    } else {
      updatedCourses = [...courses, editingCourse];
    }
    saveCourses(updatedCourses);
    toast({ title: `✅ تم ${existingCourse ? 'تحديث' : 'إضافة'} المادة بنجاح` });
    setIsModalOpen(false);
    setEditingCourse(null);
  };

  const handleDelete = () => {
    if (courseToDelete) {
      const updatedCourses = courses.filter(c => c.id !== courseToDelete.id);
      saveCourses(updatedCourses);
      toast({ title: '🗑️ تم حذف المادة بنجاح' });
      setCourseToDelete(null);
    }
  };
  
  const getCountryName = (countryId) => {
    const country = countries.find(c => c.id === countryId);
    return country ? country.name : countryId;
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">إدارة المواد الدراسية</h3>
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => handleOpenModal()}><Plus className="h-4 w-4 ml-2" />إضافة مادة جديدة</Button>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-right">
            <thead>
              <tr className="border-b border-white/20">
                <th className="py-3 px-4 text-white font-medium">اسم المادة</th>
                <th className="py-3 px-4 text-white font-medium">الدولة</th>
                <th className="py-3 px-4 text-white font-medium">الحالة</th>
                <th className="py-3 px-4 text-white font-medium text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {courses.map((course) => (
                <tr key={course.id} className="border-b border-white/10">
                  <td className="py-3 px-4 text-white">{course.name}</td>
                  <td className="py-3 px-4 text-white/70">{getCountryName(course.country)}</td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${course.status === 'نشط' ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'}`}>{course.status}</span>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex justify-center gap-1">
                      <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={() => handleOpenModal(course)}><Edit className="h-4 w-4" /></Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={() => setCourseToDelete(course)}><Trash2 className="h-4 w-4" /></Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-secondary border-border text-white">
                          <AlertDialogHeader><AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle><AlertDialogDescription>سيتم حذف المادة "{course.name}" بشكل نهائي.</AlertDialogDescription></AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="border-border hover:bg-white/10">إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/80">حذف</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>{editingCourse?.id && courses.some(c=>c.id === editingCourse.id) ? 'تعديل' : 'إضافة'} مادة دراسية</DialogTitle>
          </DialogHeader>
          {editingCourse && (
            <div className="space-y-4 py-4">
              <div>
                <Label htmlFor="courseName" className="text-white/80 mb-2 block">اسم المادة</Label>
                <Input id="courseName" value={editingCourse.name} onChange={(e) => setEditingCourse({ ...editingCourse, name: e.target.value })} className="bg-white/10 border-border text-white" />
              </div>
              <div>
                <Label htmlFor="country" className="text-white/80 mb-2 block">الدولة</Label>
                <Select value={editingCourse.country} onValueChange={(value) => setEditingCourse({ ...editingCourse, country: value })}>
                  <SelectTrigger className="w-full bg-white/10 border-border text-white"><SelectValue placeholder="اختر الدولة..." /></SelectTrigger>
                  <SelectContent className="bg-secondary border-border text-white">{countries.map(c => <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>)}</SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status" className="text-white/80 mb-2 block">الحالة</Label>
                <Select value={editingCourse.status} onValueChange={(value) => setEditingCourse({ ...editingCourse, status: value })}>
                  <SelectTrigger className="w-full bg-white/10 border-border text-white"><SelectValue /></SelectTrigger>
                  <SelectContent className="bg-secondary border-border text-white">
                    <SelectItem value="نشط">نشط</SelectItem>
                    <SelectItem value="غير نشط">غير نشط</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)} className="border-border hover:bg-white/10">إلغاء</Button>
            <Button onClick={handleSave} className="bg-primary text-primary-foreground hover:bg-primary/90">حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CoursesContent;