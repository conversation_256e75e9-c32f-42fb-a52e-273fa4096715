# نظام التحكم في الأجهزة - Gulf Academy Device Controller

## نظرة عامة

تم دمج نظام التحكم في الأجهزة (Device Controller) في منصة Gulf Academy لضمان أمان إضافي للحسابات. يقوم النظام بجمع بصمة فريدة لكل جهاز ويمنع تسجيل الدخول من أجهزة غير مصرح بها.

## الميزات الرئيسية

### 🔒 أمان الأجهزة
- **بصمة الجهاز الفريدة**: يتم إنشاء بصمة فريدة لكل جهاز باستخدام معلومات المتصفح والنظام
- **منع الدخول غير المصرح**: لا يمكن تسجيل الدخول إلا من الجهاز المسجل مسبقاً
- **عدم جمع عنوان IP**: النظام لا يجمع عناوين IP لضمان الخصوصية

### 📱 معلومات الجهاز المجمعة
- معلومات المتصفح (النوع والإصدار)
- معلومات النظام (المنصة واللغة)
- معلومات الشاشة (الدقة وعمق الألوان)
- معلومات الأجهزة (عدد المعالجات والذاكرة)
- المنطقة الزمنية
- إعدادات اللمس

### 🎛️ لوحة التحكم
- **للطلاب**: عرض معلومات الجهاز المسجل
- **للمديرين**: إدارة جميع الأجهزة المسجلة في النظام
- **مراقبة النشاط**: تتبع آخر ظهور لكل جهاز

## كيفية العمل

### 1. التسجيل
```javascript
// عند إنشاء حساب جديد
const deviceInfo = await getDeviceInfo();
await saveDeviceInfo(userEmail);
```

### 2. تسجيل الدخول
```javascript
// عند تسجيل الدخول
const currentFingerprint = await getDeviceFingerprint();
const isValidDevice = await verifyDevice(storedFingerprint);
```

### 3. المراقبة
```javascript
// تحديث آخر ظهور
updateDeviceLastSeen(userEmail);
```

## الملفات المضافة/المحدثة

### ملفات جديدة
- `src/components/DeviceController.jsx` - مكون إدارة الأجهزة
- `src/lib/mysql.js` - إعدادات قاعدة البيانات
- `.env.example` - ملف الإعدادات المثال

### ملفات محدثة
- `src/lib/deviceFingerprint.js` - تحسين جمع بيانات الجهاز
- `src/contexts/AuthContext.jsx` - دمج نظام التحقق من الأجهزة
- `src/pages/RegisterPage.jsx` - تسجيل الجهاز عند إنشاء الحساب
- `src/pages/LoginPage.jsx` - التحقق من الجهاز عند تسجيل الدخول
- `src/pages/AdminDashboard.jsx` - إضافة لوحة تحكم الأجهزة
- `src/pages/StudentDashboard.jsx` - إضافة عرض معلومات الجهاز
- `package.json` - إضافة MySQL dependency

## إعداد قاعدة البيانات

### 1. إنشاء قاعدة البيانات
```sql
CREATE DATABASE gulf_academy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. إنشاء المستخدم
```sql
CREATE USER 'gulf_academy'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON gulf_academy_db.* TO 'gulf_academy'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تشغيل السكريبت
```javascript
import { CREATE_TABLES_SQL } from './src/lib/mysql.js';
// تشغيل السكريبت لإنشاء الجداول
```

## الاستخدام

### للطلاب
1. الانتقال إلى "معلومات الجهاز" في القائمة الجانبية
2. عرض تفاصيل الجهاز المسجل
3. مراقبة آخر نشاط

### للمديرين
1. الانتقال إلى "نظام التحكم في الأجهزة" في لوحة الإدارة
2. عرض جميع الأجهزة المسجلة
3. إدارة وحذف الأجهزة حسب الحاجة

## الأمان والخصوصية

### ما يتم جمعه
- ✅ بصمة الجهاز المشفرة
- ✅ معلومات المتصفح والنظام
- ✅ معلومات الشاشة والأجهزة
- ✅ المنطقة الزمنية

### ما لا يتم جمعه
- ❌ عنوان IP
- ❌ الموقع الجغرافي
- ❌ معلومات شخصية حساسة
- ❌ كلمات المرور

## التخصيص

### تعديل البيانات المجمعة
```javascript
// في src/lib/deviceFingerprint.js
export const getDeviceFingerprint = () => {
  // إضافة أو إزالة البيانات المطلوبة
};
```

### تعديل إعدادات الأمان
```javascript
// في .env
DEVICE_SECURITY_ENABLED=true
MAX_DEVICES_PER_USER=1
DEVICE_VERIFICATION_REQUIRED=true
```

## استكشاف الأخطاء

### مشاكل شائعة
1. **فشل التحقق من الجهاز**: تأكد من تطابق البصمة
2. **عدم حفظ البيانات**: تحقق من إعدادات localStorage
3. **مشاكل قاعدة البيانات**: تأكد من صحة إعدادات الاتصال

### سجلات النظام
```javascript
// تفعيل السجلات المفصلة
console.log('Device fingerprint:', deviceFingerprint);
console.log('Verification result:', verificationResult);
```

## الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX

## الترخيص

هذا النظام مطور خصيصاً لـ Gulf Academy ومحمي بحقوق الطبع والنشر.

---

**ملاحظة**: يُنصح بإجراء نسخ احتياطية منتظمة لبيانات الأجهزة المسجلة.
