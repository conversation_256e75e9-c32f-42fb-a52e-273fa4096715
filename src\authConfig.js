import { LogLevel } from "@azure/msal-browser";

export const msalConfig = {
    auth: {
        clientId: "74658136-14ec-4630-ad9b-26e160ff0fc6", // This needs to be replaced by the user's actual client ID
        authority: "https://login.microsoftonline.com/common",
        redirectUri: "/"
    },
    cache: {
        cacheLocation: "sessionStorage",
        storeAuthStateInCookie: false,
    },
    system: {
        loggerOptions: {
            loggerCallback: (level, message, containsPii) => {
                if (containsPii) {
                    return;
                }
                switch (level) {
                    case LogLevel.Error:
                        console.error(message);
                        return;
                    case LogLevel.Info:
                        console.info(message);
                        return;
                    case LogLevel.Verbose:
                        console.debug(message);
                        return;
                    case LogLevel.Warning:
                        console.warn(message);
                        return;
                }
            }
        }
    }
};

export const loginRequest = {
    scopes: ["User.Read", "Calendars.ReadWrite"]
};