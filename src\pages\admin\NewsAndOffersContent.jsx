import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Send, User, Mail, Users, Paperclip, ChevronDown, File as FileIcon, Upload, XCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { supabase } from '@/lib/customSupabaseClient';

const NewsAndOffersContent = () => {
  const { toast } = useToast();
  const { sendAnnouncement } = useChat();
  const { user, students } = useAuth();
  
  const [media, setMedia] = useState([]);
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [targetAudience, setTargetAudience] = useState('all_users');
  const [attachedFile, setAttachedFile] = useState(null);
  const fileInputRef = useRef(null);

  const fetchMedia = async () => {
    const { data, error } = await supabase.from('study_materials').select('*');
    if (error) {
      console.error('Error fetching media:', error);
    } else {
      setMedia(data);
    }
  };

  useEffect(() => {
    fetchMedia();
  }, []);

  const handleSendMessage = async () => {
    if (!subject.trim() || !message.trim()) {
      toast({
        title: 'خطأ',
        description: 'الرجاء كتابة الموضوع والرسالة.',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);

    try {
        sendAnnouncement(subject, message, targetAudience, attachedFile);
        toast({
            title: 'تم الإرسال بنجاح!',
            description: `تم إرسال الخبر إلى المستخدمين المحددين.`,
        });
        setSubject('');
        setMessage('');
        setAttachedFile(null);
    } catch (e) {
      console.error("Error sending announcement:", e);
      toast({
        title: 'خطأ في الإرسال',
        description: 'حدث خطأ غير متوقع أثناء إرسال الرسالة.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileSelectFromLibrary = (file) => {
    setAttachedFile({
        title: file.name,
        link: file.view_link
    });
  };

  const handleUploadClick = () => {
    fileInputRef.current.click();
  };

  const uploadToDrive = async (file) => {
    setIsUploading(true);
    toast({ title: 'جاري رفع الملف...', description: file.name });
    
    try {
        const { data, error } = await supabase.functions.invoke('upload-to-drive', {
            body: file,
            headers: { 'x-file-name': encodeURIComponent(file.name) },
        });

        if (error) throw error;
        if (!data.success) throw new Error(data.error || 'فشل الرفع إلى Google Drive');

        toast({ title: '🎉 تم رفع الملف بنجاح!' });
        return { name: file.name, link: data.webViewLink, id: data.fileId };
    } catch (error) {
        console.error('Upload error:', error);
        toast({ title: '❌ فشل الرفع', description: `حدث خطأ: ${error.message}`, variant: 'destructive' });
        return null;
    } finally {
        setIsUploading(false);
    }
  };

  const handleFileChange = async (event) => {
    if (event.target.files.length === 0) return;
    const file = event.target.files[0];
    const uploadedFile = await uploadToDrive(file);
    if (uploadedFile) {
        setAttachedFile({
            title: uploadedFile.name,
            link: uploadedFile.link
        });
    }
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <Card className="glass-effect p-6">
      <CardHeader className="mb-4 p-0">
        <CardTitle className="text-2xl font-bold text-primary flex items-center justify-start gap-2">
          <Send className="h-6 w-6" />
          أرسل الأخبار والعروض
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-0">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="targetAudience" className="text-white flex items-center gap-2">
                <Users className="h-4 w-4" />
                إرسال إلى
              </Label>
              <Select value={targetAudience} onValueChange={setTargetAudience} dir="rtl">
                <SelectTrigger id="targetAudience" className="w-full bg-secondary/50 border-border text-white">
                  <SelectValue placeholder="اختر الجمهور المستهدف" />
                </SelectTrigger>
                <SelectContent className="bg-secondary border-border text-white">
                  <SelectItem value="all_users">جميع المستخدمين (طلاب ومعلمين)</SelectItem>
                  <SelectItem value="all_students">جميع الطلاب</SelectItem>
                  <SelectItem value="all_teachers">جميع المعلمين</SelectItem>
                </SelectContent>
              </Select>
            </div>
             <div className="space-y-2">
                <Label htmlFor="attachment" className="text-white flex items-center gap-2">
                    <Paperclip className="h-4 w-4" />
                    إرفاق ملف (اختياري)
                </Label>
                <div className="flex gap-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="flex-1 justify-between bg-secondary/50 border-border text-white hover:bg-secondary">
                          <span>اختر من المكتبة</span>
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width] bg-secondary border-border text-white max-h-60 overflow-y-auto">
                        {media.length > 0 ? media.map(file => (
                          <DropdownMenuItem key={file.id} onSelect={() => handleFileSelectFromLibrary(file)} className="flex items-center gap-2 cursor-pointer">
                            <FileIcon className="h-4 w-4" />
                            <span>{file.name}</span>
                          </DropdownMenuItem>
                        )) : (
                            <DropdownMenuItem disabled>لا توجد ملفات في المكتبة</DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                    <Button variant="outline" onClick={handleUploadClick} disabled={isUploading} className="bg-secondary/50 border-border text-white hover:bg-secondary">
                        <Upload className="h-4 w-4 ml-2" />
                        {isUploading ? 'جار الرفع...' : 'رفع جديد'}
                    </Button>
                    <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
                </div>
            </div>
        </div>

        {attachedFile && (
            <div className="bg-secondary/50 p-3 rounded-md flex items-center justify-between">
                <div className="flex items-center gap-2 text-white">
                    <FileIcon className="h-5 w-5 text-primary" />
                    <span className="font-medium">{attachedFile.title}</span>
                </div>
                <Button variant="ghost" size="icon" onClick={() => setAttachedFile(null)} className="text-red-500 hover:text-red-400 h-7 w-7">
                    <XCircle className="h-5 w-5" />
                </Button>
            </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="subject" className="text-white flex items-center gap-2">
            <User className="h-4 w-4" />
            الموضوع
          </Label>
          <Input
            id="subject"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            placeholder="موضوع الرسالة"
            className="bg-secondary/50 border-border text-white"
            disabled={loading || isUploading}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="message" className="text-white flex items-center gap-2">
            <Mail className="h-4 w-4" />
            الرسالة
          </Label>
          <Textarea
            id="message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="اكتب رسالتك هنا..."
            rows={8}
            className="bg-secondary/50 border-border text-white"
            disabled={loading || isUploading}
          />
        </div>
        <Button
          onClick={handleSendMessage}
          className="bg-primary hover:bg-primary/90 text-primary-foreground w-full flex items-center gap-2"
          disabled={loading || isUploading}
        >
          {loading ? 'جاري الإرسال...' : 'إرسال الرسالة'}
          <Send className="h-5 w-5" />
        </Button>
        <p className="text-sm text-muted-foreground text-center">
          ستصل هذه الرسالة إلى صندوق الوارد (الأخبار والعروض) للمستخدمين المحددين.
        </p>
      </CardContent>
    </Card>
  );
};

export default NewsAndOffersContent;