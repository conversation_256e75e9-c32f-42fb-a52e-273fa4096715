import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Paperclip, User, MessageSquare, Users, Mail, Phone, PlusCircle, Search, Mic, Square } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ChatMessage from '@/components/ChatMessage';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { supabase } from '@/lib/customSupabaseClient';

const LiveChatContent = () => {
  const { user, users } = useAuth();
  const { messages, sendMessage, editMessage, deleteMessage, internalMessages, sendInternalMessage, editInternalMessage, deleteInternalMessage, getInternalChatId, chatGroups, createChatGroup } = useChat();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('live-chat');
  const [activeChatId, setActiveChatId] = useState(null);
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const [isCreateGroupOpen, setIsCreateGroupOpen] = useState(false);
  const [newGroupData, setNewGroupData] = useState({ name: '', members: [] });
  const [searchTerm, setSearchTerm] = useState('');

  const liveChatSessions = Object.keys(messages).filter(key => messages[key].length > 0);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, internalMessages, activeChatId]);

  const handleSendMessage = (chatId, isInternal, fileInfo = null) => {
    const text = fileInfo ? `مرفق: ${fileInfo.name}` : message;
    if (text.trim() === '' || !chatId) return;
    const sender = { id: user.email, name: 'الإدارة', type: 'admin' };
    if (isInternal) {
      sendInternalMessage(chatId, text, sender, fileInfo ? fileInfo.link : null);
    } else {
      const payload = { text, sender, fileLink: fileInfo ? fileInfo.link : null };
      sendMessage(chatId, payload);
    }
    setMessage('');
  };

  const uploadToDrive = async (file) => {
    setIsUploading(true);
    toast({ title: 'جاري رفع الملف...', description: file.name });
    try {
      const { data, error } = await supabase.functions.invoke('upload-to-drive', {
        body: file,
        headers: { 'x-file-name': encodeURIComponent(file.name) },
      });

      if (error) throw error;
      if (!data.success) throw new Error(data.error || 'فشل الرفع إلى Google Drive');
      
      toast({ title: '🎉 تم إرفاق الملف بنجاح!' });
      return { name: file.name, link: data.webViewLink };
    } catch (error) {
      console.error('Upload error:', error);
      toast({ title: '❌ فشل الرفع', description: `حدث خطأ: ${error.message}`, variant: 'destructive' });
      return null;
    } finally {
      setIsUploading(false);
    }
  };
  
  const handleAttachment = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = async (event, chatId, isInternal) => {
    if (event.target.files.length === 0) return;
    const file = event.target.files[0];
    const uploadedFile = await uploadToDrive(file);
    if (uploadedFile) handleSendMessage(chatId, isInternal, uploadedFile);
    fileInputRef.current.value = "";
  };

  const startRecording = async (chatId, isInternal) => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      mediaRecorderRef.current.ondataavailable = event => audioChunksRef.current.push(event.data);
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const audioFile = new File([audioBlob], `تسجيل-صوتي-${Date.now()}.webm`, { type: 'audio/webm' });
        const uploadedFile = await uploadToDrive(audioFile);
        if (uploadedFile) handleSendMessage(chatId, isInternal, uploadedFile);
        stream.getTracks().forEach(track => track.stop());
      };
      mediaRecorderRef.current.start();
      setIsRecording(true);
      toast({ title: "🎙️ بدأ التسجيل..." });
    } catch (err) {
      toast({ title: "خطأ في الوصول للميكروفون", variant: "destructive" });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      toast({ title: "🛑 توقف التسجيل", description: "جاري معالجة المقطع الصوتي..." });
    }
  };
  
  const handleEditMessage = (chatId, messageId, newText, isInternal) => {
    if (isInternal) editInternalMessage(chatId, messageId, newText);
    else editMessage(chatId, messageId, newText);
    toast({ title: "✅ تم تعديل الرسالة" });
  };
  
  const handleDeleteMessage = (chatId, messageId, isInternal) => {
    if (isInternal) deleteInternalMessage(chatId, messageId);
    else deleteMessage(chatId, messageId);
    toast({ title: "🗑️ تم حذف الرسالة" });
  };

  const getParticipantInfo = (chatId) => {
    const chat = messages[chatId];
    if (!chat || chat.length === 0) return { name: `زائر (${chatId.substring(0, 8)})` };
    const nonAdminMessage = chat.find(msg => msg.sender.type !== 'admin');
    if (nonAdminMessage) {
        const participantUser = users.find(u => u.email === nonAdminMessage.sender.id);
        if (participantUser) {
            return { ...participantUser, name: participantUser.name || 'مستخدم مسجل' };
        }
        if (nonAdminMessage.sender.type === 'guest') {
            return nonAdminMessage.sender;
        }
    }
    const participant = users.find(u => u.email === chatId || u.id === chatId);
    return participant ? { name: participant.name, email: participant.email } : { name: `مستخدم (${chatId.substring(0, 8)})` };
  };

  const handleCreateGroup = () => {
    if (newGroupData.name.trim() && newGroupData.members.length > 0) {
      const membersWithAdmin = [...new Set([user.email, ...newGroupData.members])];
      const newGroup = createChatGroup(newGroupData.name, membersWithAdmin);
      setActiveTab('internal-chat');
      setActiveChatId(newGroup.id);
      setIsCreateGroupOpen(false);
      setNewGroupData({ name: '', members: [] });
    } else {
      toast({ title: "بيانات غير كافية", description: "يجب إدخال اسم للمجموعة واختيار عضو واحد على الأقل.", variant: "destructive" });
    }
  };

  const handleMemberSelection = (email) => {
    setNewGroupData(prev => {
      const members = prev.members.includes(email)
        ? prev.members.filter(m => m !== email)
        : [...prev.members, email];
      return { ...prev, members };
    });
  };

  const filteredUsers = users.filter(u => u.email !== user.email && u.name.toLowerCase().includes(searchTerm.toLowerCase()));

  const ChatWindow = ({ chatId, isInternal }) => {
    const chatMessages = isInternal ? (internalMessages[chatId] || []) : (messages[chatId] || []);
    const participantInfo = isInternal ? null : getParticipantInfo(chatId);
    const isGroup = chatId.startsWith('group_');
    const groupInfo = isGroup ? chatGroups.find(g => g.id === chatId) : null;

    const getTitle = () => {
      if (isGroup) return groupInfo?.name || 'مجموعة';
      if (isInternal) {
        const otherUserEmail = chatId.split('_').find(id => id !== user.email);
        const otherUser = users.find(u => u.email === otherUserEmail);
        return otherUser?.name || 'مستخدم';
      }
      return participantInfo?.name;
    };

    const handleMicClick = () => {
      if (isRecording) stopRecording();
      else startRecording(chatId, isInternal);
    };

    return (
      <div className="flex flex-col h-full">
        <header className="p-4 border-b border-white/10 flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isGroup ? <Users className="h-6 w-6 text-primary" /> : <User className="h-6 w-6 text-primary" />}
            <h3 className="font-bold text-white text-lg">{getTitle()}</h3>
          </div>
          {!isInternal && participantInfo && (
            <div className="flex items-center gap-4 text-sm text-white/70">
              {participantInfo.email && <div className="flex items-center gap-1"><Mail className="h-4 w-4" /> {participantInfo.email}</div>}
              {participantInfo.phone && <div className="flex items-center gap-1"><Phone className="h-4 w-4" /> {participantInfo.phone}</div>}
            </div>
          )}
        </header>
        <main className="flex-1 p-4 overflow-y-auto space-y-4">
          {chatMessages.map(msg => (
             <ChatMessage
                key={msg.id}
                msg={msg}
                isMyMessage={msg.sender.id === user.email}
                onEdit={(messageId, newText) => handleEditMessage(chatId, messageId, newText, isInternal)}
                onDelete={(messageId) => handleDeleteMessage(chatId, messageId, isInternal)}
                getParticipantInfo={(participantId) => users.find(u => u.email === participantId)}
            />
          ))}
          <div ref={messagesEndRef} />
        </main>
        <footer className="p-4 border-t border-white/10">
          <div className="flex items-center gap-2">
            <Input type="text" placeholder="اكتب ردك هنا..." value={message} onChange={(e) => setMessage(e.target.value)} onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(chatId, isInternal)} className="bg-white/10 border-border text-white" disabled={isRecording} />
            <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={handleAttachment} disabled={isUploading || isRecording}><Paperclip className="h-5 w-5" /></Button>
            <input type="file" ref={fileInputRef} onChange={(e) => handleFileChange(e, chatId, isInternal)} className="hidden" />
            <Button size="icon" variant="ghost" className={`text-white/70 hover:text-white ${isRecording ? 'text-red-500 animate-pulse' : ''}`} onClick={handleMicClick} disabled={isUploading}>{isRecording ? <Square className="h-5 w-5" /> : <Mic className="h-5 w-5" />}</Button>
            <Button size="icon" className="bg-primary hover:bg-primary/90" onClick={() => handleSendMessage(chatId, isInternal)} disabled={isRecording}><Send className="h-5 w-5" /></Button>
          </div>
        </footer>
      </div>
    );
  };
  
  const handleSelectChat = (id) => {
    setActiveChatId(id);
    setMessage('');
  };

  const ChatList = ({ sessions, activeId, setActiveId, isInternal }) => (
    <div className="flex-1 overflow-y-auto">
      {sessions.map(chatId => {
        const participant = getParticipantInfo(chatId);
        const lastMessage = messages[chatId]?.slice(-1)[0];
        return (
          <div
            key={chatId}
            className={`p-4 cursor-pointer border-b border-white/10 ${activeId === chatId ? 'bg-primary/20' : 'hover:bg-secondary/80'}`}
            onClick={() => handleSelectChat(chatId)}
          >
            <p className="font-semibold text-white">{participant.name}</p>
            <p className="text-sm text-white/60 truncate">{lastMessage?.text || 'لا توجد رسائل بعد'}</p>
          </div>
        );
      })}
    </div>
  );

  const InternalChatList = ({ activeId, setActiveId }) => (
    <div className="flex-1 overflow-y-auto">
      {chatGroups.map(group => (
        <div key={group.id} className={`p-4 cursor-pointer border-b border-white/10 ${activeId === group.id ? 'bg-primary/20' : 'hover:bg-secondary/80'}`} onClick={() => handleSelectChat(group.id)}>
          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-primary" />
            <p className="font-semibold text-white">{group.name}</p>
          </div>
          <p className="text-sm text-white/60 truncate mt-1">{internalMessages[group.id]?.slice(-1)[0]?.text || 'لا توجد رسائل بعد'}</p>
        </div>
      ))}
      {users.filter(u => u.email !== user.email).map(u => {
        const chatId = getInternalChatId(user.email, u.email);
        return (
          <div key={u.id} className={`p-4 cursor-pointer border-b border-white/10 ${activeId === chatId ? 'bg-primary/20' : 'hover:bg-secondary/80'}`} onClick={() => handleSelectChat(chatId)}>
            <div className="flex items-center gap-3">
              <User className="h-5 w-5 text-primary" />
              <p className="font-semibold text-white">{u.name} <span className="text-xs text-white/50">({u.userType})</span></p>
            </div>
            <p className="text-sm text-white/60 truncate mt-1">{internalMessages[chatId]?.slice(-1)[0]?.text || 'لا توجد رسائل بعد'}</p>
          </div>
        );
      })}
    </div>
  );

  return (
    <>
      <Tabs value={activeTab} onValueChange={(value) => { setActiveTab(value); setActiveChatId(null); }} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-secondary/50">
          <TabsTrigger value="live-chat">الدردشة الحية</TabsTrigger>
          <TabsTrigger value="internal-chat">الدردشة الداخلية</TabsTrigger>
        </TabsList>
        <div className="glass-effect rounded-xl p-0 flex h-[75vh] mt-4">
            <aside className="w-1/3 border-l border-white/10 flex flex-col">
              <header className="p-4 border-b border-white/10 flex justify-between items-center">
                <h3 className="font-bold text-white text-lg">
                  {activeTab === 'live-chat' ? 'المحادثات المباشرة' : 'المحادثات الداخلية'}
                </h3>
                {activeTab === 'internal-chat' && (
                  <Button size="icon" variant="ghost" onClick={() => setIsCreateGroupOpen(true)}><PlusCircle className="h-6 w-6 text-primary" /></Button>
                )}
              </header>
              {activeTab === 'live-chat' ? (
                <ChatList sessions={liveChatSessions} activeId={activeChatId} setActiveId={handleSelectChat} isInternal={false} />
              ) : (
                <InternalChatList activeId={activeChatId} setActiveId={handleSelectChat} />
              )}
            </aside>
            <main className="w-2/3 flex flex-col">
                {activeChatId ? (
                    <ChatWindow chatId={activeChatId} isInternal={activeTab === 'internal-chat'} />
                ) : (
                    <div className="flex-1 flex flex-col items-center justify-center text-white/60">
                        {activeTab === 'live-chat' ? <MessageSquare className="h-16 w-16 mb-4" /> : <Users className="h-16 w-16 mb-4" />}
                        <p>{activeTab === 'live-chat' ? 'اختر محادثة لبدء الرد' : 'اختر محادثة أو أنشئ مجموعة جديدة'}</p>
                    </div>
                )}
            </main>
        </div>
      </Tabs>

      <Dialog open={isCreateGroupOpen} onOpenChange={setIsCreateGroupOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>إنشاء مجموعة جديدة</DialogTitle>
            <DialogDescription>اختر اسمًا للمجموعة وأضف الأعضاء.</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <Input placeholder="اسم المجموعة" value={newGroupData.name} onChange={(e) => setNewGroupData({ ...newGroupData, name: e.target.value })} className="bg-white/10 border-border" />
            <div className="relative">
              <Search className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input placeholder="ابحث عن مستخدمين..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="bg-white/10 border-border pr-10" />
            </div>
            <div className="max-h-60 overflow-y-auto space-y-2 p-2 rounded-md bg-black/20">
              {filteredUsers.map(u => (
                <div key={u.id} className="flex items-center space-x-2 space-x-reverse">
                  <Checkbox id={`member-${u.id}`} checked={newGroupData.members.includes(u.email)} onCheckedChange={() => handleMemberSelection(u.email)} className="border-white/50" />
                  <Label htmlFor={`member-${u.id}`} className="flex-1 cursor-pointer">{u.name} <span className="text-xs text-white/50">({u.userType})</span></Label>
                </div>
              ))}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateGroupOpen(false)}>إلغاء</Button>
            <Button className="bg-primary hover:bg-primary/90" onClick={handleCreateGroup}>إنشاء</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default LiveChatContent;