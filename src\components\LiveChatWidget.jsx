import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Send, Paperclip, Mic, User, Mail, Phone, Square } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { supabase } from '@/lib/customSupabaseClient';

const LiveChatWidget = ({ isOpen, setIsOpen }) => {
  const [message, setMessage] = useState('');
  const [visitorInfo, setVisitorInfo] = useState({ name: '', email: '', phone: '' });
  const [isRegistered, setIsRegistered] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const { messages, sendMessage, getSessionId } = useChat();
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  const sessionId = getSessionId();
  const chatMessages = messages[sessionId] || [];

  useEffect(() => {
    if (user) {
      setVisitorInfo({ name: user.name, email: user.email, phone: user.phone || '' });
      setIsRegistered(true);
    }
  }, [user]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  const handleRegisterVisitor = (e) => {
    e.preventDefault();
    if (visitorInfo.name && visitorInfo.email && visitorInfo.phone) {
      setIsRegistered(true);
      toast({ title: `مرحباً ${visitorInfo.name}!`, description: "يمكنك الآن بدء المحادثة." });
    } else {
      toast({ title: "بيانات غير مكتملة", description: "يرجى ملء جميع الحقول.", variant: "destructive" });
    }
  };

  const handleSendMessage = (fileInfo = null) => {
    const text = fileInfo ? `مرفق: ${fileInfo.name}` : message;
    if (text.trim() === '') return;
    
    const sender = user 
      ? { id: user.email, name: user.name, type: user.userType } 
      : { id: sessionId, name: visitorInfo.name, email: visitorInfo.email, phone: visitorInfo.phone, type: 'guest' };
      
    const messagePayload = { text, sender, fileLink: fileInfo ? fileInfo.link : null };
    sendMessage(sessionId, messagePayload);
    setMessage('');
  };

  const uploadToDrive = async (file) => {
    setIsUploading(true);
    toast({ title: 'جاري رفع الملف...', description: file.name });
    
    try {
        const { data, error } = await supabase.functions.invoke('upload-to-drive', {
            body: file,
            headers: { 'x-file-name': encodeURIComponent(file.name) },
        });

        if (error) throw error;
        if (!data.success) throw new Error(data.error || 'فشل الرفع إلى Google Drive');

        toast({ title: '🎉 تم إرفاق الملف بنجاح!' });
        return { name: file.name, link: data.webViewLink };
    } catch (error) {
        console.error('Upload error:', error);
        toast({ title: '❌ فشل الرفع', description: `حدث خطأ: ${error.message}`, variant: 'destructive' });
        return null;
    } finally {
        setIsUploading(false);
    }
  };

  const handleAttachment = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = async (event) => {
    if (event.target.files.length === 0) return;
    const file = event.target.files[0];
    const uploadedFile = await uploadToDrive(file);
    if (uploadedFile) handleSendMessage(uploadedFile);
    fileInputRef.current.value = "";
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      mediaRecorderRef.current.ondataavailable = event => {
        audioChunksRef.current.push(event.data);
      };
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const audioFile = new File([audioBlob], `تسجيل-صوتي-${Date.now()}.webm`, { type: 'audio/webm' });
        const uploadedFile = await uploadToDrive(audioFile);
        if (uploadedFile) handleSendMessage(uploadedFile);
        stream.getTracks().forEach(track => track.stop());
      };
      mediaRecorderRef.current.start();
      setIsRecording(true);
      toast({ title: "🎙️ بدأ التسجيل..." });
    } catch (err) {
      toast({ title: "خطأ في الوصول للميكروفون", description: "يرجى التأكد من منح الإذن لاستخدام الميكروفون.", variant: "destructive" });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      toast({ title: "🛑 توقف التسجيل", description: "جاري معالجة المقطع الصوتي..." });
    }
  };

  const handleMicClick = () => {
    if (isRecording) stopRecording();
    else startRecording();
  };

  const renderChatContent = () => {
    if (!isRegistered) {
      return (
        <div className="p-6 flex flex-col justify-center h-full">
          <h4 className="text-lg font-bold text-white mb-4 text-center">مرحباً بك في الدعم المباشر</h4>
          <p className="text-white/70 text-sm mb-6 text-center">يرجى إدخال بياناتك للمتابعة.</p>
          <form onSubmit={handleRegisterVisitor} className="space-y-4">
            <div><Label htmlFor="name" className="text-white/80">الاسم</Label><div className="relative"><User className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" /><Input id="name" value={visitorInfo.name} onChange={(e) => setVisitorInfo({...visitorInfo, name: e.target.value})} className="bg-white/10 border-border text-white pr-10" required /></div></div>
            <div><Label htmlFor="email" className="text-white/80">البريد الإلكتروني</Label><div className="relative"><Mail className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" /><Input id="email" type="email" value={visitorInfo.email} onChange={(e) => setVisitorInfo({...visitorInfo, email: e.target.value})} className="bg-white/10 border-border text-white pr-10" required /></div></div>
            <div><Label htmlFor="phone" className="text-white/80">رقم الهاتف</Label><div className="relative"><Phone className="absolute right-3 top-3 h-4 w-4 text-muted-foreground" /><Input id="phone" type="tel" value={visitorInfo.phone} onChange={(e) => setVisitorInfo({...visitorInfo, phone: e.target.value})} className="bg-white/10 border-border text-white pr-10" required /></div></div>
            <Button type="submit" className="w-full bg-primary hover:bg-primary/90">بدء المحادثة</Button>
          </form>
        </div>
      );
    }

    return (
      <>
        <main className="flex-1 p-4 overflow-y-auto space-y-4">
          {chatMessages.map((msg) => (
            <div key={msg.id} className={`flex ${msg.sender.type === 'admin' ? 'justify-start' : 'justify-end'}`}>
              <div className={`max-w-[80%] p-3 rounded-xl ${msg.sender.type === 'admin' ? 'bg-primary/30 rounded-br-none' : 'bg-white/10 rounded-bl-none'}`}>
                {msg.fileLink ? (
                  <a href={msg.fileLink} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline flex items-center gap-2">
                    <Paperclip className="h-4 w-4" /> {msg.text.replace('مرفق: ', '')}
                  </a>
                ) : (
                  <p className="text-sm text-white">{msg.text}</p>
                )}
                 <div className="flex items-center justify-end gap-2">
                    {msg.edited && <span className="text-xs text-white/50">(تم التعديل)</span>}
                    <p className="text-xs text-white/50 mt-1">{new Date(msg.timestamp).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</p>
                 </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </main>
        <footer className="p-4 border-t border-white/10">
          <div className="flex items-center gap-2">
            <Input type="text" placeholder="اكتب رسالتك هنا..." value={message} onChange={(e) => setMessage(e.target.value)} onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()} className="bg-white/10 border-border text-white" />
            <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={handleAttachment} disabled={isUploading || isRecording}><Paperclip className="h-5 w-5" /></Button>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" accept="image/*,application/pdf,audio/*" />
            <Button size="icon" variant="ghost" className={`text-white/70 hover:text-white ${isRecording ? 'text-red-500 animate-pulse' : ''}`} onClick={handleMicClick} disabled={isUploading}>{isRecording ? <Square className="h-5 w-5" /> : <Mic className="h-5 w-5" />}</Button>
            <Button size="icon" className="bg-primary hover:bg-primary/90" onClick={() => handleSendMessage()}><Send className="h-5 w-5" /></Button>
          </div>
        </footer>
      </>
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div 
            initial={{ opacity: 0, y: 20, scale: 0.95 }} 
            animate={{ opacity: 1, y: 0, scale: 1 }} 
            exit={{ opacity: 0, y: 20, scale: 0.95 }} 
            transition={{ duration: 0.2 }} 
            className="fixed bottom-24 right-4 sm:right-8 w-[calc(100%-2rem)] sm:w-96 h-[60vh] max-h-[70vh] bg-secondary/80 backdrop-blur-lg border border-white/20 rounded-2xl shadow-2xl flex flex-col z-50" 
            style={{ direction: 'rtl' }}
        >
          <header className="p-4 border-b border-white/10 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="font-bold text-white">الدردشة الحية</h3>
              <p className="text-xs text-green-400 flex items-center gap-1">
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                </span>
                متصل
              </p>
            </div>
            <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={() => setIsOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </header>
          {renderChatContent()}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LiveChatWidget;