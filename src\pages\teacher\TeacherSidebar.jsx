import React from 'react';
import { Button } from '@/components/ui/button';
import { LayoutDashboard, Calendar, Users, BookOpen, MessageSquare, BarChart, User, BarChart2, Clock, CheckCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useSettings } from '@/contexts/SettingsContext';

const TeacherSidebar = ({ activeTab, setActiveTab }) => {
    const { logout } = useAuth();
    const { settings } = useSettings();

    const navItems = [
        { id: 'dashboard', icon: LayoutDashboard, label: 'لوحة التحكم الرئيسية' },
        { id: 'schedule', icon: Calendar, label: 'جدول الحصص الأسبوعي' },
        { id: 'upcoming-sessions', icon: Clock, label: 'الحصص القادمة' },
        { id: 'finished-lessons', icon: CheckCircle, label: 'الحصص المنتهية' },
        { id: 'students', icon: Users, label: 'الطلاب' },
        { id: 'media-library', icon: BookOpen, label: 'مكتبة الوسائط' },
        { id: 'chat', icon: MessageSquare, label: 'المحادثات' },
        { id: 'reports', icon: BarChart, label: 'تقارير الحصص' },
        { id: 'sales-reports', icon: BarChart2, label: 'تقارير المبيعات' },
        { id: 'profile', icon: User, label: 'الملف الشخصي' },
    ];

    return (
        <aside className="w-64 bg-secondary p-6 flex flex-col justify-between h-screen sticky top-0">
            <div>
                <div className="flex items-center justify-center mb-10">
                    <img src={settings.academyLogoUrl || "https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png"} alt="شعار الأكاديمية" className="h-16 w-auto" />
                </div>
                <nav className="space-y-2">
                    {navItems.map(item => (
                        <Button
                            key={item.id}
                            variant={activeTab === item.id ? 'secondary' : 'ghost'}
                            className={`w-full justify-start text-right text-white ${activeTab === item.id ? 'bg-primary text-primary-foreground' : ''}`}
                            onClick={() => setActiveTab(item.id)}
                        >
                            <item.icon className="h-4 w-4 ml-3" />
                            {item.label}
                        </Button>
                    ))}
                </nav>
            </div>
            <Button variant="destructive" onClick={logout} className="w-full">تسجيل الخروج</Button>
        </aside>
    );
};

export default TeacherSidebar;