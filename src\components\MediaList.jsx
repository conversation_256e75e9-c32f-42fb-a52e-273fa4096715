
import React from 'react';
    import { But<PERSON> } from '@/components/ui/button';
    import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
    import { Edit, Trash2, Video, FileText, Image, Mic, Eye, User, Users, Download } from 'lucide-react';
    import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

    const getFileTypeIcon = (fileType) => {
        if (fileType?.startsWith('فيديو')) return <Video />;
        if (fileType === 'ملف PDF') return <FileText />;
        if (fileType?.startsWith('صورة')) return <Image />;
        if (fileType?.startsWith('صوت')) return <Mic />;
        return <Eye />;
    };

    const MediaList = ({ media, assignments, onDelete, students, teachers, loading, isTeacherView = false }) => {

        const getUploaderName = (email) => {
            if(isTeacherView) return '';
            const teacher = teachers.find(t => t.email === email);
            return teacher ? teacher.name : 'الإدارة';
        };
        
        const getStudentNames = (mediaId) => {
            const assignedEmails = assignments.filter(a => a.material_id === mediaId).map(a => a.student_email);
            if (assignedEmails.length === 0) return "متاح للجميع";
            return assignedEmails.map(email => students.find(s => s.email === email)?.name || email).join(', ');
        };

        if (loading) {
            return <p className="text-center py-8 text-white/70">جاري تحميل المواد...</p>;
        }

        if (media.length === 0) {
            return <p className="text-center py-8 text-white/70">لا توجد مواد مرفوعة بعد.</p>;
        }

        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {media.map((item) => {
                    const icon = getFileTypeIcon(item.file_type);
                    const assignedCount = assignments.filter(a => a.material_id === item.id).length;
                    return(
                    <div key={item.id} className="bg-secondary/50 rounded-lg p-6 hover-lift flex flex-col justify-between">
                        <div className="text-center">
                            <div className="bg-gradient-to-br from-primary/50 to-blue-500/50 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white">
                                {React.cloneElement(icon, { className: "h-8 w-8" })}
                            </div>
                            <h4 className="font-bold text-white mb-1 truncate" title={item.lesson_title}>{item.lesson_title}</h4>
                            <p className="text-white/80 text-sm mb-1">{item.subject}{item.other_subject_name ? ` (${item.other_subject_name})` : ''}</p>
                            <p className="text-white/70 text-sm mb-2">{item.grade}</p>
                            {!isTeacherView && <p className="text-white/60 text-xs mb-1">المعلم: {getUploaderName(item.uploaded_by_email)}</p>}
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <div className="text-white/70 text-sm mb-2 flex items-center justify-center gap-1 cursor-pointer">
                                            {assignedCount > 1 ? <Users className="h-4 w-4"/> : <User className="h-4 w-4"/>}
                                            {assignedCount > 0 ? `مخصص لـ ${assignedCount} طالب` : 'متاح للجميع'}
                                        </div>
                                    </TooltipTrigger>
                                    <TooltipContent className="max-w-xs">
                                        <p>{getStudentNames(item.id)}</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                            <p className="text-white/60 text-xs mb-4">تاريخ الرفع: {new Date(item.created_at).toLocaleDateString('ar-SA')}</p>
                        </div>
                        <div className="flex justify-center gap-2 mt-4">
                            <Button asChild variant="outline" size="sm" className="border-border text-white hover:bg-secondary/80">
                                <a href={item.view_link} target="_blank" rel="noopener noreferrer"><Download className="h-4 w-4 ml-1" />تحميل</a>
                            </Button>
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <Button variant="destructive" size="sm"><Trash2 className="h-4 w-4 ml-1" />حذف</Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent className="bg-secondary border-border text-white">
                                    <AlertDialogHeader><AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle><AlertDialogDescription>سيتم حذف المادة وتخصيصاتها بشكل نهائي.</AlertDialogDescription></AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                        <AlertDialogAction onClick={() => onDelete(item.id)} className="bg-destructive hover:bg-destructive/90">حذف</AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </div>
                    </div>
                )})}
            </div>
        );
    };

    export default MediaList;
