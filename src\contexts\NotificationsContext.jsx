import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { toast as sonnerToast } from 'sonner';
import { useAuth } from './AuthContext';
import notificationSound from '@/assets/notification.mp3';
import { useNavigate } from 'react-router-dom';

const NotificationsContext = createContext();

export const useNotifications = () => {
    const context = useContext(NotificationsContext);
    if (context === undefined) {
        throw new Error('useNotifications must be used within a NotificationsProvider');
    }
    return context;
};

const NOTIFICATIONS_DB_KEY = 'gulfAcademyNotifications';

export const NotificationsProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const { user } = useAuth();
  const audioRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    try {
      const storedNotifications = JSON.parse(localStorage.getItem(NOTIFICATIONS_DB_KEY) || '[]');
      setNotifications(storedNotifications);
    } catch (error) {
      console.error("Error loading notifications from localStorage", error);
      localStorage.removeItem(NOTIFICATIONS_DB_KEY);
    }
    
    const initAudio = () => {
        if (!audioRef.current) {
            audioRef.current = new Audio(notificationSound);
        }
    };
    document.addEventListener('click', initAudio, { once: true });
    
    return () => {
        document.removeEventListener('click', initAudio);
    }
    
  }, []);

  const updateNotificationsInStorage = (updatedNotifications) => {
    setNotifications(updatedNotifications);
    localStorage.setItem(NOTIFICATIONS_DB_KEY, JSON.stringify(updatedNotifications));
  };
  
  const createNotification = (recipientEmail, message, type = 'info', link = '#') => {
    const newNotification = {
      id: uuidv4(),
      recipientEmail,
      message,
      type,
      link,
      read: false,
      createdAt: new Date().toISOString(),
    };

    setNotifications(prev => {
        const updated = [...prev, newNotification];
        localStorage.setItem(NOTIFICATIONS_DB_KEY, JSON.stringify(updated));
        return updated;
    });
    
    if(user && (user.email === recipientEmail || user.userType === recipientEmail)) {
        showToast(message, type, link);
        if (audioRef.current) {
            audioRef.current.play().catch(e => console.log("Audio play failed:", e));
        }
    }
  };

  const showToast = (message, type, link) => {
       const toastOptions = {
            description: message,
            action: link && link !== '#' ? {
                label: 'عرض',
                onClick: () => {
                    navigate(link);
                }
            } : undefined
        };
       switch (type) {
            case 'success':
                sonnerToast.success('نجاح', toastOptions);
                break;
            case 'error':
                sonnerToast.error('خطأ', toastOptions);
                break;
            case 'warning':
                sonnerToast.warning('تنبيه', toastOptions);
                break;
            case 'chat':
                sonnerToast.message('رسالة جديدة', toastOptions);
                break;
            case 'announcement':
                sonnerToast.info('إشعار من الإدارة', toastOptions);
                break;
            case 'booking':
                sonnerToast.info('تحديث الحجز', toastOptions);
                break;
             case 'exam':
                sonnerToast.info('تذكير بالاختبار', toastOptions);
                break;
            default:
                sonnerToast.info('إشعار جديد', toastOptions);
        }
  }

  const markAsRead = (notificationId) => {
    const updatedNotifications = notifications.map(n =>
      n.id === notificationId ? { ...n, read: true } : n
    );
    updateNotificationsInStorage(updatedNotifications);
  };
  
  const markAllAsRead = (recipientEmail, userType) => {
      const updatedNotifications = notifications.map(n => 
          (n.recipientEmail === recipientEmail || n.recipientEmail === userType) ? { ...n, read: true } : n
      );
      updateNotificationsInStorage(updatedNotifications);
  }
  
  const deleteAllForUser = (recipientEmail, userType) => {
     const remainingNotifications = notifications.filter(n => n.recipientEmail !== recipientEmail && n.recipientEmail !== userType);
     updateNotificationsInStorage(remainingNotifications);
  }

  const getUserNotifications = (recipientEmail, userType) => {
    return notifications
      .filter(n => n.recipientEmail === recipientEmail || n.recipientEmail === userType)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  };

  const value = {
    notifications,
    createNotification,
    markAsRead,
    markAllAsRead,
    deleteAllForUser,
    getUserNotifications,
  };

  return <NotificationsContext.Provider value={value}>{children}</NotificationsContext.Provider>;
};