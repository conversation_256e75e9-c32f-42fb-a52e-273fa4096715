// External Database API Client
// عميل API قاعدة البيانات الخارجية

// إعدادات API
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'https://your-api-endpoint.com/api',
  apiKey: import.meta.env.VITE_API_KEY || '',
  timeout: 10000
};

// عميل API للتعامل مع قاعدة البيانات الخارجية
class ExternalAPIClient {
  constructor(config = API_CONFIG) {
    this.config = config;
  }

  // دالة مساعدة لإرسال طلبات HTTP
  async request(endpoint, options = {}) {
    const url = `${this.config.baseURL}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      timeout: this.config.timeout
    };

    const finalOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    };

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        ...finalOptions,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('انتهت مهلة الطلب');
      }
      console.error('خطأ في طلب API:', error);
      throw error;
    }
  }

  // طلبات GET
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url, { method: 'GET' });
  }

  // طلبات POST
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // طلبات PUT
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // طلبات DELETE
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // طلبات PATCH
  async patch(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }
}

// إنشاء instance من العميل
const apiClient = new ExternalAPIClient();

// دوال API للمستخدمين
export const usersAPI = {
  // إنشاء مستخدم جديد
  async create(userData) {
    return apiClient.post('/users', userData);
  },

  // تسجيل دخول المستخدم
  async login(credentials) {
    return apiClient.post('/users/login', credentials);
  },

  // جلب معلومات المستخدم
  async getById(userId) {
    return apiClient.get(`/users/${userId}`);
  },

  // تحديث معلومات المستخدم
  async update(userId, userData) {
    return apiClient.put(`/users/${userId}`, userData);
  },

  // حذف المستخدم
  async delete(userId) {
    return apiClient.delete(`/users/${userId}`);
  },

  // جلب جميع المستخدمين (للمدير)
  async getAll(filters = {}) {
    return apiClient.get('/users', filters);
  }
};

// دوال API للأجهزة
export const devicesAPI = {
  // تسجيل جهاز جديد
  async register(deviceData) {
    return apiClient.post('/devices', deviceData);
  },

  // التحقق من الجهاز
  async verify(userEmail, deviceFingerprint) {
    return apiClient.post('/devices/verify', {
      user_email: userEmail,
      device_fingerprint: deviceFingerprint
    });
  },

  // جلب أجهزة المستخدم
  async getUserDevices(userEmail) {
    return apiClient.get(`/devices/user/${encodeURIComponent(userEmail)}`);
  },

  // جلب جميع الأجهزة (للمدير)
  async getAll(filters = {}) {
    return apiClient.get('/devices', filters);
  },

  // تحديث آخر ظهور للجهاز
  async updateLastSeen(userEmail, deviceId) {
    return apiClient.patch(`/devices/${deviceId}/last-seen`, {
      user_email: userEmail
    });
  },

  // حذف جهاز
  async delete(deviceId) {
    return apiClient.delete(`/devices/${deviceId}`);
  },

  // إلغاء تفعيل جهاز
  async deactivate(deviceId) {
    return apiClient.patch(`/devices/${deviceId}/deactivate`);
  }
};

// دوال API لسجلات تسجيل الدخول
export const loginAttemptsAPI = {
  // تسجيل محاولة دخول
  async log(attemptData) {
    return apiClient.post('/login-attempts', attemptData);
  },

  // جلب محاولات المستخدم
  async getUserAttempts(userEmail, filters = {}) {
    return apiClient.get(`/login-attempts/user/${encodeURIComponent(userEmail)}`, filters);
  },

  // جلب جميع المحاولات (للمدير)
  async getAll(filters = {}) {
    return apiClient.get('/login-attempts', filters);
  }
};

// دوال API لإعدادات النظام
export const settingsAPI = {
  // جلب إعداد
  async get(key) {
    return apiClient.get(`/settings/${key}`);
  },

  // تحديث إعداد
  async update(key, value, type = 'string') {
    return apiClient.put(`/settings/${key}`, {
      value,
      type
    });
  },

  // جلب جميع الإعدادات
  async getAll() {
    return apiClient.get('/settings');
  },

  // تحديث عدة إعدادات
  async updateMultiple(settings) {
    return apiClient.put('/settings/bulk', { settings });
  }
};

// دوال API للإحصائيات
export const statsAPI = {
  // إحصائيات الأجهزة
  async getDeviceStats() {
    return apiClient.get('/stats/devices');
  },

  // إحصائيات المستخدمين
  async getUserStats() {
    return apiClient.get('/stats/users');
  },

  // إحصائيات تسجيل الدخول
  async getLoginStats(period = '7d') {
    return apiClient.get('/stats/login', { period });
  },

  // إحصائيات عامة
  async getOverview() {
    return apiClient.get('/stats/overview');
  }
};

// دالة للتحقق من حالة API
export const healthCheck = async () => {
  try {
    const response = await apiClient.get('/health');
    return {
      status: 'healthy',
      ...response
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
};

// دالة لتكوين API
export const configureAPI = (newConfig) => {
  Object.assign(API_CONFIG, newConfig);
};

// تصدير العميل الافتراضي
export default apiClient;

// معالج الأخطاء العام
export const handleAPIError = (error) => {
  console.error('خطأ في API:', error);
  
  // رسائل خطأ مخصصة
  const errorMessages = {
    'Network Error': 'خطأ في الشبكة. تحقق من اتصال الإنترنت.',
    'Unauthorized': 'غير مصرح. يرجى تسجيل الدخول مرة أخرى.',
    'Forbidden': 'ممنوع. ليس لديك صلاحية للوصول.',
    'Not Found': 'المورد غير موجود.',
    'Internal Server Error': 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
    'انتهت مهلة الطلب': 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.'
  };

  return errorMessages[error.message] || error.message || 'حدث خطأ غير متوقع';
};

// دالة للتحقق من توفر API
export const isAPIAvailable = async () => {
  try {
    const health = await healthCheck();
    return health.status === 'healthy';
  } catch (error) {
    return false;
  }
};
