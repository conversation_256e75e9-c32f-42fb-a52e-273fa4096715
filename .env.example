# Gulf Academy - Device Controller Configuration
# إعدادات نظام التحكم في الأجهزة - Gulf Academy

# External Database API Configuration
# إعدادات API قاعدة البيانات الخارجية
VITE_API_BASE_URL=https://your-api-endpoint.com/api
VITE_API_KEY=your_api_key_here
VITE_API_TIMEOUT=10000

# Device Security Settings
# إعدادات أمان الأجهزة
DEVICE_SECURITY_ENABLED=true
MAX_DEVICES_PER_USER=1
DEVICE_VERIFICATION_REQUIRED=true
FAILED_LOGIN_ATTEMPTS_LIMIT=5
DEVICE_SESSION_TIMEOUT_HOURS=24

# Application Settings
# إعدادات التطبيق
NODE_ENV=development
VITE_APP_NAME=Gulf Academy
VITE_APP_VERSION=1.0.0

# Fallback Mode Configuration
# إعدادات وضع الاحتياطي
VITE_ENABLE_FALLBACK_MODE=true
VITE_FALLBACK_TO_LOCALSTORAGE=true

# Security Settings
# إعدادات الأمان
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Supabase Configuration (if using)
# إعدادات Supabase (في حالة الاستخدام)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Email Configuration (for notifications)
# إعدادات البريد الإلكتروني (للإشعارات)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_NAME=Gulf Academy
SMTP_FROM_EMAIL=<EMAIL>

# Logging Configuration
# إعدادات السجلات
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
ENABLE_DEVICE_LOGGING=true

# Rate Limiting
# تحديد معدل الطلبات
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
# إعدادات الجلسات
SESSION_SECRET=your_session_secret_here
SESSION_TIMEOUT_MINUTES=60
REMEMBER_ME_DURATION_DAYS=30
