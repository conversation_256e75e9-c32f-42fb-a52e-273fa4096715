import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { useToast } from '@/components/ui/use-toast';
import SessionReminderModal from '@/components/SessionReminderModal';
import { parseISO, isAfter, differenceInMinutes } from 'date-fns';
import { useNavigate } from 'react-router-dom';

const SessionReminderHandler = () => {
  const { user, isAuthenticatedWithCode } = useAuth();
  const { bookings, trackSessionJoin, trackSessionLeave } = useBooking();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [reminder, setReminder] = useState({
    isOpen: false,
    session: null,
    booking: null,
  });

  const nextUpcomingSession = useMemo(() => {
    if (!user) return null;

    const userBookings = bookings.filter(b => 
        b.status === 'approved' &&
        (b.teacherEmail === user.email || (b.studentEmails && b.studentEmails.includes(user.email)))
    );

    let closestSession = null;
    let closestBooking = null;

    userBookings.forEach(booking => {
      (booking.sessions || []).forEach(session => {
        if (session.status === 'scheduled') {
          const sessionTime = parseISO(`${session.date}T${session.time}`);
          if (isAfter(sessionTime, new Date())) {
            if (!closestSession || isAfter(parseISO(`${closestSession.date}T${closestSession.time}`), sessionTime)) {
              closestSession = session;
              closestBooking = booking;
            }
          }
        }
      });
    });

    return closestSession ? { session: closestSession, booking: closestBooking } : null;
  }, [bookings, user]);

  useEffect(() => {
    if (!nextUpcomingSession) return;

    const timer = setInterval(() => {
      const now = new Date();
      const sessionTime = parseISO(`${nextUpcomingSession.session.date}T${nextUpcomingSession.session.time}`);
      const minutesUntilSession = differenceInMinutes(sessionTime, now);

      if (minutesUntilSession > 0 && minutesUntilSession <= 15) {
        // Show reminder if it's not already shown for this session
        if (!reminder.isOpen || reminder.session?.sessionId !== nextUpcomingSession.session.sessionId) {
          if ((user.userType === 'student' && isAuthenticatedWithCode) || user.userType === 'teacher') {
             setReminder({
                isOpen: true,
                session: nextUpcomingSession.session,
                booking: nextUpcomingSession.booking,
             });
          }
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(timer);

  }, [nextUpcomingSession, reminder.isOpen, reminder.session?.sessionId, isAuthenticatedWithCode, user.userType]);

  const handleJoinSession = useCallback((booking, session) => {
    if (user.userType === 'student' && !isAuthenticatedWithCode) {
      toast({
        title: "الوصول مقيد",
        description: "يرجى تسجيل الدخول بكود الوصول للانضمام للحصة.",
        variant: "destructive"
      });
      navigate('/login/access-code');
      return;
    }

    if (booking.zoomLink && session) {
      trackSessionJoin(booking.id, session.sessionId, user.userType);
      const newWindow = window.open(booking.zoomLink, '_blank', 'noopener,noreferrer');
      const checkWindowClosed = setInterval(() => {
        if (!newWindow || newWindow.closed) {
          trackSessionLeave(booking.id, session.sessionId, user.userType);
          clearInterval(checkWindowClosed);
        }
      }, 1000);
      setReminder(prev => ({ ...prev, isOpen: false }));
    } else {
      toast({
        title: "خطأ",
        description: "رابط الحصة غير متوفر بعد.",
        variant: "destructive"
      });
    }
  }, [isAuthenticatedWithCode, navigate, toast, trackSessionJoin, trackSessionLeave, user.userType]);

  return (
    <SessionReminderModal
      session={reminder.session}
      booking={reminder.booking}
      userType={user.userType}
      isOpen={reminder.isOpen}
      onOpenChange={(isOpen) => setReminder(prev => ({ ...prev, isOpen }))}
      onJoinSession={handleJoinSession}
    />
  );
};

export default SessionReminderHandler;