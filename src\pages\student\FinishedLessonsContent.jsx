import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { CheckCircle } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';

const FinishedLessonsContent = () => {
  const { user } = useAuth();
  const { bookings } = useBooking();

  const finishedSessions = bookings
    .filter(b => b.studentEmails?.includes(user.email))
    .flatMap(b => 
      (b.sessions || [])
        .filter(s => s.status === 'completed')
        .map(s => ({
          ...s,
          bookingId: b.id,
          teacherName: b.teacherName,
          subject: b.subjects.map(sub => sub.subject).join(', '),
        }))
    )
    .sort((a,b) => parseISO(b.date) - parseISO(a.date));

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
          <CheckCircle className="text-green-400" />
          الدروس المنتهية
        </h3>
        {finishedSessions.length === 0 ? (
          <div className="text-center py-12 text-white/70">
            <p className="mt-4">لم تكمل أي دروس بعد.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-right">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="py-3 px-4 text-white font-medium">المادة</th>
                  <th className="py-3 px-4 text-white font-medium">المعلم</th>
                  <th className="py-3 px-4 text-white font-medium">تاريخ الإنجاز</th>
                </tr>
              </thead>
              <tbody>
                {finishedSessions.map((session) => (
                  <tr key={session.sessionId} className="border-b border-white/10">
                    <td className="py-3 px-4 text-white">{session.subject}</td>
                    <td className="py-3 px-4 text-white/70">{session.teacherName}</td>
                    <td className="py-3 px-4 text-white/70">{format(parseISO(session.date), 'EEEE, d MMMM yyyy', { locale: arSA })}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinishedLessonsContent;