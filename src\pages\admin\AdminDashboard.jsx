import React, { useState, useEffect } from 'react';
    import { motion } from 'framer-motion';
    import { Helmet } from 'react-helmet';
    import { useLocation, useNavigate } from 'react-router-dom';
    import { useAuth } from '@/contexts/AuthContext';
    import AdminSidebar from '@/pages/admin/AdminSidebar';
    import DashboardContent from '@/pages/admin/DashboardContent';
    import UsersContent from '@/pages/admin/UsersContent';
    import BookingsContent from '@/pages/admin/BookingsContent';
    import ReportsContent from '@/pages/admin/ReportsContent';
    import MediaLibraryContent from '@/pages/admin/MediaLibraryContent';
    import SettingsContent from '@/pages/admin/SettingsContent';
    import PackagesContent from '@/pages/admin/PackagesContent';
    import UpcomingLessonsContent from '@/pages/admin/UpcomingLessonsContent';
    import FinishedLessonsContent from '@/pages/admin/FinishedLessonsContent';
    import LiveChatContent from '@/pages/admin/LiveChatContent';
    import InquiriesContent from '@/pages/admin/InquiriesContent';
    import NotificationsDropdown from '@/components/NotificationsDropdown';
    import AccessCodesContent from '@/pages/admin/AccessCodesContent';
    import IntegrationsContent from '@/pages/admin/IntegrationsContent';

    const AdminDashboard = () => {
        const { user } = useAuth();
        const location = useLocation();
        const navigate = useNavigate();
        const [activeTab, setActiveTab] = useState('dashboard');

        useEffect(() => {
            const params = new URLSearchParams(location.search);
            const tab = params.get('tab');
            if (tab) {
                setActiveTab(tab);
            }
        }, [location.search]);

        const handleSetActiveTab = (tab) => {
            setActiveTab(tab);
            navigate(`/admin?tab=${tab}`);
        };

        const renderContent = () => {
            switch (activeTab) {
                case 'dashboard': return <DashboardContent setActiveTab={handleSetActiveTab} />;
                case 'users': return <UsersContent />;
                case 'bookings': return <BookingsContent />;
                case 'packages': return <PackagesContent />;
                case 'upcoming-lessons': return <UpcomingLessonsContent />;
                case 'finished-lessons': return <FinishedLessonsContent />;
                case 'reports': return <ReportsContent />;
                case 'media-library': return <MediaLibraryContent />;
                case 'live-chat': return <LiveChatContent />;
                case 'inquiries': return <InquiriesContent />;
                case 'access-codes': return <AccessCodesContent />;
                case 'integrations': return <IntegrationsContent />;
                case 'settings': return <SettingsContent />;
                default: return <DashboardContent setActiveTab={handleSetActiveTab} />;
            }
        };

        if (!user) {
            return null;
        }

        return (
            <>
                <Helmet>
                    <title>لوحة تحكم المدير - Gulf Academy</title>
                    <meta name="description" content="لوحة تحكم المدير في منصة Gulf Academy" />
                </Helmet>
                <div className="min-h-screen gradient-bg">
                    <div className="flex">
                        <AdminSidebar activeTab={activeTab} setActiveTab={handleSetActiveTab} />
                        <main className="flex-1 p-8 overflow-y-auto h-screen">
                            <div className="flex justify-between items-center mb-8">
                                <div>
                                    <h1 className="text-3xl font-bold text-white mb-2">لوحة تحكم المدير</h1>
                                    <p className="text-white/70">مرحباً بعودتك، {user.name}!</p>
                                </div>
                                <NotificationsDropdown />
                            </div>
                            <motion.div
                                key={activeTab}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5 }}
                            >
                                {renderContent()}
                            </motion.div>
                        </main>
                    </div>
                </div>
            </>
        );
    };

    export default AdminDashboard;