import React, { useState, useMemo, useCallback } from 'react';
import { useBooking } from '@/contexts/BookingContext';
import { useAuth } from '@/contexts/AuthContext';
import { format, parseISO, isFuture } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Link as LinkIcon, XCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const UpcomingLessonsContent = () => {
    const { bookings, markSessionAttendance } = useBooking();
    const { users } = useAuth();
    const { toast } = useToast();
    const [searchTerm, setSearchTerm] = useState('');
    const [filterTeacher, setFilterTeacher] = useState('');

    const teachers = useMemo(() => users.filter(u => u.userType === 'teacher'), [users]);

    const upcomingSessions = useMemo(() => {
        let allSessions = [];
        bookings.forEach(booking => {
            if (booking.status === 'approved' && booking.sessions) {
                booking.sessions.forEach(session => {
                    const sessionDateTime = parseISO(`${session.date}T${session.time}`);
                    if (session.status === 'scheduled' && isFuture(sessionDateTime)) {
                        allSessions.push({
                            ...session,
                            bookingId: booking.id,
                            studentEmails: booking.studentEmails,
                            studentNames: booking.studentNames,
                            teacherEmail: booking.teacherEmail,
                            teacherName: booking.teacherName,
                            subjects: booking.subjects,
                            sessionDateTime,
                            country: booking.country,
                            zoomLink: booking.zoomLink,
                        });
                    }
                });
            }
        });

        if (filterTeacher) {
            allSessions = allSessions.filter(session => session.teacherEmail === filterTeacher);
        }

        if (searchTerm) {
            const lowercasedFilter = searchTerm.toLowerCase();
            allSessions = allSessions.filter(session => {
                const studentNames = session.studentEmails?.map(email => session.studentNames[email] || '').join(' ').toLowerCase();
                const countryName = (session.country?.name || (typeof session.country === 'string' ? session.country : ''))?.toLowerCase();
                return (
                    studentNames.includes(lowercasedFilter) ||
                    session.teacherName?.toLowerCase().includes(lowercasedFilter) ||
                    countryName.includes(lowercasedFilter) ||
                    session.subjects.some(s => s.subject.toLowerCase().includes(lowercasedFilter))
                );
            });
        }

        return allSessions.sort((a, b) => a.sessionDateTime - b.sessionDateTime);
    }, [bookings, searchTerm, filterTeacher]);
    
    const handleJoin = (zoomLink) => {
        if (zoomLink) {
            window.open(zoomLink, '_blank', 'noopener,noreferrer');
        } else {
            toast({ title: 'خطأ', description: 'رابط الحصة غير متوفر.', variant: 'destructive' });
        }
    };
    
    const handleEndLesson = useCallback((bookingId, sessionId) => {
        markSessionAttendance(bookingId, sessionId, 'absent', 0);
        toast({ title: 'تم إنهاء الحصة', description: 'تم تسجيل الحصة على أنها غياب لعدم الحضور.' });
    }, [markSessionAttendance, toast]);


    const formatDate = (date) => format(date, 'eeee, d MMMM yyyy', { locale: arSA });
    const formatTime = (date) => format(date, 'p', { locale: arSA });

    return (
        <div className="glass-effect rounded-xl p-6">
            <h3 className="text-2xl font-bold text-white mb-6">الحصص القادمة لجميع المعلمين</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 bg-white/5 p-4 rounded-lg">
                <div>
                    <Label htmlFor="search" className="text-white/80">بحث</Label>
                    <Input
                        id="search"
                        type="text"
                        placeholder="ابحث باسم الطالب, المعلم, الدولة, أو المادة..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="bg-secondary border-border text-white"
                    />
                </div>
                <div>
                    <Label htmlFor="teacher-filter" className="text-white/80">فلترة بالمعلم</Label>
                    <Select value={filterTeacher} onValueChange={setFilterTeacher}>
                        <SelectTrigger id="teacher-filter" className="bg-secondary border-border">
                            <SelectValue placeholder="اختر معلماً" />
                        </SelectTrigger>
                        <SelectContent className="bg-secondary border-border text-white">
                            <SelectItem value="">كل المعلمين</SelectItem>
                            {teachers.map(teacher => (
                                <SelectItem key={teacher.id} value={teacher.email}>
                                    {teacher.name}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="w-full text-right min-w-[1200px]">
                    <thead>
                        <tr className="border-b border-white/20">
                            <th className="py-3 px-2 text-white font-medium">المعلم</th>
                            <th className="py-3 px-2 text-white font-medium">الطلاب</th>
                            <th className="py-3 px-2 text-white font-medium">الدولة</th>
                            <th className="py-3 px-2 text-white font-medium">المادة</th>
                            <th className="py-3 px-2 text-white font-medium">التاريخ</th>
                            <th className="py-3 px-2 text-white font-medium">الوقت</th>
                            <th className="py-3 px-2 text-white font-medium text-center">إجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {upcomingSessions.length > 0 ? (
                            upcomingSessions.map(session => {
                                const studentNames = session.studentEmails?.map(email => session.studentNames[email] || email).join(', ');
                                const countryName = session.country?.name || (typeof session.country === 'string' ? session.country : 'غير محدد');
                                return (
                                <tr key={session.sessionId} className="border-b border-white/10 hover:bg-white/5 text-sm">
                                    <td className="py-3 px-2 text-white">{session.teacherName}</td>
                                    <td className="py-3 px-2 text-white/80">{studentNames}</td>
                                    <td className="py-3 px-2 text-white/80">{countryName}</td>
                                    <td className="py-3 px-2 text-white/80">{session.subjects.map(s => s.subject).join(', ')}</td>
                                    <td className="py-3 px-2 text-white/80">{formatDate(session.sessionDateTime)}</td>
                                    <td className="py-3 px-2 text-primary font-semibold">{formatTime(session.sessionDateTime)}</td>
                                    <td className="py-3 px-2 text-center">
                                      <div className="flex gap-2 justify-center">
                                        <Button size="sm" variant="outline" onClick={() => handleJoin(session.zoomLink)} disabled={!session.zoomLink}>
                                            <LinkIcon className="h-4 w-4 ml-1" />
                                            انضمام
                                        </Button>
                                        <Button size="sm" variant="destructive" onClick={() => handleEndLesson(session.bookingId, session.sessionId)}>
                                            <XCircle className="h-4 w-4 ml-1" />
                                            إنهاء
                                        </Button>
                                      </div>
                                    </td>
                                </tr>
                            )})
                        ) : (
                            <tr>
                                <td colSpan="7" className="text-center py-8 text-white/70">
                                    لا توجد حصص قادمة.
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default UpcomingLessonsContent;