import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Edit, Trash2, Check, X, File, PlayCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { formatDistanceToNow } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ChatMessage = ({ msg, isMyMessage, onEdit, onDelete, getParticipantInfo, isReadOnly }) => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(msg.text);
  const senderInfo = isMyMessage ? user : getParticipantInfo(msg.sender.id) || msg.sender;
  
  const handleEditSubmit = () => {
    onEdit(msg.id, editText);
    setIsEditing(false);
  };
  
  const renderMessageContent = () => {
    if (msg.fileLink) {
      const isAudio = msg.fileLink.includes('.webm') || msg.fileLink.includes('.mp3') || msg.fileLink.includes('audio');
      if (isAudio) {
        return (
          <div className="flex items-center gap-2">
            <PlayCircle className="h-5 w-5 text-primary"/>
            <a href={msg.fileLink} target="_blank" rel="noopener noreferrer" className="underline text-primary">
              {msg.text || 'مقطع صوتي'}
            </a>
          </div>
        )
      }
      return (
        <a href={msg.fileLink} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 underline text-primary">
          <File className="h-5 w-5"/>
          {msg.text || 'عرض الملف'}
        </a>
      );
    }
    return <p className="whitespace-pre-wrap">{msg.text}</p>;
  }

  return (
    <div className={`flex items-start gap-3 ${isMyMessage ? 'justify-end' : ''}`}>
      {!isMyMessage && (
        <Avatar className="h-10 w-10">
          <AvatarImage src={senderInfo.profilePicture} />
          <AvatarFallback>{senderInfo.name ? senderInfo.name.charAt(0).toUpperCase() : '?'}</AvatarFallback>
        </Avatar>
      )}
      <div className={`flex flex-col ${isMyMessage ? 'items-end' : 'items-start'}`}>
        <div className={`p-3 rounded-lg max-w-sm ${isMyMessage ? 'bg-primary text-primary-foreground' : 'bg-secondary'}`}>
          {!isMyMessage && (
            <p className="text-xs font-bold text-primary mb-1">{senderInfo.name}</p>
          )}
          {isEditing ? (
            <div className="flex items-center gap-2">
              <Input
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                className="bg-background text-foreground h-8"
              />
              <Button size="icon" variant="ghost" className="h-8 w-8 text-green-400 hover:text-green-300" onClick={handleEditSubmit}>
                <Check className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="ghost" className="h-8 w-8 text-red-400 hover:text-red-300" onClick={() => setIsEditing(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            renderMessageContent()
          )}
        </div>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-muted-foreground">
            {formatDistanceToNow(new Date(msg.timestamp), { addSuffix: true, locale: arSA })}
            {msg.edited && ' (تم التعديل)'}
          </span>
          {isMyMessage && !isReadOnly && (user.userType === 'admin' || !msg.sender.type || msg.sender.type !== 'admin') && (
            <>
              <Button size="icon" variant="ghost" className="h-5 w-5" onClick={() => setIsEditing(true)}>
                <Edit className="h-3 w-3" />
              </Button>
              <Button size="icon" variant="ghost" className="h-5 w-5 text-destructive" onClick={() => onDelete(msg.id)}>
                <Trash2 className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>
      {isMyMessage && (
        <Avatar className="h-10 w-10">
          <AvatarImage src={user.profilePicture} />
          <AvatarFallback>{user.name.charAt(0).toUpperCase()}</AvatarFallback>
        </Avatar>
      )}
    </div>
  );
};

export default ChatMessage;