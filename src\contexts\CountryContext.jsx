import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';

const CountryContext = createContext(undefined);
const PRICING_DB_KEY = 'gulfAcademyPricing';

const initialCountries = [
  { 
    id: 'sa', 
    name: 'السعودية', 
    nationality: 'سعودي',
    currencyCode: 'SAR',
    packages: [
      { id: 1, name: 'ساعة', hours: 1, price: '50', discount: '0' },
      { id: 2, name: 'ساعة ونصف', hours: 1.5, price: '75', discount: '0' },
      { id: 3, name: 'ساعتان', hours: 2, price: '90', discount: '0' },
      { id: 4, name: 'باقة 4 ساعات', hours: 4, price: '180', discount: '20' },
      { id: 5, name: 'باقة 8 ساعات', hours: 8, price: '350', discount: '50' },
      { id: 6, name: 'باقة 12 ساعة', hours: 12, price: '500', discount: '100' },
      { id: 7, name: 'باقة 24 ساعة', hours: 24, price: '950', discount: '250' },
      { id: 8, name: 'باقة 36 ساعة', hours: 36, price: '1400', discount: '400' },
    ]
  },
  { 
    id: 'qa', 
    name: 'قطر', 
    nationality: 'قطري',
    currencyCode: 'QAR',
    packages: [
      { id: 10, name: 'ساعة', hours: 1, price: '55', discount: '0' },
      { id: 11, name: 'ساعة ونصف', hours: 1.5, price: '80', discount: '0' },
      { id: 12, name: 'ساعتان', hours: 2, price: '100', discount: '0' },
      { id: 13, name: 'باقة 4 ساعات', hours: 4, price: '200', discount: '0' },
    ]
  },
  { 
    id: 'kw', 
    name: 'الكويت', 
    nationality: 'كويتي',
    currencyCode: 'KWD',
    packages: [
      { id: 20, name: 'ساعة', hours: 1, price: '5', discount: '0' },
      { id: 21, name: 'باقة 8 ساعات', hours: 8, price: '35', discount: '5' },
    ]
  },
  { 
    id: 'om', 
    name: 'عُمان', 
    nationality: 'عماني',
    currencyCode: 'OMR',
    packages: [
      { id: 30, name: 'ساعة', hours: 1, price: '6', discount: '0' },
      { id: 31, name: 'باقة 8 ساعات', hours: 8, price: '40', discount: '8' },
    ]
  },
  { 
    id: 'ae', 
    name: 'الإمارات', 
    nationality: 'إماراتي',
    currencyCode: 'AED',
    packages: [
      { id: 40, name: 'ساعة', hours: 1, price: '50', discount: '0' },
      { id: 41, name: 'باقة 8 ساعات', hours: 8, price: '380', discount: '20' },
    ]
  },
];


export const CountryProvider = ({ children }) => {
  const [country, setCountry] = useState(null);
  const [countries, setCountries] = useState([]);
  const [hasSelectedCountry, setHasSelectedCountry] = useState(false);
  const [loading, setLoading] = useState(true);
  const { user, updateUserMetadata, loading: authLoading } = useAuth();


  useEffect(() => {
    if (authLoading) return;
    try {
      const storedPricing = JSON.parse(localStorage.getItem(PRICING_DB_KEY) || '{}');
      const loadedCountries = initialCountries.map(c => ({
        ...c,
        packages: storedPricing[c.id] || c.packages,
      }));
      setCountries(loadedCountries);

      const savedCountryId = sessionStorage.getItem('gulfAcademyCountryId');
      if (savedCountryId) {
        const savedCountry = loadedCountries.find(c => c.id === savedCountryId);
        if (savedCountry) {
          setCountry(savedCountry);
          setHasSelectedCountry(true);
        }
      } else if (user?.countryId) {
          const userCountry = loadedCountries.find(c => c.id === user.countryId);
           if (userCountry) {
              setCountry(userCountry);
              setHasSelectedCountry(true);
              sessionStorage.setItem('gulfAcademyCountryId', userCountry.id);
           }
      }
    } catch (error) {
        console.error("Failed to load country data", error);
    } finally {
        setLoading(false);
    }
  }, [user, authLoading]);

  const selectCountry = useCallback((countryData) => {
    setCountry(countryData);
    setHasSelectedCountry(true);
    sessionStorage.setItem('gulfAcademyCountryId', countryData.id);
    if (user && updateUserMetadata) {
      updateUserMetadata({ countryId: countryData.id });
    }
  }, [user, updateUserMetadata]);
  
  const updateCountryPackages = (countryId, newPackages) => {
    const updatedCountries = countries.map(c => 
      c.id === countryId ? { ...c, packages: newPackages } : c
    );
    setCountries(updatedCountries);
    
    const storedPricing = JSON.parse(localStorage.getItem(PRICING_DB_KEY) || '{}');
    storedPricing[countryId] = newPackages;
    localStorage.setItem(PRICING_DB_KEY, JSON.stringify(storedPricing));

    if(country?.id === countryId) {
       selectCountry({...country, packages: newPackages});
    }
  };

  const value = { country, countries, selectCountry, hasSelectedCountry, loading, updateCountryPackages };

  return (
    <CountryContext.Provider value={value}>
      {!loading && children}
    </CountryContext.Provider>
  );
};

export const useCountry = () => {
  const context = useContext(CountryContext);
  if (context === undefined) {
    throw new Error("useCountry must be used within a CountryProvider");
  }
  return context;
};