import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Shield, Settings, Save, AlertTriangle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';

const DeviceControllerSettings = () => {
  const [settings, setSettings] = useState({
    deviceSecurityEnabled: true,
    maxDevicesPerUser: 1,
    deviceVerificationRequired: true,
    failedLoginAttemptsLimit: 5,
    deviceSessionTimeoutHours: 24,
    enableDeviceLogging: true,
    autoRemoveInactiveDevices: false,
    inactiveDeviceThresholdDays: 30
  });
  
  const [loading, setLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    try {
      const savedSettings = localStorage.getItem('deviceControllerSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات:', error);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      
      // حفظ الإعدادات في localStorage
      localStorage.setItem('deviceControllerSettings', JSON.stringify(settings));
      
      // في المستقبل: حفظ في قاعدة البيانات
      // await updateSystemSettings(settings);
      
      setHasChanges(false);
      
      toast({
        title: "تم الحفظ بنجاح",
        description: "تم حفظ إعدادات نظام التحكم في الأجهزة",
      });
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      toast({
        title: "خطأ في الحفظ",
        description: "فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      deviceSecurityEnabled: true,
      maxDevicesPerUser: 1,
      deviceVerificationRequired: true,
      failedLoginAttemptsLimit: 5,
      deviceSessionTimeoutHours: 24,
      enableDeviceLogging: true,
      autoRemoveInactiveDevices: false,
      inactiveDeviceThresholdDays: 30
    });
    setHasChanges(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="h-6 w-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold text-white">إعدادات نظام التحكم في الأجهزة</h2>
            <p className="text-muted-foreground">إدارة إعدادات الأمان والتحكم في الأجهزة</p>
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={resetToDefaults}
            disabled={loading}
          >
            إعادة تعيين
          </Button>
          <Button
            onClick={saveSettings}
            disabled={!hasChanges || loading}
            className="bg-primary text-primary-foreground"
          >
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
          </Button>
        </div>
      </div>

      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4"
        >
          <div className="flex items-center gap-2 text-yellow-400">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">يوجد تغييرات غير محفوظة</span>
          </div>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* إعدادات الأمان الأساسية */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Shield className="h-5 w-5" />
              إعدادات الأمان الأساسية
            </CardTitle>
            <CardDescription>
              التحكم في الميزات الأساسية لنظام أمان الأجهزة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-white">تفعيل نظام أمان الأجهزة</Label>
                <p className="text-sm text-muted-foreground">تفعيل أو إلغاء تفعيل النظام بالكامل</p>
              </div>
              <Switch
                checked={settings.deviceSecurityEnabled}
                onCheckedChange={(checked) => handleSettingChange('deviceSecurityEnabled', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-white">إجبارية التحقق من الجهاز</Label>
                <p className="text-sm text-muted-foreground">منع تسجيل الدخول من أجهزة غير مسجلة</p>
              </div>
              <Switch
                checked={settings.deviceVerificationRequired}
                onCheckedChange={(checked) => handleSettingChange('deviceVerificationRequired', checked)}
                disabled={!settings.deviceSecurityEnabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-white">تفعيل سجلات الأجهزة</Label>
                <p className="text-sm text-muted-foreground">تسجيل محاولات الدخول وأنشطة الأجهزة</p>
              </div>
              <Switch
                checked={settings.enableDeviceLogging}
                onCheckedChange={(checked) => handleSettingChange('enableDeviceLogging', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الحدود والقيود */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Settings className="h-5 w-5" />
              الحدود والقيود
            </CardTitle>
            <CardDescription>
              تحديد الحدود والقيود للأجهزة والمحاولات
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-white">الحد الأقصى للأجهزة لكل مستخدم</Label>
              <Input
                type="number"
                min="1"
                max="10"
                value={settings.maxDevicesPerUser}
                onChange={(e) => handleSettingChange('maxDevicesPerUser', parseInt(e.target.value))}
                className="bg-secondary/50 border-border text-white mt-2"
              />
              <p className="text-sm text-muted-foreground mt-1">عدد الأجهزة المسموح تسجيلها لكل مستخدم</p>
            </div>

            <div>
              <Label className="text-white">حد محاولات تسجيل الدخول الفاشلة</Label>
              <Input
                type="number"
                min="3"
                max="20"
                value={settings.failedLoginAttemptsLimit}
                onChange={(e) => handleSettingChange('failedLoginAttemptsLimit', parseInt(e.target.value))}
                className="bg-secondary/50 border-border text-white mt-2"
              />
              <p className="text-sm text-muted-foreground mt-1">عدد المحاولات المسموحة قبل حظر الحساب مؤقتاً</p>
            </div>

            <div>
              <Label className="text-white">مدة انتهاء جلسة الجهاز (بالساعات)</Label>
              <Input
                type="number"
                min="1"
                max="168"
                value={settings.deviceSessionTimeoutHours}
                onChange={(e) => handleSettingChange('deviceSessionTimeoutHours', parseInt(e.target.value))}
                className="bg-secondary/50 border-border text-white mt-2"
              />
              <p className="text-sm text-muted-foreground mt-1">المدة قبل انتهاء صلاحية جلسة الجهاز</p>
            </div>
          </CardContent>
        </Card>

        {/* إعدادات الصيانة التلقائية */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              الصيانة التلقائية
            </CardTitle>
            <CardDescription>
              إعدادات التنظيف والصيانة التلقائية للنظام
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-white">إزالة الأجهزة غير النشطة تلقائياً</Label>
                <p className="text-sm text-muted-foreground">حذف الأجهزة التي لم تُستخدم لفترة طويلة</p>
              </div>
              <Switch
                checked={settings.autoRemoveInactiveDevices}
                onCheckedChange={(checked) => handleSettingChange('autoRemoveInactiveDevices', checked)}
              />
            </div>

            {settings.autoRemoveInactiveDevices && (
              <div>
                <Label className="text-white">عتبة الأجهزة غير النشطة (بالأيام)</Label>
                <Input
                  type="number"
                  min="7"
                  max="365"
                  value={settings.inactiveDeviceThresholdDays}
                  onChange={(e) => handleSettingChange('inactiveDeviceThresholdDays', parseInt(e.target.value))}
                  className="bg-secondary/50 border-border text-white mt-2"
                />
                <p className="text-sm text-muted-foreground mt-1">عدد الأيام قبل اعتبار الجهاز غير نشط</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* معلومات النظام */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white">معلومات النظام</CardTitle>
            <CardDescription>
              إحصائيات ومعلومات حول نظام التحكم في الأجهزة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">حالة النظام:</span>
              <span className={`font-medium ${settings.deviceSecurityEnabled ? 'text-green-400' : 'text-red-400'}`}>
                {settings.deviceSecurityEnabled ? 'نشط' : 'غير نشط'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">إصدار النظام:</span>
              <span className="text-white">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">آخر تحديث:</span>
              <span className="text-white">{new Date().toLocaleDateString('ar-SA')}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DeviceControllerSettings;
