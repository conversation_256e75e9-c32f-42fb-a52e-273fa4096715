import React, { useState, useEffect, useCallback } from 'react';
    import { useToast } from '@/components/ui/use-toast';
    import { Filter } from 'lucide-react';
    import { useAuth } from '@/contexts/AuthContext';
    import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
    import { supabase } from '@/lib/customSupabaseClient';
    import MediaUploadForm from '@/components/MediaUploadForm';
    import MediaList from '@/components/MediaList';
    import { Label } from '@/components/ui/label';

    const MediaLibraryContent = () => {
      const { toast } = useToast();
      const { students, teachers } = useAuth();
      const [media, setMedia] = useState([]);
      const [assignments, setAssignments] = useState([]);
      const [loading, setLoading] = useState(true);
      const [filters, setFilters] = useState({ teacher: 'all', subject: 'all' });

      const fetchMedia = useCallback(async () => {
        setLoading(true);
        const { data: mediaData, error: mediaError } = await supabase.from('study_materials').select('*').order('created_at', { ascending: false });
        if (mediaError) {
          toast({ title: 'خطأ', description: 'فشل في جلب المواد.', variant: 'destructive' });
        } else {
          setMedia(mediaData);
        }
        
        const { data: assignmentData, error: assignmentError } = await supabase.from('material_assignments').select('*');
        if (assignmentError) {
          toast({ title: 'خطأ', description: 'فشل في جلب تخصيصات المواد.', variant: 'destructive' });
        } else {
          setAssignments(assignmentData);
        }
        setLoading(false);
      }, [toast]);

      useEffect(() => {
        fetchMedia();
      }, [fetchMedia]);

      const handleMaterialUploaded = () => {
        fetchMedia(); 
      };
      
      const handleDelete = async (mediaId) => {
        const materialToDelete = media.find(m => m.id === mediaId);
        
        if (materialToDelete.drive_file_id) { // This field now holds the file path in Supabase Storage
            const { error: storageError } = await supabase.storage.from('study_materials').remove([materialToDelete.drive_file_id]);
            if (storageError) {
                console.warn("Could not delete file from Supabase Storage, maybe it's already deleted.", storageError);
            }
        }
        
        const { error: assignmentError } = await supabase.from('material_assignments').delete().eq('material_id', mediaId);
        if (assignmentError) {
            toast({ title: 'خطأ في حذف التخصيصات', variant: 'destructive' });
        }
        
        const { error } = await supabase.from('study_materials').delete().eq('id', mediaId);
        if (error) {
          toast({ title: '❌ فشل حذف المادة من قاعدة البيانات', variant: 'destructive' });
        } else {
          toast({ title: '🗑️ تم الحذف بنجاح' });
          fetchMedia();
        }
      };
      
      const filteredMedia = media.filter(item => {
        const teacherMatch = filters.teacher === 'all' || item.uploaded_by_email === filters.teacher;
        const subjectMatch = filters.subject === 'all' || item.subject === filters.subject;
        return teacherMatch && subjectMatch;
      });

      const allSubjects = [...new Set(media.map(m => m.subject).filter(Boolean))];

      return (
        <div className="space-y-8">
          <div className="glass-effect rounded-xl p-6">
            <div className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
                <h3 className="text-xl font-bold text-white">إدارة مكتبة الوسائط</h3>
                <MediaUploadForm onUploadSuccess={handleMaterialUploaded} />
            </div>
            
            <div className="bg-secondary/50 rounded-lg p-4 mb-6 flex flex-col md:flex-row gap-4 items-center">
                <Filter className="h-5 w-5 text-primary hidden md:block" />
                <div className='flex-1 w-full'>
                    <Label className="text-white/80">فلترة حسب المعلم</Label>
                    <Select value={filters.teacher} onValueChange={(v) => setFilters(f => ({...f, teacher: v}))}>
                        <SelectTrigger><SelectValue/></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">كل المعلمين</SelectItem>
                            {teachers.map(t => <SelectItem key={t.email} value={t.email}>{t.name}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </div>
                <div className='flex-1 w-full'>
                    <Label className="text-white/80">فلترة حسب المادة</Label>
                    <Select value={filters.subject} onValueChange={(v) => setFilters(f => ({...f, subject: v}))}>
                        <SelectTrigger><SelectValue/></SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">كل المواد</SelectItem>
                            {allSubjects.map(s => <SelectItem key={s} value={s}>{s}</SelectItem>)}
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <MediaList
              media={filteredMedia}
              assignments={assignments}
              onDelete={handleDelete}
              students={students}
              teachers={teachers}
              loading={loading}
            />
          </div>
        </div>
      );
    };

    export default MediaLibraryContent;