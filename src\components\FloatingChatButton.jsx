import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { MessageSquare, X } from 'lucide-react';
import LiveChatWidget from '@/components/LiveChatWidget';

const FloatingChatButton = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);

  return (
    <>
      <div className="fixed bottom-6 right-6 z-40">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <motion.button
                onClick={() => setIsChatOpen(!isChatOpen)}
                className="w-16 h-16 rounded-full bg-gradient-to-br from-primary to-primary/70 text-white flex items-center justify-center shadow-xl focus:outline-none"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                animate={{ rotate: isChatOpen ? 90 : 0 }}
                aria-label={isChatOpen ? "إغلاق الدردشة" : "فتح الدردشة"}
              >
                {isChatOpen ? <X className="w-8 h-8" /> : <MessageSquare className="w-8 h-8" />}
              </motion.button>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-secondary text-white border-border">
              <p>{isChatOpen ? "إغلاق الدردشة" : "الدردشة الحية"}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <LiveChatWidget isOpen={isChatOpen} setIsOpen={setIsChatOpen} />
    </>
  );
};

export default FloatingChatButton;