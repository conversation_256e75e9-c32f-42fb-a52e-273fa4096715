import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowRight, Mail, Lock, Eye, EyeOff, KeyRound } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

const LoginPage = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { login } = useAuth();

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const { email, password } = formData;

    if (!email || !password) {
      toast({
        title: "خطأ في البيانات",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const loggedInUser = login(email, password);

    if (loggedInUser) {
      toast({
        title: "تم تسجيل الدخول بنجاح!",
        description: `مرحباً بك مجدداً في Gulf Academy`
      });
      navigate(`/${loggedInUser.userType}`);
    } else {
      toast({
        title: "فشل تسجيل الدخول",
        description: "البريد الإلكتروني أو كلمة المرور غير صحيح. يرجى المحاولة مرة أخرى.",
        variant: "destructive"
      });
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <>
      <Helmet>
        <title>تسجيل الدخول - Gulf Academy</title>
        <meta name="description" content="سجل دخولك إلى منصة Gulf Academy للوصول إلى حسابك والبدء في رحلتك التعليمية" />
      </Helmet>

      <div className="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="glass-effect rounded-2xl p-8"
          >
            <div className="text-center mb-8">
              <Link to="/" className="inline-flex items-center gap-3 mb-6 justify-center">
                <img  alt="Gulf Academy Logo" className="h-12 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
              </Link>
              <h1 className="text-3xl font-bold text-white mb-2">تسجيل الدخول</h1>
              <p className="text-muted-foreground">ادخل إلى حسابك للمتابعة</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">البريد الإلكتروني</Label>
                <div className="relative">
                  <Mail className="absolute right-3 top-3 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="bg-secondary/50 border-border text-white placeholder:text-muted-foreground pr-10"
                    placeholder="أدخل بريدك الإلكتروني"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">كلمة المرور</Label>
                <div className="relative">
                  <Lock className="absolute right-3 top-3 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="bg-secondary/50 border-border text-white placeholder:text-muted-foreground pr-10 pl-10"
                    placeholder="أدخل كلمة المرور"
                    required
                  />
                  <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-white">
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
              </div>

              <Button 
                type="submit" 
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90 text-lg py-3"
              >
                تسجيل الدخول
                <ArrowRight className="mr-2 h-5 w-5" />
              </Button>
            </form>

            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-border" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-secondary px-2 text-muted-foreground">أو</span>
              </div>
            </div>

            <Button 
                variant="outline" 
                className="w-full text-lg py-3"
                onClick={() => navigate('/login/access-code')}
            >
                <KeyRound className="ml-2 h-5 w-5" />
                الدخول باستخدام كود
            </Button>


            <div className="mt-6 text-center">
              <p className="text-muted-foreground">
                ليس لديك حساب؟{' '}
                <Link to="/register" className="text-primary hover:text-primary/80 font-medium">
                  سجل الآن
                </Link>
              </p>
            </div>

            <div className="mt-4 text-center">
              <Link to="/" className="text-muted-foreground hover:text-white transition-colors">
                العودة إلى الصفحة الرئيسية
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </>
  );
};

export default LoginPage;