import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { useBooking } from '@/contexts/BookingContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PlusCircle, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { arSA } from 'date-fns/locale';

const ChatContent = ({ openChat }) => {
    const { user, users, teachers } = useAuth();
    const { bookings } = useBooking();
    const { internalMessages, chatGroups, createChatGroup, getInternalChatId, announcements } = useChat();
    const [searchTerm, setSearchTerm] = useState('');
    const [isGroupModalOpen, setIsGroupModalOpen] = useState(false);
    const [groupName, setGroupName] = useState('');
    const [selectedMembers, setSelectedMembers] = useState([]);
    const { toast } = useToast();

    const adminUser = users.find(u => u.userType === 'admin');

    const inboxContact = {
        id: 'inbox',
        name: 'الأخبار والعروض',
        type: 'inbox',
        userType: 'system'
    };

    const myStudentEmails = [...new Set(bookings
        .filter(b => b.teacherEmail === user.email && b.status === 'approved')
        .map(b => b.studentEmail))
    ];
    
    const myStudents = users.filter(u => myStudentEmails.includes(u.email));

    const contacts = [
        adminUser,
        ...myStudents,
        ...chatGroups.filter(g => g.members.includes(user.email)),
    ].filter(Boolean);

    const getInitial = (name) => name ? name.charAt(0).toUpperCase() : '?';

    const getLastMessage = (contact) => {
        if (contact.id === 'inbox') {
            const lastAnnouncement = announcements?.[0];
            return lastAnnouncement ? { text: `**${lastAnnouncement.subject}**`, timestamp: lastAnnouncement.timestamp, sender: { name: 'الإدارة' } } : { text: 'لا توجد أخبار أو عروض جديدة', timestamp: '' };
        }
        
        const chatId = contact.type === 'group' ? contact.id : getInternalChatId(user.email, contact.id || contact.email);
        const messages = internalMessages[chatId];
        if (!messages || messages.length === 0) return { text: 'لا توجد رسائل', timestamp: '' };
        return messages[messages.length - 1];
    };

    const filteredContacts = contacts.filter(contact =>
        contact.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleSelectMember = (email) => {
        setSelectedMembers(prev =>
            prev.includes(email) ? prev.filter(m => m !== email) : [...prev, email]
        );
    };

    const handleCreateGroup = () => {
        if (!groupName.trim()) {
            toast({ title: "خطأ", description: "يرجى إدخال اسم للمجموعة", variant: "destructive" });
            return;
        }
        if (selectedMembers.length < 1) {
            toast({ title: "خطأ", description: "يرجى اختيار عضو واحد على الأقل", variant: "destructive" });
            return;
        }

        createChatGroup(groupName, [...selectedMembers, user.email]);
        setGroupName('');
        setSelectedMembers([]);
        setIsGroupModalOpen(false);
    };

    return (
        <div dir="rtl">
            <Card className="glass-effect">
                <CardHeader>
                    <CardTitle className="text-primary text-2xl">المحادثات</CardTitle>
                    <div className="flex gap-2 mt-4">
                        <div className="relative flex-grow">
                            <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                            <Input
                                placeholder="بحث عن محادثة..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full bg-secondary/50 border-border pr-10"
                            />
                        </div>
                        <Button onClick={() => setIsGroupModalOpen(true)} className="bg-primary hover:bg-primary/90">
                            <PlusCircle className="ml-2 h-5 w-5" />
                            مجموعة جديدة
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <ul className="space-y-2">
                        {[inboxContact, ...filteredContacts].map(contact => {
                             const lastMessage = getLastMessage(contact);
                             const isInboxUnread = contact.id === 'inbox' && announcements.length > 0;
                             return (
                                <li key={contact.id} onClick={() => openChat(contact)} className="p-3 flex items-center gap-4 rounded-lg cursor-pointer hover:bg-white/10 transition-colors duration-200">
                                    <Avatar>
                                        <AvatarImage src={contact.profilePicture} />
                                        <AvatarFallback className="bg-primary text-primary-foreground">{getInitial(contact.name)}</AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1">
                                        <div className="flex justify-between items-center">
                                            <h3 className="font-semibold">{contact.name}</h3>
                                             {lastMessage.timestamp && (
                                                <p className="text-xs text-muted-foreground">
                                                    {formatDistanceToNow(new Date(lastMessage.timestamp), { addSuffix: true, locale: arSA })}
                                                </p>
                                            )}
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <p className="text-sm text-muted-foreground truncate max-w-xs">
                                                {lastMessage.sender?.name && lastMessage.sender.name !== user.name ? `${lastMessage.sender.name}: ` : ''}
                                                {lastMessage.text.startsWith('**') ? <strong>{lastMessage.text.replace(/\*\*/g, '')}</strong> : lastMessage.text}
                                            </p>
                                            {contact.type === 'group' && <Badge variant="secondary">مجموعة</Badge>}
                                            {contact.userType && contact.userType !== 'teacher' && <Badge variant="secondary">{contact.userType === 'admin' ? 'الإدارة' : 'طالب'}</Badge>}
                                            {isInboxUnread && <span className="w-2.5 h-2.5 bg-primary rounded-full animate-pulse"></span>}
                                        </div>
                                    </div>
                                </li>
                            )
                        })}
                    </ul>
                </CardContent>
            </Card>

            <Dialog open={isGroupModalOpen} onOpenChange={setIsGroupModalOpen}>
                <DialogContent className="bg-secondary border-border text-white">
                    <DialogHeader>
                        <DialogTitle>إنشاء مجموعة جديدة</DialogTitle>
                        <DialogDescription>
                            اختر الأعضاء لإنشاء مجموعة دردشة جديدة.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4" dir="rtl">
                        <div>
                            <Label htmlFor="group-name">اسم المجموعة</Label>
                            <Input id="group-name" value={groupName} onChange={(e) => setGroupName(e.target.value)} placeholder="اسم المجموعة..." className="bg-white/10 mt-2" />
                        </div>
                        <div>
                            <Label>أعضاء المجموعة</Label>
                            <div className="space-y-2 mt-2 max-h-60 overflow-y-auto pr-2">
                                {[...myStudents, ...teachers.filter(t => t.email !== user.email)].map(member => (
                                    <div key={member.email} className="flex items-center space-x-2 space-x-reverse">
                                        <Checkbox
                                            id={`member-${member.email}`}
                                            checked={selectedMembers.includes(member.email)}
                                            onCheckedChange={() => handleSelectMember(member.email)}
                                        />
                                        <Label htmlFor={`member-${member.email}`} className="flex items-center gap-2 cursor-pointer">
                                            <Avatar className="h-8 w-8">
                                                <AvatarImage src={member.profilePicture} />
                                                <AvatarFallback>{getInitial(member.name)}</AvatarFallback>
                                            </Avatar>
                                            {member.name}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsGroupModalOpen(false)}>إلغاء</Button>
                        <Button className="bg-primary hover:bg-primary/90" onClick={handleCreateGroup}>إنشاء</Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ChatContent;