import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Paperclip, Mic, Square } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useChat } from '@/contexts/ChatContext';
import { useToast } from '@/components/ui/use-toast';
import ChatMessage from '@/components/ChatMessage';
import { supabase } from '@/lib/customSupabaseClient';

const InternalChat = ({ recipient }) => {
  const { user, users } = useAuth();
  const { internalMessages, sendInternalMessage, editInternalMessage, deleteInternalMessage, getInternalChatId } = useChat();
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  const isGroupChat = recipient.type === 'group';
  const chatId = isGroupChat ? recipient.id : getInternalChatId(user.email, recipient.id);
  const chatMessages = internalMessages[chatId] || [];
  
  const isReadOnly = recipient.id === 'inbox';

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  const handleSendMessage = (fileInfo = null) => {
    if (isReadOnly) return;
    const text = fileInfo ? `مرفق: ${fileInfo.name}` : message;
    if (text.trim() === '') return;
    const sender = { id: user.email, name: user.name, type: user.userType };
    sendInternalMessage(chatId, text, sender, fileInfo ? fileInfo.link : null);
    setMessage('');
  };

  const uploadToDrive = async (file) => {
    setIsUploading(true);
    toast({ title: 'جاري رفع الملف...', description: file.name });
    
    try {
        const { data, error } = await supabase.functions.invoke('upload-to-drive', {
            body: file,
            headers: { 'x-file-name': encodeURIComponent(file.name) },
        });

        if (error) throw error;
        if (!data.success) throw new Error(data.error || 'فشل الرفع إلى Google Drive');

        toast({ title: '🎉 تم إرفاق الملف بنجاح!' });
        return { name: file.name, link: data.webViewLink };
    } catch (error) {
        console.error('Upload error:', error);
        toast({ title: '❌ فشل الرفع', description: `حدث خطأ: ${error.message}`, variant: 'destructive' });
        return null;
    } finally {
        setIsUploading(false);
    }
  };

  const handleAttachment = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = async (event) => {
    if (event.target.files.length === 0) return;
    const file = event.target.files[0];
    const uploadedFile = await uploadToDrive(file);
    if (uploadedFile) handleSendMessage(uploadedFile);
    fileInputRef.current.value = "";
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      mediaRecorderRef.current.ondataavailable = event => {
        audioChunksRef.current.push(event.data);
      };
      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const audioFile = new File([audioBlob], `تسجيل-صوتي-${Date.now()}.webm`, { type: 'audio/webm' });
        const uploadedFile = await uploadToDrive(audioFile);
        if (uploadedFile) handleSendMessage(uploadedFile);
        stream.getTracks().forEach(track => track.stop());
      };
      mediaRecorderRef.current.start();
      setIsRecording(true);
      toast({ title: "🎙️ بدأ التسجيل..." });
    } catch (err) {
      toast({ title: "خطأ في الوصول للميكروفون", description: "يرجى التأكد من منح الإذن لاستخدام الميكروفون.", variant: "destructive" });
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      toast({ title: "🛑 توقف التسجيل", description: "جاري معالجة المقطع الصوتي..." });
    }
  };

  const handleMicClick = () => {
    if (isRecording) stopRecording();
    else startRecording();
  };

  const handleEdit = (messageId, newText) => {
    editInternalMessage(chatId, messageId, newText);
    toast({ title: "✅ تم تعديل الرسالة" });
  };

  const handleDelete = (messageId) => {
    deleteInternalMessage(chatId, messageId);
    toast({ title: "🗑️ تم حذف الرسالة" });
  };
  
  const getParticipantInfo = (participantId) => {
    return users.find(u => u.email === participantId);
  }

  return (
    <div className="flex flex-col h-full" style={{ direction: 'rtl' }}>
      <main className="flex-1 p-4 overflow-y-auto space-y-4">
        {chatMessages.map((msg) => (
          <ChatMessage
            key={msg.id}
            msg={msg}
            isMyMessage={msg.sender.id === user.email}
            onEdit={handleEdit}
            onDelete={handleDelete}
            getParticipantInfo={getParticipantInfo}
            isReadOnly={isReadOnly}
          />
        ))}
        <div ref={messagesEndRef} />
      </main>
      {!isReadOnly && (
        <footer className="p-4 border-t border-white/10">
          <div className="flex items-center gap-2">
            <Input
              type="text"
              placeholder="اكتب رسالتك هنا..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              className="bg-white/10 border-border text-white"
              disabled={isRecording}
            />
            <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={handleAttachment} disabled={isUploading || isRecording}>
              <Paperclip className="h-5 w-5" />
            </Button>
            <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
            <Button size="icon" variant="ghost" className={`text-white/70 hover:text-white ${isRecording ? 'text-red-500 animate-pulse' : ''}`} onClick={handleMicClick} disabled={isUploading}>
              {isRecording ? <Square className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
            </Button>
            <Button size="icon" className="bg-primary hover:bg-primary/90" onClick={() => handleSendMessage()} disabled={isRecording}>
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </footer>
      )}
    </div>
  );
};

export default InternalChat;