import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Helmet } from 'react-helmet';
import { useAuth } from '@/contexts/AuthContext';
import TeacherSidebar from '@/pages/teacher/TeacherSidebar';
import DashboardContent from '@/pages/teacher/DashboardContent';
import ScheduleContent from '@/pages/teacher/ScheduleContent';
import MediaLibraryContent from '@/pages/teacher/MediaLibraryContent.jsx';
import ExamsContent from '@/pages/teacher/ExamsContent.jsx';
import ProfileContent from '@/pages/teacher/ProfileContent';
import StudentsContent from '@/pages/teacher/StudentsContent';
import InternalChat from '@/components/InternalChat';
import UpcomingSessionsContent from '@/pages/teacher/UpcomingSessionsContent';
import ChatContent from '@/pages/teacher/ChatContent';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

const TeacherDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [chatRecipient, setChatRecipient] = useState(null);

  if (!user) {
    return null;
  }

  const openChat = (recipient) => {
    setChatRecipient(recipient);
    setIsChatOpen(true);
  };
  
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardContent setActiveTab={setActiveTab} />;
      case 'schedule':
        return <ScheduleContent />;
      case 'upcoming-sessions':
        return <UpcomingSessionsContent />;
      case 'media-library':
        return <MediaLibraryContent />;
      case 'exams':
        return <ExamsContent />;
      case 'students':
        return <StudentsContent openChat={openChat} />;
      case 'chat':
        return <ChatContent openChat={openChat} />;
      case 'profile':
        return <ProfileContent openChat={openChat} />;
      default:
        return <DashboardContent setActiveTab={setActiveTab} />;
    }
  };

  return (
    <>
      <Helmet>
        <title>لوحة تحكم المعلم - Gulf Academy</title>
        <meta name="description" content="لوحة تحكم المعلم في منصة Gulf Academy - إدارة حصصك ومواد التدريس" />
      </Helmet>
      <div className="min-h-screen gradient-bg">
        <div className="flex">
          <TeacherSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
          <main className="flex-1 p-8 overflow-y-auto h-screen">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">مرحباً، {user.name}</h1>
              <p className="text-white/70">إدارة حصصك وموادك التعليمية بكل سهولة</p>
            </div>
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {renderContent()}
            </motion.div>
          </main>
        </div>
      </div>
       <Dialog open={isChatOpen} onOpenChange={setIsChatOpen}>
        <DialogContent className="bg-secondary border-border text-white max-w-2xl h-[70vh] flex flex-col p-0">
          <DialogHeader className="p-4 border-b border-white/10">
            <DialogTitle>محادثة مع {chatRecipient?.name}</DialogTitle>
          </DialogHeader>
          {chatRecipient && <InternalChat recipient={chatRecipient} />}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TeacherDashboard;