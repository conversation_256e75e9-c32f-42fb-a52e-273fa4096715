import React, { useState, useEffect } from 'react';
    import { motion } from 'framer-motion';
    import { Helmet } from 'react-helmet';
    import { useLocation, useNavigate } from 'react-router-dom';
    import { useAuth } from '@/contexts/AuthContext';
    import TeacherSidebar from '@/pages/teacher/TeacherSidebar';
    import DashboardContent from '@/pages/teacher/DashboardContent';
    import ScheduleContent from '@/pages/teacher/ScheduleContent';
    import StudentsContent from '@/pages/teacher/StudentsContent';
    import MediaLibraryContent from '@/pages/teacher/MediaLibraryContent';
    import ChatContent from '@/pages/teacher/ChatContent';
    import ReportsContent from '@/pages/teacher/ReportsContent';
    import ProfileContent from '@/pages/teacher/ProfileContent';
    import SalesReportsContent from '@/pages/teacher/SalesReportsContent';
    import UpcomingSessionsContent from '@/pages/teacher/UpcomingSessionsContent';
    import FinishedLessonsContent from '@/pages/teacher/FinishedLessonsContent';
    import NotificationsDropdown from '@/components/NotificationsDropdown';

    const TeacherDashboard = () => {
        const { user } = useAuth();
        const location = useLocation();
        const navigate = useNavigate();
        const [activeTab, setActiveTab] = useState('dashboard');

        useEffect(() => {
            const params = new URLSearchParams(location.search);
            const tab = params.get('tab');
            if (tab) {
                setActiveTab(tab);
            }
        }, [location.search]);

        const handleSetActiveTab = (tab) => {
            setActiveTab(tab);
            navigate(`/teacher?tab=${tab}`);
        };

        const renderContent = () => {
            switch (activeTab) {
                case 'dashboard': return <DashboardContent setActiveTab={handleSetActiveTab} />;
                case 'schedule': return <ScheduleContent />;
                case 'upcoming-sessions': return <UpcomingSessionsContent />;
                case 'finished-lessons': return <FinishedLessonsContent />;
                case 'students': return <StudentsContent />;
                case 'media-library': return <MediaLibraryContent />;
                case 'chat': return <ChatContent />;
                case 'reports': return <ReportsContent />;
                case 'sales-reports': return <SalesReportsContent />;
                case 'profile': return <ProfileContent />;
                default: return <DashboardContent setActiveTab={handleSetActiveTab} />;
            }
        };

        if (!user) {
            return null;
        }

        return (
            <>
                <Helmet>
                    <title>لوحة تحكم المعلم - Gulf Academy</title>
                    <meta name="description" content="لوحة تحكم المعلم في منصة Gulf Academy" />
                </Helmet>
                <div className="min-h-screen gradient-bg">
                    <div className="flex">
                        <TeacherSidebar activeTab={activeTab} setActiveTab={handleSetActiveTab} />
                        <main className="flex-1 p-8 overflow-y-auto h-screen">
                            <div className="flex justify-between items-center mb-8">
                                <div>
                                    <h1 className="text-3xl font-bold text-white mb-2">لوحة تحكم المعلم</h1>
                                    <p className="text-white/70">مرحباً بعودتك، {user.name}!</p>
                                </div>
                                <NotificationsDropdown />
                            </div>
                            <motion.div
                                key={activeTab}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5 }}
                            >
                                {renderContent()}
                            </motion.div>
                        </main>
                    </div>
                </div>
            </>
        );
    };

    export default TeacherDashboard;