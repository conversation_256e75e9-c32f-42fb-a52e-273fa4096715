import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '@/lib/customSupabaseClient.js';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const FooterSocialLinks = () => {
  const [socials, setSocials] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSocialLinks = async () => {
      setLoading(true);
      const { data, error } = await supabase
        .from('social_links')
        .select('*')
        .eq('is_visible', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching social links for footer:', error);
        toast.error('فشل في تحميل روابط التواصل.');
      } else {
        setSocials(data);
      }
      setLoading(false);
    };

    fetchSocialLinks();
  }, []);

  if (loading) {
    return <div className="flex justify-center md:justify-start"><Loader2 className="h-6 w-6 animate-spin text-primary" /></div>;
  }

  return (
    <div className="flex justify-center md:justify-start items-center gap-4">
      {socials.map(social => (
        <a
          key={social.id}
          href={social.url && social.url !== '#' ? social.url : undefined}
          target="_blank"
          rel="noopener noreferrer"
          onClick={(e) => {
            if (!social.url || social.url === '#') {
              e.preventDefault();
              toast.warning(`🚧 لم يتم ربط حساب ${social.name} بعد.`);
            }
          }}
          className="text-muted-foreground hover:text-primary transition-colors"
          aria-label={social.name}
        >
          <img src={social.icon_url} alt={social.name} className="w-9 h-9 object-cover rounded-full transition-transform duration-300 hover:scale-110" />
        </a>
      ))}
    </div>
  );
};


const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-secondary/20 border-t border-border/20 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-center md:text-right">
          <div className="flex flex-col items-center md:items-start">
            <Link to="/" className="mb-4">
              <img alt="Gulf Academy Logo" className="h-20 w-auto" src="https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png" />
            </Link>
            <p className="text-muted-foreground text-sm max-w-xs">
              منصة تعليمية تفاعلية مصممة لطلاب الخليج، تقدم دروساً خصوصية مع نخبة من المعلمين المعتمدين.
            </p>
          </div>

          <div>
            <p className="font-bold text-lg mb-4">روابط سريعة</p>
            <ul className="space-y-3">
              <li><Link to="/" className="text-muted-foreground hover:text-primary transition-colors">الرئيسية</Link></li>
              <li><Link to="/about" className="text-muted-foreground hover:text-primary transition-colors">من نحن</Link></li>
              <li><Link to="/faq" className="text-muted-foreground hover:text-primary transition-colors">الأسئلة الشائعة</Link></li>
              <li><Link to="/privacy" className="text-muted-foreground hover:text-primary transition-colors">سياسة الخصوصية</Link></li>
            </ul>
          </div>
          
          <div>
            <p className="font-bold text-lg mb-4">تابعنا</p>
            <p className="text-muted-foreground mb-4 text-sm">
              تواصل معنا وكن على اطلاع دائم بآخر أخبارنا عبر منصاتنا الاجتماعية.
            </p>
            <FooterSocialLinks />
          </div>

        </div>

        <div className="mt-12 pt-8 border-t border-border/20 text-center text-muted-foreground text-sm">
          <p>&copy; {currentYear} أكاديمية الخليج. جميع الحقوق محفوظة.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;