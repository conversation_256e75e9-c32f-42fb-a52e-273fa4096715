import {
  AccountEntity,
  ApiId,
  AuthError,
  AuthErrorCodes_exports,
  AuthErrorMessage,
  AuthenticationHeaderParser,
  AuthenticationScheme,
  AzureCloudInstance,
  BrowserAuthError,
  BrowserAuthErrorCodes_exports,
  BrowserAuthErrorMessage,
  BrowserCacheLocation,
  BrowserConfigurationAuthError,
  BrowserConfigurationAuthErrorCodes_exports,
  BrowserConfigurationAuthErrorMessage,
  BrowserPerformanceClient,
  BrowserStorage,
  BrowserUtils_exports,
  CacheLookupPolicy,
  ClientAuthError,
  ClientAuthErrorCodes_exports,
  ClientAuthErrorMessage,
  ClientConfigurationError,
  ClientConfigurationErrorCodes_exports,
  ClientConfigurationErrorMessage,
  DEFAULT_IFRAME_TIMEOUT_MS,
  EventHandler,
  EventMessageUtils,
  EventType,
  InteractionRequiredAuthError,
  InteractionRequiredAuthErrorCodes_exports,
  InteractionRequiredAuthErrorMessage,
  InteractionStatus,
  InteractionType,
  JsonWebTokenTypes,
  LocalStorage,
  LogLevel,
  Logger,
  MemoryStorage,
  NavigationClient,
  OIDC_DEFAULT_SCOPES,
  PerformanceEvents,
  PromptValue,
  ProtocolMode,
  PublicClientApplication,
  PublicClientNext,
  ServerError,
  ServerResponseType,
  SessionStorage,
  SignedHttpRequest,
  StringUtils,
  StubPerformanceClient,
  UrlString,
  WrapperSKU,
  createNestablePublicClientApplication,
  createStandardPublicClientApplication,
  stubbedPublicClientApplication,
  version
} from "./chunk-RGED4R45.js";
import {
  BrowserPerformanceMeasurement
} from "./chunk-IXOCUMLG.js";
import "./chunk-2LSFTFF7.js";
export {
  AccountEntity,
  ApiId,
  AuthError,
  AuthErrorCodes_exports as AuthErrorCodes,
  AuthErrorMessage,
  AuthenticationHeaderParser,
  AuthenticationScheme,
  AzureCloudInstance,
  BrowserAuthError,
  BrowserAuthErrorCodes_exports as BrowserAuthErrorCodes,
  BrowserAuthErrorMessage,
  BrowserCacheLocation,
  BrowserConfigurationAuthError,
  BrowserConfigurationAuthErrorCodes_exports as BrowserConfigurationAuthErrorCodes,
  BrowserConfigurationAuthErrorMessage,
  BrowserPerformanceClient,
  BrowserPerformanceMeasurement,
  BrowserStorage,
  BrowserUtils_exports as BrowserUtils,
  CacheLookupPolicy,
  ClientAuthError,
  ClientAuthErrorCodes_exports as ClientAuthErrorCodes,
  ClientAuthErrorMessage,
  ClientConfigurationError,
  ClientConfigurationErrorCodes_exports as ClientConfigurationErrorCodes,
  ClientConfigurationErrorMessage,
  DEFAULT_IFRAME_TIMEOUT_MS,
  EventHandler,
  EventMessageUtils,
  EventType,
  InteractionRequiredAuthError,
  InteractionRequiredAuthErrorCodes_exports as InteractionRequiredAuthErrorCodes,
  InteractionRequiredAuthErrorMessage,
  InteractionStatus,
  InteractionType,
  JsonWebTokenTypes,
  LocalStorage,
  LogLevel,
  Logger,
  MemoryStorage,
  NavigationClient,
  OIDC_DEFAULT_SCOPES,
  PerformanceEvents,
  PromptValue,
  ProtocolMode,
  PublicClientApplication,
  PublicClientNext,
  ServerError,
  ServerResponseType,
  SessionStorage,
  SignedHttpRequest,
  StringUtils,
  StubPerformanceClient,
  UrlString,
  WrapperSKU,
  createNestablePublicClientApplication,
  createStandardPublicClientApplication,
  stubbedPublicClientApplication,
  version
};
//# sourceMappingURL=@azure_msal-browser.js.map
