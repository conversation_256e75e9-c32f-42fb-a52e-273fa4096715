import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import {
  getDeviceFingerprint,
  hashString,
  getDeviceInfo,
  saveDeviceInfo,
  verifyDevice,
  updateDeviceLastSeen
} from '@/lib/deviceFingerprint';
import { supabase } from '@/lib/customSupabaseClient';
import { usersAPI, devicesAPI, loginAttemptsAPI, isAPIAvailable } from '@/lib/externalAPI';

const AuthContext = createContext(null);

const MOCK_USERS_DB = 'gulfAcademyUsers';
const CURRENT_USER_SESSION = 'currentUser';

const initializeUsers = () => {
    let usersJson = localStorage.getItem(MOCK_USERS_DB);
    let users = {};
    
    try {
        users = usersJson ? JSON.parse(usersJson) : {};
    } catch (e) {
        console.error("Could not parse users from localStorage", e);
        users = {};
    }

    const defaultUsers = {
        '<EMAIL>': { 
            name: 'Admin', 
            email: '<EMAIL>', 
            password: 'Gulf2025', 
            userType: 'admin',
            registrationDate: new Date().toISOString(),
            status: 'نشط',
            id: '<EMAIL>'
        },
        '<EMAIL>': {
            name: 'أ. محمد أحمد',
            email: '<EMAIL>',
            password: 'password',
            userType: 'teacher',
            registrationDate: new Date().toISOString(),
            specialization: 'الرياضيات',
            status: 'نشط',
            id: '<EMAIL>',
            assignedStudents: ['<EMAIL>', '<EMAIL>']
        },
        '<EMAIL>': {
            name: 'أ. سارة علي',
            email: '<EMAIL>',
            password: 'password',
            userType: 'teacher',
            registrationDate: new Date().toISOString(),
            specialization: 'الفيزياء',
            status: 'نشط',
            id: '<EMAIL>',
            assignedStudents: ['<EMAIL>']
        },
        '<EMAIL>': {
            name: 'أحمد الغامدي',
            email: '<EMAIL>',
            password: 'password',
            userType: 'student',
            registrationDate: new Date().toISOString(),
            specialization: 'المرحلة الثانوية',
            status: 'نشط',
            id: '<EMAIL>',
            countryId: 'sa',
            subscriptions: [
                { subject: 'الرياضيات', totalHours: 20 },
                { subject: 'الفيزياء', totalHours: 15 }
            ]
        },
        '<EMAIL>': {
            name: 'فاطمة الزهراني',
            email: '<EMAIL>',
            password: 'password',
            userType: 'student',
            registrationDate: new Date().toISOString(),
            specialization: 'المرحلة الثانوية',
            status: 'نشط',
            id: '<EMAIL>',
            countryId: 'qa',
            subscriptions: [
                { subject: 'الرياضيات', totalHours: 25 }
            ]
        }
    };
    
    const usersWithDefaults = { ...defaultUsers, ...users };
    
    localStorage.setItem(MOCK_USERS_DB, JSON.stringify(usersWithDefaults));
    return usersWithDefaults;
};

const useAuthHook = () => {
    const [user, setUser] = useState(null);
    const [users, setUsers] = useState({});
    const [loading, setLoading] = useState(true);
    const [loginMethod, setLoginMethod] = useState(null);
    const [useExternalAPI, setUseExternalAPI] = useState(false);
    
    useEffect(() => {
        const initializeAuth = async () => {
            // التحقق من توفر API الخارجي
            const apiAvailable = await isAPIAvailable();
            setUseExternalAPI(apiAvailable);

            if (apiAvailable) {
                console.log('🌐 استخدام قاعدة البيانات الخارجية');
                // تحميل البيانات من API الخارجي
                await loadUsersFromAPI();
            } else {
                console.log('💾 استخدام التخزين المحلي (localStorage)');
                // استخدام localStorage كـ fallback
                const allUsers = initializeUsers();
                setUsers(allUsers);
            }

            // تحميل جلسة المستخدم الحالية
            const sessionUserJson = localStorage.getItem(CURRENT_USER_SESSION);
            if (sessionUserJson) {
                try {
                    const sessionData = JSON.parse(sessionUserJson);
                    if (useExternalAPI) {
                        // التحقق من صحة الجلسة مع API
                        const userExists = await verifyUserSession(sessionData.user.email);
                        if (userExists) {
                            setUser(sessionData.user);
                            setLoginMethod(sessionData.method);
                        } else {
                            localStorage.removeItem(CURRENT_USER_SESSION);
                        }
                    } else {
                        // التحقق من localStorage
                        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
                        if (allUsers[sessionData.user.email]) {
                            setUser(allUsers[sessionData.user.email]);
                            setLoginMethod(sessionData.method);
                        } else {
                            localStorage.removeItem(CURRENT_USER_SESSION);
                        }
                    }
                } catch(e) {
                    console.error("Error parsing user session", e);
                    localStorage.removeItem(CURRENT_USER_SESSION);
                }
            }

            setLoading(false);
        };

        initializeAuth();
    }, []);

    // تحميل المستخدمين من API الخارجي
    const loadUsersFromAPI = async () => {
        try {
            const response = await usersAPI.getAll();
            const usersArray = response.users || [];
            const usersObject = {};
            usersArray.forEach(user => {
                usersObject[user.email] = user;
            });
            setUsers(usersObject);
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين من API:', error);
            // العودة إلى localStorage في حالة الخطأ
            const allUsers = initializeUsers();
            setUsers(allUsers);
        }
    };

    // التحقق من صحة جلسة المستخدم مع API
    const verifyUserSession = async (userEmail) => {
        try {
            const response = await usersAPI.getById(userEmail);
            return response && response.user;
        } catch (error) {
            console.error('خطأ في التحقق من جلسة المستخدم:', error);
            return false;
        }
    };

    const login = async (email, password) => {
        try {
            let userData = null;

            if (useExternalAPI) {
                // محاولة تسجيل الدخول عبر API الخارجي
                try {
                    const response = await usersAPI.login({ email, password });
                    userData = response.user;

                    // تسجيل محاولة الدخول
                    await loginAttemptsAPI.log({
                        user_email: email,
                        attempt_type: 'success',
                        success: true
                    });
                } catch (apiError) {
                    console.error('خطأ في API، العودة إلى localStorage:', apiError);
                    // العودة إلى localStorage في حالة فشل API
                    return await loginWithLocalStorage(email, password);
                }
            } else {
                // استخدام localStorage
                return await loginWithLocalStorage(email, password);
            }

            if (!userData || userData.status !== 'نشط') {
                return { success: false, message: 'بيانات الدخول غير صحيحة أو الحساب غير نشط' };
            }

            // التحقق من الجهاز إذا كان مسجل من قبل (للمستخدمين العاديين فقط)
            if (userData.deviceRegistered && userData.userType === 'student') {
                const deviceVerified = await verifyUserDevice(email);
                if (!deviceVerified.success) {
                    return deviceVerified;
                }
            }

            const sessionData = { user: userData, method: 'password' };
            localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
            setUser(userData);
            setLoginMethod('password');

            return { success: true, user: userData };
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
        }
    };

    // دالة مساعدة لتسجيل الدخول باستخدام localStorage
    const loginWithLocalStorage = async (email, password) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const userData = allUsers[email];

        if (!userData || userData.password !== password || userData.status !== 'نشط') {
            return { success: false, message: 'بيانات الدخول غير صحيحة أو الحساب غير نشط' };
        }

        // التحقق من الجهاز للمستخدمين العاديين فقط
        if (userData.deviceRegistered && userData.userType === 'student') {
            const deviceVerified = await verifyUserDevice(email);
            if (!deviceVerified.success) {
                return deviceVerified;
            }
        }

        const sessionData = { user: userData, method: 'password' };
        localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
        setUser(userData);
        setLoginMethod('password');

        return { success: true, user: userData };
    };

    // دالة مساعدة للتحقق من جهاز المستخدم
    const verifyUserDevice = async (email) => {
        try {
            const currentFingerprint = await getDeviceFingerprint();
            const currentHash = await hashString(currentFingerprint);

            if (useExternalAPI) {
                // التحقق عبر API الخارجي
                const response = await devicesAPI.verify(email, currentHash);
                if (!response.is_valid) {
                    return {
                        success: false,
                        message: 'لا يمكن تسجيل الدخول من هذا الجهاز. يجب استخدام الجهاز المسجل مسبقاً.'
                    };
                }

                // تحديث آخر ظهور
                await devicesAPI.updateLastSeen(email, response.device_id);
            } else {
                // التحقق من localStorage
                const deviceKey = `device_${email}`;
                const storedDeviceData = localStorage.getItem(deviceKey);

                if (storedDeviceData) {
                    const deviceInfo = JSON.parse(storedDeviceData);

                    if (deviceInfo.fingerprint !== currentHash) {
                        return {
                            success: false,
                            message: 'لا يمكن تسجيل الدخول من هذا الجهاز. يجب استخدام الجهاز المسجل مسبقاً.'
                        };
                    }

                    updateDeviceLastSeen(email);
                } else {
                    return {
                        success: false,
                        message: 'لم يتم العثور على بيانات الجهاز المسجل. يرجى التواصل مع الدعم الفني.'
                    };
                }
            }

            return { success: true };
        } catch (error) {
            console.error('خطأ في التحقق من الجهاز:', error);
            return { success: false, message: 'حدث خطأ أثناء التحقق من الجهاز' };
        }
    };

    const loginWithAccessCode = async (accessCode) => {
        const { data: codeData, error } = await supabase
            .from('access_codes')
            .select('*')
            .eq('code', accessCode)
            .single();

        if (error || !codeData) {
            return { success: false, message: 'الكود غير صحيح أو غير موجود.' };
        }
        
        if (!codeData.is_active) {
            return { success: false, message: 'هذا الكود غير نشط حالياً.' };
        }
        
        const fingerprint = await getDeviceFingerprint();
        const fingerprintHash = await hashString(fingerprint);

        if (codeData.device_fingerprint_hash) {
            if (codeData.device_fingerprint_hash !== fingerprintHash) {
                return { success: false, message: 'تم استخدام هذا الكود من جهاز آخر ولا يمكن استخدامه هنا.' };
            }
        } else {
            await supabase
                .from('access_codes')
                .update({ 
                    device_fingerprint_hash: fingerprintHash,
                    first_used_at: new Date().toISOString()
                })
                .eq('id', codeData.id);
        }

        await supabase
            .from('access_codes')
            .update({ last_used_at: new Date().toISOString() })
            .eq('id', codeData.id);

        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const userData = allUsers[codeData.user_email];

        if (!userData || userData.status !== 'نشط') {
            return { success: false, message: 'الحساب المرتبط بهذا الكود غير نشط.' };
        }

        const sessionData = { user: userData, method: 'access_code' };
        localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
        setUser(userData);
        setLoginMethod('access_code');
        return { success: true, user: userData };
    };


    const register = async (userData, byAdmin = false) => {
        try {
            // جمع وحفظ بيانات الجهاز عند التسجيل للمستخدمين العاديين فقط
            let deviceInfo = null;
            const userType = byAdmin ? userData.userType : 'student';
            const shouldRegisterDevice = !byAdmin && userType === 'student';

            if (shouldRegisterDevice) {
                deviceInfo = await saveDeviceInfo(userData.email);
            }

            const newUser = {
                ...userData,
                id: userData.email,
                registrationDate: new Date().toISOString(),
                status: userData.status || 'نشط',
                userType: userType,
                deviceRegistered: shouldRegisterDevice,
                deviceId: deviceInfo?.deviceId || null
            };

            if (useExternalAPI) {
                // حفظ في قاعدة البيانات الخارجية
                try {
                    const response = await usersAPI.create(newUser);

                    // حفظ بيانات الجهاز في قاعدة البيانات الخارجية للمستخدمين العاديين فقط
                    if (deviceInfo && shouldRegisterDevice) {
                        await devicesAPI.register({
                            ...deviceInfo,
                            user_email: userData.email
                        });
                    }

                    // تحديث الحالة المحلية
                    const updatedUsers = { ...users, [userData.email]: newUser };
                    setUsers(updatedUsers);

                } catch (apiError) {
                    console.error('خطأ في API، العودة إلى localStorage:', apiError);
                    // العودة إلى localStorage في حالة فشل API
                    return await registerWithLocalStorage(newUser, deviceInfo, byAdmin);
                }
            } else {
                // استخدام localStorage
                return await registerWithLocalStorage(newUser, deviceInfo, byAdmin);
            }

            if (!byAdmin) {
                const sessionData = { user: newUser, method: 'register' };
                localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
                setUser(newUser);
                setLoginMethod('register');
            }

            return true;
        } catch (error) {
            console.error('خطأ في التسجيل:', error);
            return false;
        }
    };

    // دالة مساعدة للتسجيل باستخدام localStorage
    const registerWithLocalStorage = async (newUser, deviceInfo, byAdmin) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        if (allUsers[newUser.email]) {
            return false;
        }

        const updatedUsers = { ...allUsers, [newUser.email]: newUser };
        setUsers(updatedUsers);
        localStorage.setItem(MOCK_USERS_DB, JSON.stringify(updatedUsers));

        if (!byAdmin) {
            const sessionData = { user: newUser, method: 'register' };
            localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
            setUser(newUser);
            setLoginMethod('register');
        }

        return true;
    };

    const logout = () => {
        localStorage.removeItem(CURRENT_USER_SESSION);
        setUser(null);
        setLoginMethod(null);
    };

    const updateUser = useCallback((originalEmail, updatedData) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        const newEmail = updatedData.email;

        if (allUsers[originalEmail]) {
            const originalUserData = { ...allUsers[originalEmail] };
            const updatedUser = { ...originalUserData, ...updatedData };
            
            if (!updatedData.password) {
                updatedUser.password = originalUserData.password;
            }

            if (originalEmail !== newEmail) {
                if (allUsers[newEmail]) {
                    console.error("New email already exists.");
                    return false;
                }
                delete allUsers[originalEmail];
            }

            allUsers[newEmail] = updatedUser;

            if (user && user.email === originalEmail) {
                const sessionData = JSON.parse(localStorage.getItem(CURRENT_USER_SESSION));
                sessionData.user = updatedUser;
                localStorage.setItem(CURRENT_USER_SESSION, JSON.stringify(sessionData));
                setUser(updatedUser);
            }

            setUsers(allUsers);
            localStorage.setItem(MOCK_USERS_DB, JSON.stringify(allUsers));
            return true;
        }
        return false;
    }, [user]);

    const updateUserMetadata = useCallback((metadata) => {
        if (user) {
            const updatedUserData = { ...user, ...metadata };
            updateUser(user.email, updatedUserData);
        }
    }, [user, updateUser]);


    const deleteUser = (email) => {
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        delete allUsers[email];
        setUsers(allUsers);
        localStorage.setItem(MOCK_USERS_DB, JSON.stringify(allUsers));
    };

    const getAllUsers = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers);
    }, []);
    
    const getAllStudents = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers).filter(u => u.userType === 'student');
    }, []);

    const getAllTeachers = useCallback(() => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return Object.values(allUsers).filter(u => u.userType === 'teacher');
    }, []);

    const getUserById = useCallback((email) => {
      const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
      return allUsers[email];
    }, []);
    
    const getStudentsForTeacher = useCallback((teacherEmail, bookings) => {
        const studentEmails = new Set();
        bookings.forEach(booking => {
            if (booking.teacherEmail === teacherEmail && (booking.status === 'approved' || booking.status === 'completed')) {
                studentEmails.add(booking.studentEmail);
            }
        });
        const allUsers = JSON.parse(localStorage.getItem(MOCK_USERS_DB) || '{}');
        return Array.from(studentEmails).map(email => allUsers[email]).filter(Boolean);
    }, []);

    return { 
        user,
        loginMethod,
        isAuthenticatedWithCode: loginMethod === 'access_code',
        users: getAllUsers(), 
        students: getAllStudents(),
        teachers: getAllTeachers(),
        getUserById,
        getStudentsForTeacher,
        login, 
        loginWithAccessCode,
        register, 
        logout, 
        loading,
        updateUser,
        updateUserMetadata,
        deleteUser,
        getAllStudents,
        getAllTeachers
    };
};

export const AuthProvider = ({ children }) => {
    const auth = useAuthHook();
    return (
        <AuthContext.Provider value={auth}>
            {!auth.loading && children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};