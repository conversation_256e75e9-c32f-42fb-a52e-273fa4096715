import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Users, 
  Shield, 
  Activity,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const DeviceStats = () => {
  const [stats, setStats] = useState({
    totalDevices: 0,
    activeDevices: 0,
    deviceTypes: {
      desktop: 0,
      mobile: 0,
      tablet: 0
    },
    browsers: {},
    platforms: {},
    recentActivity: []
  });

  useEffect(() => {
    calculateStats();
  }, []);

  const calculateStats = () => {
    try {
      const devices = [];
      const browsers = {};
      const platforms = {};
      const deviceTypes = { desktop: 0, mobile: 0, tablet: 0 };

      // جمع بيانات الأجهزة من localStorage للطلاب فقط
      const usersData = JSON.parse(localStorage.getItem('gulfAcademyUsers') || '{}');

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('device_')) {
          try {
            const deviceData = JSON.parse(localStorage.getItem(key));
            const userEmail = key.replace('device_', '');
            const user = usersData[userEmail];

            // تضمين الجهاز فقط إذا كان المستخدم طالب
            if (user && user.userType === 'student') {
              devices.push(deviceData);
            
              // تصنيف نوع الجهاز
              const userAgent = deviceData.browser?.userAgent || '';
              if (userAgent.includes('Mobile')) {
                deviceTypes.mobile++;
              } else if (userAgent.includes('Tablet')) {
                deviceTypes.tablet++;
              } else {
                deviceTypes.desktop++;
              }

              // إحصائيات المتصفحات
              const browserName = deviceData.browser?.name || 'Unknown';
              browsers[browserName] = (browsers[browserName] || 0) + 1;

              // إحصائيات المنصات
              const platform = deviceData.system?.platform || 'Unknown';
              platforms[platform] = (platforms[platform] || 0) + 1;
            }
          } catch (error) {
            console.error('خطأ في قراءة بيانات الجهاز:', error);
          }
        }
      }

      // حساب الأجهزة النشطة (آخر 24 ساعة)
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const activeDevices = devices.filter(device => {
        const lastSeen = new Date(device.lastSeen);
        return lastSeen > oneDayAgo;
      }).length;

      // النشاط الأخير
      const recentActivity = devices
        .sort((a, b) => new Date(b.lastSeen) - new Date(a.lastSeen))
        .slice(0, 5)
        .map(device => ({
          userEmail: device.userEmail,
          deviceType: getDeviceType(device),
          lastSeen: device.lastSeen,
          browser: device.browser?.name
        }));

      setStats({
        totalDevices: devices.length,
        activeDevices,
        deviceTypes,
        browsers,
        platforms,
        recentActivity
      });
    } catch (error) {
      console.error('خطأ في حساب الإحصائيات:', error);
    }
  };

  const getDeviceType = (device) => {
    const userAgent = device.browser?.userAgent || '';
    if (userAgent.includes('Mobile')) return 'mobile';
    if (userAgent.includes('Tablet')) return 'tablet';
    return 'desktop';
  };

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Tablet className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  const getDeviceTypeLabel = (type) => {
    switch (type) {
      case 'mobile': return 'هاتف محمول';
      case 'tablet': return 'جهاز لوحي';
      default: return 'حاسوب مكتبي';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-SA', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Activity className="h-6 w-6 text-primary" />
        <div>
          <h2 className="text-2xl font-bold text-white">إحصائيات أجهزة الطلاب</h2>
          <p className="text-muted-foreground">إحصائيات الأجهزة المسجلة للطلاب فقط</p>
        </div>
      </div>

      {/* الإحصائيات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-secondary/50 border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">إجمالي الأجهزة</p>
                <p className="text-2xl font-bold text-white">{stats.totalDevices}</p>
              </div>
              <Shield className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-secondary/50 border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">الأجهزة النشطة</p>
                <p className="text-2xl font-bold text-green-400">{stats.activeDevices}</p>
              </div>
              <Activity className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-secondary/50 border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">أجهزة الحاسوب</p>
                <p className="text-2xl font-bold text-blue-400">{stats.deviceTypes.desktop}</p>
              </div>
              <Monitor className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-secondary/50 border-border">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">الهواتف المحمولة</p>
                <p className="text-2xl font-bold text-purple-400">{stats.deviceTypes.mobile}</p>
              </div>
              <Smartphone className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* توزيع المتصفحات */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white">توزيع المتصفحات</CardTitle>
            <CardDescription>الأجهزة حسب نوع المتصفح</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.browsers).map(([browser, count]) => (
                <div key={browser} className="flex items-center justify-between">
                  <span className="text-white">{browser}</span>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
              {Object.keys(stats.browsers).length === 0 && (
                <p className="text-muted-foreground text-center py-4">لا توجد بيانات</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* توزيع المنصات */}
        <Card className="bg-secondary/50 border-border">
          <CardHeader>
            <CardTitle className="text-white">توزيع المنصات</CardTitle>
            <CardDescription>الأجهزة حسب نظام التشغيل</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.platforms).map(([platform, count]) => (
                <div key={platform} className="flex items-center justify-between">
                  <span className="text-white">{platform}</span>
                  <Badge variant="secondary">{count}</Badge>
                </div>
              ))}
              {Object.keys(stats.platforms).length === 0 && (
                <p className="text-muted-foreground text-center py-4">لا توجد بيانات</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* النشاط الأخير */}
      <Card className="bg-secondary/50 border-border">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            النشاط الأخير
          </CardTitle>
          <CardDescription>آخر الأجهزة التي تم استخدامها</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.recentActivity.map((activity, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-secondary/30 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  {getDeviceIcon(activity.deviceType)}
                  <div>
                    <p className="text-white font-medium">{activity.userEmail}</p>
                    <p className="text-sm text-muted-foreground">
                      {getDeviceTypeLabel(activity.deviceType)} • {activity.browser}
                    </p>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">
                  {formatDate(activity.lastSeen)}
                </span>
              </motion.div>
            ))}
            {stats.recentActivity.length === 0 && (
              <p className="text-muted-foreground text-center py-4">لا يوجد نشاط حديث</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DeviceStats;
