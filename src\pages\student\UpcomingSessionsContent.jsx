import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Calendar, Plus, Clock, UserX, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Link as LinkIcon } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose, DialogDescription } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Progress } from '@/components/ui/progress';
import { format, parseISO, isAfter, isBefore, addMinutes } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';

const ActionModal = ({ type, booking, onSubmit }) => {
  const [reason, setReason] = useState('');
  const { toast } = useToast();
  
  const modalConfig = {
    postpone: { title: "طلب تأجيل", description: `لماذا ترغب في تأجيل اشتراكك في مادة ${booking.subjects.map(s=>s.subject).join(", ")}?`, button: "إرسال طلب التأجيل" },
    changeTeacher: { title: "طلب تغيير المعلم", description: `لماذا ترغب في تغيير المعلم لاشتراكك في ${booking.subjects.map(s=>s.subject).join(", ")}?`, button: "إرسال طلب التغيير" },
    delete: { title: "طلب إلغاء الاشتراك", description: `هل أنت متأكد من رغبتك في إلغاء الاشتراك في ${booking.subjects.map(s=>s.subject).join(", ")}? سيتم إرسال طلبك للإدارة للموافقة. يرجى توضيح السبب.`, button: "تأكيد طلب الإلغاء" },
    reportProblem: { title: "الإبلاغ عن مشكلة في الرابط", description: `أبلغ عن مشكلة في رابط الحصة لمادة ${booking.subjects.map(s=>s.subject).join(", ")}. سيتم إشعار الإدارة فوراً لإنشاء رابط جديد.`, button: "إرسال البلاغ" },
  };
  
  const currentConfig = modalConfig[type];

  const handleSubmit = () => {
    if ((type !== 'reportProblem') && !reason.trim()) {
      toast({ title: "خطأ", description: "يرجى كتابة السبب.", variant: "destructive" });
      return;
    }
    onSubmit(booking.id, reason);
  };

  return (
    <DialogContent className="bg-secondary border-border text-white">
      <DialogHeader>
        <DialogTitle>{currentConfig.title}</DialogTitle>
        <DialogDescription>{currentConfig.description}</DialogDescription>
      </DialogHeader>
      {type !== 'reportProblem' && (
        <div className="py-4 space-y-4">
          <Label htmlFor="reason-textarea">السبب</Label>
          <Textarea id="reason-textarea" value={reason} onChange={(e) => setReason(e.target.value)} placeholder="اكتب السبب هنا..." className="bg-white/10 border-border" />
        </div>
      )}
      <DialogFooter>
        <DialogClose asChild><Button variant="outline">إلغاء</Button></DialogClose>
        <DialogClose asChild><Button onClick={handleSubmit}>{currentConfig.button}</Button></DialogClose>
      </DialogFooter>
    </DialogContent>
  );
};

const UpcomingSessionsContent = ({ onBookNew }) => {
  const { user, isAuthenticatedWithCode } = useAuth();
  const { bookings, requestPostponement, requestTeacherChange, requestSubscriptionDeletion, requestNewLink, trackSessionJoin, trackSessionLeave } = useBooking();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  
  const [modalState, setModalState] = useState({
      type: null, // 'postpone', 'changeTeacher', 'delete', 'reportProblem'
      booking: null,
      isOpen: false
  });

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 60000); // Update every minute
    return () => clearInterval(timer);
  }, []);

  const openModal = (type, booking) => setModalState({ type, booking, isOpen: true });
  const closeModal = () => setModalState({ type: null, booking: null, isOpen: false });

  const myBookings = bookings.filter(b => 
    (b.studentEmails && b.studentEmails.includes(user.email)) && 
    (b.status === 'approved' || b.status === 'pending')
  );

  const handleSubmit = (bookingId, reason) => {
    switch (modalState.type) {
      case 'postpone':
        requestPostponement(bookingId, reason, 'student');
        toast({ title: "✅ تم إرسال طلب التأجيل." });
        break;
      case 'changeTeacher':
        requestTeacherChange(bookingId, reason);
        toast({ title: "✅ تم إرسال طلب تغيير المعلم." });
        break;
      case 'delete':
        requestSubscriptionDeletion(bookingId, reason);
        toast({ title: "✅ تم إرسال طلب إلغاء الاشتراك." });
        break;
      case 'reportProblem':
        requestNewLink(bookingId, "أبلغ الطالب عن مشكلة في الرابط.");
        toast({ title: "✅ تم إرسال البلاغ للإدارة.", description: "سيتم تزويدك برابط جديد قريباً." });
        break;
      default: break;
    }
    closeModal();
  };
  
  const handleJoin = (booking, session) => {
    if(!isAuthenticatedWithCode) {
        toast({ title: "الوصول مقيد", description: "يرجى تسجيل الدخول بكود الوصول للانضمام للحصة.", variant: "destructive" });
        navigate('/login/access-code');
        return;
    }
    if (booking.zoomLink && session) {
        trackSessionJoin(booking.id, session.sessionId, 'student');
        const newWindow = window.open(booking.zoomLink, '_blank', 'noopener,noreferrer');
        
        const checkWindowClosed = setInterval(() => {
            if (!newWindow || newWindow.closed) {
                trackSessionLeave(booking.id, session.sessionId, 'student');
                clearInterval(checkWindowClosed);
            }
        }, 1000);

    } else {
        toast({ title: "خطأ", description: "رابط الحصة غير متوفر بعد أو لا توجد حصة قادمة.", variant: "destructive" });
    }
  }

  const getStatusBadge = (booking) => {
    if (booking.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-blue-900 text-blue-300">بانتظار الموافقة</span>;
    }
    if (booking.linkProblemRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-orange-900 text-orange-300">بلاغ عن رابط قيد المراجعة</span>;
    }
    if (booking.deletionRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-red-900 text-red-300">طلب إلغاء قيد المراجعة</span>;
    }
    if (booking.teacherChangeRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-cyan-900 text-cyan-300">طلب تغيير معلم قيد المراجعة</span>;
    }
    if (booking.postponementRequest?.status === 'pending') {
        return <span className="text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full bg-yellow-900 text-yellow-300">طلب تأجيل قيد المراجعة</span>;
    }
    return null;
  };

  const getNextSessionInfo = (booking) => {
    if (booking.status !== 'approved') return { text: "الحجز غير مفعل بعد", session: null };
    const upcomingSessions = (booking.sessions || [])
      .filter(s => s.status === 'scheduled' && isAfter(parseISO(`${s.date}T${s.time}`), new Date()))
      .sort((a, b) => parseISO(`${a.date}T${a.time}`) - parseISO(`${b.date}T${b.time}`));
    
    if (upcomingSessions.length > 0) {
      const nextSession = upcomingSessions[0];
      try {
        const sessionDateTime = parseISO(`${nextSession.date}T${nextSession.time}`);
        return {
            text: `الحصة القادمة: ${format(sessionDateTime, "eeee, d MMMM yyyy 'الساعة' p", { locale: arSA })}`,
            session: nextSession
        };
      } catch (e) {
        return { text: "تاريخ غير صالح", session: null };
      }
    }
    return { text: "لا توجد حصص قادمة مجدولة", session: null };
  };

  return (
    <>
      <div className="space-y-8">
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-white">اشتراكاتي</h3>
            {onBookNew && (
              <Button onClick={onBookNew} className="bg-primary text-primary-foreground hover:bg-primary/90">
                <Plus className="h-4 w-4 ml-2" />حجز اشتراك جديد
              </Button>
            )}
          </div>
          {myBookings.length === 0 ? (
            <div className="text-center py-12 text-white/70">
              <Calendar className="mx-auto h-12 w-12" />
              <p className="mt-4">ليس لديك أي اشتراكات.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {myBookings.map(booking => {
                const { text: nextSessionText, session: nextSession } = getNextSessionInfo(booking);
                const completedHours = (booking.completedDuration || 0) / 60;
                const progress = booking.totalHours > 0 ? (completedHours / booking.totalHours) * 100 : 0;
                const countryName = booking.country?.name || (typeof booking.country === 'string' ? booking.country : '');
                
                let isJoinable = false;
                if (nextSession) {
                    const sessionTime = parseISO(`${nextSession.date}T${nextSession.time}`);
                    const tenMinutesBefore = new Date(sessionTime.getTime() - 10 * 60 * 1000);
                    const sessionEndTime = addMinutes(sessionTime, 90); // Joinable for 90 minutes
                    isJoinable = isAfter(currentTime, tenMinutesBefore) && isBefore(currentTime, sessionEndTime);
                }

                return (
                <div key={booking.id} className="bg-secondary/50 rounded-lg p-4">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center flex-wrap gap-2">
                        <p className="font-medium text-white text-lg">{booking.subjects.map(s=>s.subject).join(', ')} مع {booking.teacherName} {countryName && `(${countryName})`}</p>
                        {getStatusBadge(booking)}
                      </div>
                      <p className="text-sm text-primary mt-1">{nextSessionText}</p>
                       {booking.status === 'approved' && (
                         <div className="w-full mt-2">
                             <div className="flex justify-between text-xs text-white/80 mb-1">
                                 <span>التقدم في الباقة</span>
                                 <span>{completedHours.toFixed(2)} / {booking.totalHours} ساعة</span>
                             </div>
                             <Progress value={progress} className="w-full h-2" />
                         </div>
                       )}
                    </div>
                    {booking.status === 'approved' && (
                      <div className="flex items-center gap-2 flex-wrap">
                        <Button variant="outline" className="border-primary text-primary hover:bg-primary/10 disabled:opacity-50 disabled:cursor-not-allowed" onClick={() => handleJoin(booking, nextSession)} disabled={!isJoinable}>
                          {isJoinable && <span className="animate-pulse absolute h-3 w-3 rounded-full bg-green-400 top-0 right-0 -mt-1 -mr-1"></span>}
                          <LinkIcon className="h-4 w-4 ml-2" />انضم الآن
                        </Button>
                        <Button variant="outline" className="border-orange-500 text-orange-400 hover:bg-orange-500/10" onClick={() => openModal('reportProblem', booking)}><AlertTriangle className="h-4 w-4 ml-2" />إبلاغ عن مشكلة</Button>
                        <Button variant="outline" className="border-yellow-500 text-yellow-400 hover:bg-yellow-500/10" onClick={() => openModal('postpone', booking)}><Clock className="h-4 w-4 ml-2" /> تأجيل</Button>
                        <Button variant="outline" className="border-cyan-500 text-cyan-400 hover:bg-cyan-500/10" onClick={() => openModal('changeTeacher', booking)}><UserX className="h-4 w-4 ml-2" /> تغيير المعلم</Button>
                        <Button variant="outline" className="border-red-500 text-red-400 hover:bg-red-500/10" onClick={() => openModal('delete', booking)}><Trash2 className="h-4 w-4 ml-2" /> إلغاء الاشتراك</Button>
                      </div>
                    )}
                  </div>
                </div>
              )})}
            </div>
          )}
        </div>
      </div>
      <Dialog open={modalState.isOpen} onOpenChange={closeModal}>
        {modalState.type && modalState.booking && <ActionModal type={modalState.type} booking={modalState.booking} onSubmit={handleSubmit} />}
      </Dialog>
    </>
  );
};

export default UpcomingSessionsContent;