{"name": "gulf-academy", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^3.10.0", "@azure/msal-react": "^2.0.12", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@react-pdf/renderer": "^3.4.4", "@supabase/supabase-js": "2.30.0", "axios": "^1.6.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "framer-motion": "^11.2.10", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lucide-react": "^0.395.0", "mysql2": "^3.14.3", "qr-scanner": "^1.4.2", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.16.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.57.1", "eslint-config-react-app": "^7.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}