import React from 'react';
import { Helmet } from 'react-helmet';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { motion } from 'framer-motion';
import { Building, Target, BookOpen, Globe, Users, Puzzle } from 'lucide-react';

const Section = ({ icon, title, children }) => (
  <div className="mb-8">
    <h2 className="flex items-center text-3xl font-bold text-primary mb-4 justify-start">
      {icon}
      <span className="mr-3">{title}</span>
    </h2>
    <div className="text-white/90 text-lg leading-relaxed space-y-3 text-right">
      {children}
    </div>
  </div>
);

const AboutPage = () => {
  return (
    <>
      <Helmet>
        <title>من نحن - Gulf Academy</title>
        <meta name="description" content="تعرف على Gulf Academy، المنصة التعليمية الرقمية المتكاملة التي تخدم طلاب الخليج." />
      </Helmet>
      <div className="flex flex-col min-h-screen gradient-bg text-foreground">
        <Header />
        <main className="flex-grow">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="container mx-auto px-4 py-16"
          >
            <div className="glass-effect rounded-xl p-8 md:p-12 text-right">
              <h1 className="text-5xl font-bold text-white mb-6 flex items-center justify-start">
                🏫
                <span className="mr-4">منصة Gulf Academy</span>
              </h1>
              <p className="text-xl text-white/90 leading-relaxed mb-4">
                Gulf Academy هي منصة تعليمية رقمية متكاملة تقدم خدمات التعليم عن بُعد للطلاب في دول الخليج العربي، وتشمل:
              </p>
              <ul className="list-disc list-inside text-xl text-white/90 space-y-2 mb-6 pr-5">
                <li>المملكة العربية السعودية</li>
                <li>قطر</li>
                <li>الكويت</li>
                <li>سلطنة عُمان</li>
                <li>الإمارات العربية المتحدة</li>
              </ul>
              <p className="text-xl text-white/90 leading-relaxed mb-12">
                تهدف المنصة إلى توفير تجربة تعليمية مرنة، حديثة، وآمنة، تغطي جميع المراحل التعليمية من المرحلة الابتدائية وحتى المرحلة الجامعية، عبر بيئة افتراضية متكاملة وسهلة الاستخدام.
              </p>

              <Section icon={<Target className="h-8 w-8" />} title="الرؤية">
                <p>تمكين الطلاب الخليجيين من الوصول إلى تعليم عالي الجودة عبر الإنترنت، باستخدام أحدث التقنيات التعليمية، وبما يتوافق مع المناهج المحلية لكل دولة خليجية.</p>
              </Section>

              <Section icon={<BookOpen className="h-8 w-8" />} title="خدمات المنصة">
                <ul className="list-disc list-inside space-y-2 pr-5">
                  <li>فصول مباشرة (Live Classes) باستخدام Zoom</li>
                  <li>مكتبة دروس مسجلة يمكن للطالب الرجوع إليها في أي وقت</li>
                  <li>مناهج مصنفة حسب الصف والدولة</li>
                  <li>أنظمة تقييم واختبارات تفاعلية</li>
                  <li>دعم فني وتعليمي مخصص للطلاب والمعلمين</li>
                  <li>أدوات تواصل فعّالة بين الطالب والمعلم وأولياء الأمور</li>
                </ul>
              </Section>

              <Section icon={<Globe className="h-8 w-8" />} title="الميزات التقنية">
                 <ul className="list-disc list-inside space-y-2 pr-5">
                  <li>دعم كامل للغة العربية</li>
                  <li>تكامل مع أدوات التعليم الحديثة مثل:
                    <ul className="list-circle list-inside pr-5 mt-2 space-y-1">
                        <li>Zoom</li>
                        <li>Twilio</li>
                        <li>Google Workspace</li>
                    </ul>
                  </li>
                  <li>تجربة مستخدم سلسة على جميع الأجهزة (جوال، تابلت، كمبيوتر)</li>
                  <li>أمان وحماية بيانات عالية وفق معايير الخصوصية الخليجية والدولية</li>
                </ul>
              </Section>

              <Section icon={<Users className="h-8 w-8" />} title="الفئة المستهدفة">
                <ul className="list-disc list-inside space-y-2 pr-5">
                  <li>طلاب المدارس والجامعات في الخليج</li>
                  <li>أولياء الأمور الراغبين في تعليم مرن وآمن لأبنائهم</li>
                  <li>معلمون يرغبون في تقديم دروس عن بُعد بسهولة واحترافية</li>
                </ul>
              </Section>

              <Section icon={<Puzzle className="h-8 w-8" />} title="لماذا Gulf Academy؟">
                <ul className="list-disc list-inside space-y-2 pr-5">
                  <li>تعليم يتجاوز الحواجز الجغرافية</li>
                  <li>محتوى مخصص حسب كل دولة خليجية</li>
                  <li>دعم فني وتربوي مستمر</li>
                  <li>منصة موثوقة تربط بين الجودة التعليمية والتقنية الحديثة.</li>
                </ul>
              </Section>
            </div>
          </motion.div>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default AboutPage;