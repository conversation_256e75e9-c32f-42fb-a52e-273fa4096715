import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2, Message<PERSON><PERSON>re, Send } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuFooter
} from '@/components/ui/dropdown-menu';
import { useNotifications } from '@/contexts/NotificationsContext';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';

const NotificationsDropdown = () => {
  const { user } = useAuth();
  const { getUserNotifications, markAsRead, markAllAsRead, deleteAllForUser } = useNotifications();
  const userNotifications = user ? getUserNotifications(user.email, user.userType) : [];
  const unreadCount = userNotifications.filter(n => !n.read).length;
  const navigate = useNavigate();

  const getIconForType = (type) => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'booking': return '📅';
      case 'chat': return <MessageSquare className="h-4 w-4 text-blue-400" />;
      case 'announcement': return <Send className="h-4 w-4 text-primary" />;
      default: return 'ℹ️';
    }
  };
  
  const handleMarkAllRead = (e) => {
      e.stopPropagation();
      markAllAsRead(user.email, user.userType);
  }

  const handleDeleteAll = (e) => {
      e.stopPropagation();
      deleteAllForUser(user.email, user.userType);
  }

  const handleNotificationClick = (notification) => {
    markAsRead(notification.id);
    if(notification.link) {
        navigate(notification.link, { replace: true });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="text-white relative">
          <Bell className="h-6 w-6" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
              {unreadCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80 bg-secondary border-border text-white" align="end">
        <DropdownMenuLabel className="flex justify-between items-center">
            <span>الإشعارات</span>
            {unreadCount > 0 && <span className="text-xs text-primary">{unreadCount} جديد</span>}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <div className="max-h-96 overflow-y-auto">
          {userNotifications.length > 0 ? (
            userNotifications.map(notification => (
              <DropdownMenuItem
                key={notification.id}
                className={`flex items-start gap-3 p-2 cursor-pointer ${!notification.read ? 'bg-primary/10' : ''}`}
                onSelect={(e) => {
                  e.preventDefault();
                  handleNotificationClick(notification);
                }}
              >
                <span className="text-lg mt-1">{getIconForType(notification.type)}</span>
                <div className="flex-1">
                  <p className="text-sm text-white">{notification.message}</p>
                  <p className="text-xs text-white/60">
                    {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true, locale: arSA })}
                  </p>
                </div>
              </DropdownMenuItem>
            ))
          ) : (
            <p className="text-center text-sm text-white/70 py-4">لا توجد إشعارات جديدة.</p>
          )}
        </div>
        {userNotifications.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuFooter className="p-1 flex justify-around">
                <Button variant="ghost" className="w-full text-center text-sm text-primary" onClick={handleMarkAllRead}>
                    <CheckCheck className="h-4 w-4 ml-2" />
                    تحديد الكل كمقروء
                </Button>
                <Button variant="ghost" className="w-full text-center text-sm text-destructive" onClick={handleDeleteAll}>
                    <Trash2 className="h-4 w-4 ml-2" />
                    حذف الكل
                </Button>
            </DropdownMenuFooter>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationsDropdown;