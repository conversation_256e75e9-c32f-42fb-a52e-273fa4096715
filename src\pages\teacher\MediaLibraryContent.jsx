import React, { useState, useEffect, useCallback } from 'react';
    import { useToast } from '@/components/ui/use-toast';
    import { supabase } from '@/lib/customSupabaseClient';
    import { useAuth } from '@/contexts/AuthContext';
    import MediaUploadForm from '@/components/MediaUploadForm';
    import MediaList from '@/components/MediaList';

    const MediaLibraryContent = () => {
        const { toast } = useToast();
        const { user, students, teachers } = useAuth();
        const [media, setMedia] = useState([]);
        const [assignments, setAssignments] = useState([]);
        const [loading, setLoading] = useState(true);

        const fetchMedia = useCallback(async () => {
            setLoading(true);
            const { data: mediaData, error: mediaError } = await supabase
                .from('study_materials')
                .select('*')
                .eq('uploaded_by_email', user.email)
                .order('created_at', { ascending: false });

            if (mediaError) {
                toast({ title: 'خطأ', description: 'فشل في جلب المواد.', variant: 'destructive' });
                setMedia([]);
            } else {
                setMedia(mediaData);
                const mediaIds = mediaData.map(m => m.id);
                if (mediaIds.length > 0) {
                    const { data: assignmentData, error: assignmentError } = await supabase
                        .from('material_assignments')
                        .select('*')
                        .in('material_id', mediaIds);
                    if (assignmentError) {
                         toast({ title: 'خطأ', description: 'فشل في جلب تخصيصات المواد.', variant: 'destructive' });
                         setAssignments([]);
                    } else {
                        setAssignments(assignmentData);
                    }
                } else {
                    setAssignments([]);
                }
            }
            setLoading(false);
        }, [toast, user.email]);

        useEffect(() => {
            fetchMedia();
        }, [fetchMedia]);

        const handleMaterialUploaded = () => {
            fetchMedia();
        };

        const handleDelete = async (mediaId) => {
            const materialToDelete = media.find(m => m.id === mediaId);

            if (materialToDelete.drive_file_id) { // This field now holds the file path in Supabase Storage
                const { error: storageError } = await supabase.storage.from('study_materials').remove([materialToDelete.drive_file_id]);
                if (storageError) {
                    console.warn("Could not delete file from Supabase Storage, maybe it's already deleted.", storageError);
                }
            }
            
            const { error: assignmentError } = await supabase.from('material_assignments').delete().eq('material_id', mediaId);
            if (assignmentError) {
                toast({ title: 'خطأ في حذف التخصيصات', variant: 'destructive' });
            }

            const { error } = await supabase.from('study_materials').delete().eq('id', mediaId);
            if (error) {
                toast({ title: '❌ فشل حذف المادة من قاعدة البيانات', variant: 'destructive' });
            } else {
                toast({ title: '🗑️ تم الحذف بنجاح' });
                fetchMedia();
            }
        };

        return (
            <div className="space-y-8">
                <div className="glass-effect rounded-xl p-6">
                    <div className="flex flex-col md:flex-row items-center justify-between mb-6 gap-4">
                        <h3 className="text-xl font-bold text-white">مكتبة الوسائط الخاصة بي</h3>
                        <MediaUploadForm onUploadSuccess={handleMaterialUploaded} />
                    </div>
                    
                    <MediaList
                        media={media}
                        assignments={assignments}
                        onDelete={handleDelete}
                        students={students}
                        teachers={teachers}
                        loading={loading}
                        isTeacherView={true}
                    />
                </div>
            </div>
        );
    };

    export default MediaLibraryContent;