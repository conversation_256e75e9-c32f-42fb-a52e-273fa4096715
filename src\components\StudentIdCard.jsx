
import React, { forwardRef } from 'react';
import QRCode from 'qrcode.react';
import { useSettings } from '@/contexts/SettingsContext';
import { format, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';

const StudentIdCard = forwardRef(({ student, accessCode }, ref) => {
    const { settings } = useSettings();

    const getValidityText = (code) => {
        if (!code.expires_at) return 'غير محددة';
        const days = Math.ceil((new Date(code.expires_at) - new Date()) / (1000 * 60 * 60 * 24));
        return days > 0 ? `${days} يوم` : 'منتهية';
    };

    return (
        <div ref={ref} className="bg-gradient-to-br from-gray-900 to-gray-800 text-white rounded-xl shadow-lg w-[350px] h-[200px] p-4 flex flex-col justify-between font-sans">
            <div className="flex justify-between items-start">
                <div className="text-right">
                    <h2 className="text-xl font-bold text-primary">{student.name}</h2>
                    <p className="text-sm text-gray-300">طالب في أكاديمية الخليج</p>
                </div>
                <img src={settings.academyLogoUrl || "https://storage.googleapis.com/hostinger-horizons-assets-prod/5c2a1cb0-081d-44c5-af23-652f3e3e6df8/3f52b2601f61ad478a29b61dbb558f30.png"} alt="شعار الأكاديمية" className="h-12 w-12 object-contain" />
            </div>

            <div className="flex items-center justify-between mt-2">
                <div className="text-right flex-1">
                    <div className="mb-2">
                        <label className="text-xs text-gray-400 block">كود الدخول</label>
                        <p className="font-mono text-lg tracking-widest text-yellow-300">{accessCode.code}</p>
                    </div>
                    <div className="grid grid-cols-2 gap-x-4 text-xs">
                        <div>
                            <label className="text-gray-400 block">تاريخ البدء</label>
                            <p>{accessCode.created_at ? format(parseISO(accessCode.created_at), 'd MMM yyyy', { locale: arSA }) : 'N/A'}</p>
                        </div>
                        <div>
                            <label className="text-gray-400 block">تاريخ الانتهاء</label>
                            <p>{accessCode.expires_at ? format(parseISO(accessCode.expires_at), 'd MMM yyyy', { locale: arSA }) : 'غير محدد'}</p>
                        </div>
                    </div>
                </div>
                <div className="ml-4 p-1 bg-white rounded-md">
                    <QRCode value={accessCode.code} size={70} bgColor="#ffffff" fgColor="#000000" level="Q" />
                </div>
            </div>
        </div>
    );
});

export default StudentIdCard;
