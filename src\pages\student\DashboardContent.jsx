import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Book<PERSON>pen, Clock, Star, ListVideo, PlayCircle, BarChart, Lock } from 'lucide-react';
import UpcomingSessionsContent from '@/pages/student/UpcomingSessionsContent';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { useExam } from '@/contexts/ExamContext';
import { Link, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import ActiveSessionsList from '@/components/ActiveSessionsList';

const DashboardContent = ({ setActiveTab, setIsBookingModalOpen }) => {
  const { user, isAuthenticatedWithCode } = useAuth();
  const { bookings } = useBooking();
  const { exams, results } = useExam();
  const { toast } = useToast();
  const navigate = useNavigate();

  const myBookings = bookings.filter(b => b.studentEmail === user.email);
  const remainingSessions = myBookings
    .filter(b => b.status === 'approved')
    .reduce((acc, b) => acc + (b.sessionsTotal - (b.sessionsCompleted || 0)), 0);
  
  const completedSessions = myBookings
    .filter(b => b.status === 'approved' || b.status === 'completed')
    .reduce((acc, b) => acc + (b.sessionsCompleted || 0), 0);

  const totalHoursCompleted = myBookings.reduce((acc, b) => acc + (b.completedDuration || 0), 0) / 60;

  const myExams = exams
    .filter(exam => (exam.assignedStudents || []).includes(user.email))
    .slice(0, 2);

  const getStatusClass = (status) => {
    switch (status) {
      case 'متاح': return 'bg-blue-500/20 text-blue-300';
      case 'مكتمل': return 'bg-purple-500/20 text-purple-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  const getExamStatus = (examId) => {
    const result = results.find(r => r.examId === examId && r.studentEmail === user.email);
    const exam = exams.find(e => e.id === examId);
    if (result) {
      return { status: 'مكتمل', score: `${result.score}/${exam?.questions.length}` };
    }
    return { status: 'متاح', score: null };
  };

  const handleProtectedFeatureClick = (tabName) => {
    if (!isAuthenticatedWithCode) {
      toast({
        title: "الوصول مقيد",
        description: "الرجاء تسجيل الدخول باستخدام كود الوصول الخاص بك لعرض هذا المحتوى.",
        variant: "destructive",
        action: <Button onClick={() => navigate('/login/access-code')}>تسجيل الدخول بالكود</Button>
      });
    } else {
      setActiveTab(tabName);
    }
  };


  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-primary/20 p-3 rounded-full"><BookOpen className="h-6 w-6 text-primary" /></div><div><p className="text-muted-foreground text-sm">الحصص المتبقية</p><p className="text-2xl font-bold text-white">{remainingSessions}</p></div></div></div>
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-green-500/20 p-3 rounded-full"><Clock className="h-6 w-6 text-green-400" /></div><div><p className="text-muted-foreground text-sm">الحصص المكتملة</p><p className="text-2xl font-bold text-white">{completedSessions}</p></div></div></div>
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-indigo-500/20 p-3 rounded-full"><BarChart className="h-6 w-6 text-indigo-400" /></div><div><p className="text-muted-foreground text-sm">إجمالي الساعات</p><p className="text-2xl font-bold text-white">{totalHoursCompleted.toFixed(1)}</p></div></div></div>
      </div>

      <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
                <PlayCircle className="h-6 w-6 text-green-400 animate-pulse" />
                <h3 className="text-xl font-bold text-white">الحصص الجارية الآن</h3>
            </div>
            {!isAuthenticatedWithCode && (
              <div className="flex items-center gap-2 text-yellow-400 text-sm">
                <Lock size={16} />
                <span>يتطلب الدخول بالكود</span>
              </div>
            )}
          </div>
          {isAuthenticatedWithCode ? (
            <ActiveSessionsList />
          ) : (
             <div className="text-center py-6 text-white/70">
                <p>قم بتسجيل الدخول باستخدام كود الوصول الخاص بك لعرض الحصص الجارية والانضمام إليها.</p>
                <Button size="sm" className="mt-4 bg-primary hover:bg-primary/90" onClick={() => navigate('/login/access-code')}>الدخول بالكود</Button>
            </div>
          )}
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white">الحصص القادمة</h3>
            <Button size="sm" className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => handleProtectedFeatureClick('upcoming-sessions')}>
              <ListVideo className="h-4 w-4 ml-2" />عرض الكل
            </Button>
          </div>
          <UpcomingSessionsContent onBookNew={() => setIsBookingModalOpen(true)} />
        </div>
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white">آخر الاختبارات</h3>
            <Button size="sm" variant="outline" className="border-border text-white hover:bg-secondary/50" onClick={() => setActiveTab('exams')}>
              عرض الكل
            </Button>
          </div>
          <div className="space-y-4">
            {myExams.length > 0 ? myExams.map((exam) => {
              const { status, score } = getExamStatus(exam.id);
              return (
                <div key={exam.id} className="bg-secondary/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-white">{exam.title}</p>
                      <p className="text-muted-foreground text-sm">{exam.subject}</p>
                    </div>
                    {status === 'متاح' ? (
                       <Link to={`/student/exam/${exam.id}`}>
                           <Button size="sm" className="bg-blue-600 hover:bg-blue-700">ابدأ الآن</Button>
                       </Link>
                    ) : (
                       <span className={`px-3 py-1 rounded-full text-xs ${getStatusClass(status)}`}>
                         {score}
                       </span>
                    )}
                  </div>
                </div>
              );
            }) : (
                <div className="text-center py-6 text-white/70">
                    <p>لا توجد اختبارات مخصصة لك حالياً.</p>
                </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardContent;