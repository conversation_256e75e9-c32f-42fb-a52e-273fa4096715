import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/customSupabaseClient';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { ShoppingBag, Users, FileCheck, FileX, Files } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const SalesReportsContent = () => {
    const { users } = useAuth();
    const { toast } = useToast();
    const [salesData, setSalesData] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchSalesData = useCallback(async () => {
        setLoading(true);
        const { data, error } = await supabase.from('sales_reports').select('*');
        if (error) {
            toast({ title: 'خطأ', description: 'فشل في جلب تقارير البيع.', variant: 'destructive' });
        } else {
            setSalesData(data);
        }
        setLoading(false);
    }, [toast]);

    useEffect(() => {
        fetchSalesData();
    }, [fetchSalesData]);

    const stats = useMemo(() => {
        const totalAssignments = salesData.length;
        const activatedStudents = new Set(salesData.map(d => d.student_email)).size;
        
        return {
            totalAssignments,
            activatedStudents,
            // Assuming total students is the number of all students in the system
            totalStudents: users.filter(u => u.userType === 'student').length,
        };
    }, [salesData, users]);

    const salesByAssigner = useMemo(() => {
        const data = salesData.reduce((acc, sale) => {
            const assignerName = sale.assigner_name || 'الإدارة';
            if (!acc[assignerName]) {
                acc[assignerName] = { name: assignerName, count: 0 };
            }
            acc[assignerName].count += 1;
            return acc;
        }, {});
        return Object.values(data);
    }, [salesData]);

    return (
        <div className="space-y-8">
            <div className="glass-effect rounded-xl p-6">
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                    <ShoppingBag />
                    تقارير البيع (تفعيل المكتبة)
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-blue-500/20 p-3 rounded-full"><Users className="h-6 w-6 text-blue-300"/></div>
                        <div><p className="text-sm text-white/70">طلاب مفعل لهم المكتبة</p><p className="text-2xl font-bold">{stats.activatedStudents}</p></div>
                    </div>
                     <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-green-500/20 p-3 rounded-full"><FileCheck className="h-6 w-6 text-green-300"/></div>
                        <div><p className="text-sm text-white/70">إجمالي المواد المفعلة</p><p className="text-2xl font-bold">{stats.totalAssignments}</p></div>
                    </div>
                     <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-red-500/20 p-3 rounded-full"><FileX className="h-6 w-6 text-red-300"/></div>
                        <div><p className="text-sm text-white/70">طلاب غير مفعل لهم</p><p className="text-2xl font-bold">{stats.totalStudents - stats.activatedStudents}</p></div>
                    </div>
                     <div className="stats-card p-4 rounded-lg flex items-center gap-4">
                        <div className="bg-purple-500/20 p-3 rounded-full"><Files className="h-6 w-6 text-purple-300"/></div>
                        <div><p className="text-sm text-white/70">إجمالي الطلاب</p><p className="text-2xl font-bold">{stats.totalStudents}</p></div>
                    </div>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h4 className="text-xl font-bold text-white mb-4">تفاصيل التفعيلات</h4>
                        <div className="overflow-auto max-h-[400px]">
                            <table className="w-full text-right">
                                <thead>
                                    <tr className="border-b border-white/20"><th className="p-2">الطالب</th><th className="p-2">المادة</th><th className="p-2">المُفعِّل</th></tr>
                                </thead>
                                <tbody>
                                    {salesData.map(sale => (
                                        <tr key={sale.id} className="border-b border-white/10 text-sm"><td className="p-2">{sale.student_name}</td><td className="p-2">{sale.material_name}</td><td className="p-2">{sale.assigner_name} ({sale.assigner_role})</td></tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div>
                        <h4 className="text-xl font-bold text-white mb-4">التفعيلات حسب المُفعِّل</h4>
                        <div style={{ width: '100%', height: 300 }}>
                            <ResponsiveContainer>
                                <BarChart data={salesByAssigner}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                                    <XAxis dataKey="name" stroke="rgba(255,255,255,0.7)" />
                                    <YAxis stroke="rgba(255,255,255,0.7)" />
                                    <Tooltip contentStyle={{ backgroundColor: '#1f2937', border: 'none', color: '#fff' }}/>
                                    <Legend wrapperStyle={{ color: '#fff' }} />
                                    <Bar dataKey="count" fill="#8884d8" name="عدد التفعيلات" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SalesReportsContent;