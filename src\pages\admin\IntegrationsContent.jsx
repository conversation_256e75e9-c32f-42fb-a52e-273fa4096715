import React, { useState } from 'react';
import { useSettings } from '@/contexts/SettingsContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, Zap, Save, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/lib/customSupabaseClient';

const IntegrationCard = ({ title, description, icon, isConnected, children }) => (
  <Card className="glass-effect shadow-lg hover-lift border-white/10 flex flex-col">
    <CardHeader className="flex flex-row items-start justify-between">
      <div className="flex items-center gap-4">
        <div className="p-3 bg-primary/20 rounded-full">
          {icon}
        </div>
        <div>
          <CardTitle className="text-xl text-white">{title}</CardTitle>
          <CardDescription className="text-white/70">{description}</CardDescription>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {isConnected ? (
          <>
            <CheckCircle className="text-green-400" />
            <span className="text-green-400 font-semibold">متصل</span>
          </>
        ) : (
          <>
            <XCircle className="text-red-400" />
            <span className="text-red-400 font-semibold">غير متصل</span>
          </>
        )}
      </div>
    </CardHeader>
    <CardContent className="flex-grow">
      {children}
    </CardContent>
  </Card>
);

const CustomZoomIcon = () => (
    <svg width="24" height="24" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-blue-500">
        <path fill="#2D8CFF" d="M381.91 381.91h260.18v260.18H381.91z"></path>
        <path fill="#2D8CFF" d="M512 0C229.22 0 0 229.22 0 512s229.22 512 512 512c282.77 0 512-229.23 512-512S794.77 0 512 0zm195.12 642.09c0 35.84-29.13 64.97-64.97 64.97H381.91c-35.84 0-64.97-29.13-64.97-64.97V381.91c0-35.84 29.13-64.97 64.97-64.97h260.18c35.84 0 64.97 29.13 64.97 64.97v260.18z"></path>
        <path fill="#fff" d="M512 432.22a27.18 27.18 0 0 0-27.18 27.18v101.9a27.18 27.18 0 1 0 54.36 0v-101.9a27.18 27.18 0 0 0-27.18-27.18z"></path>
        <path fill="#fff" d="M432.22 512a27.18 27.18 0 0 0 27.18 27.18h101.9a27.18 27.18 0 0 0 0-54.36h-101.9a27.18 27.18 0 0 0-27.18 27.18z"></path>
    </svg>
);


const IntegrationsContent = () => {
  const { toast } = useToast();
  const { settings, updateSettings } = useSettings();
  const [zoomSettings, setZoomSettings] = useState({
      accountId: settings.zoom?.accountId || '',
      clientId: settings.zoom?.clientId || '',
      clientSecret: settings.zoom?.clientSecret || '',
  });
  const [loading, setLoading] = useState(false);

  const handleSaveZoom = async () => {
    setLoading(true);
    
    // Store secrets in Supabase Vault
    const { error } = await supabase.functions.invoke('set-supabase-secrets', {
        body: {
            secrets: [
                { name: 'ZOOM_ACCOUNT_ID', value: zoomSettings.accountId },
                { name: 'ZOOM_CLIENT_ID', value: zoomSettings.clientId },
                { name: 'ZOOM_CLIENT_SECRET', value: zoomSettings.clientSecret },
            ]
        }
    });

    if (error) {
        setLoading(false);
        toast({
            title: "فشل حفظ بيانات Zoom",
            description: "حدث خطأ أثناء تخزين البيانات بشكل آمن. " + error.message,
            variant: "destructive"
        });
        return;
    }

    // Update local settings (without the secret)
    updateSettings({
      zoom: {
        accountId: zoomSettings.accountId,
        clientId: zoomSettings.clientId,
        clientSecret: '', // Don't store secret in localStorage
        connected: !!(zoomSettings.accountId && zoomSettings.clientId && zoomSettings.clientSecret),
      }
    });
    setLoading(false);
    toast({
      title: "تم حفظ الإعدادات",
      description: "تم تحديث إعدادات Zoom بنجاح.",
    });
  };
  
  const handleInputChange = (e) => {
    const { id, value } = e.target;
    setZoomSettings(prev => ({ ...prev, [id]: value }));
  };

  return (
    <div className="p-4 text-white">
      <h1 className="text-4xl font-bold mb-8 text-gradient">التكاملات</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        
        <IntegrationCard
          title="Supabase"
          description="لإدارة قواعد البيانات، المصادقة، والتخزين."
          icon={<Zap className="text-green-400" />}
          isConnected={true}
        >
          <p className="text-white/80">
            يتم استخدام Supabase لتوفير بنية تحتية آمنة وموثوقة للمنصة. الاتصال مُدار من قبل النظام ولا يمكن قطعه من هنا.
          </p>
        </IntegrationCard>

        <IntegrationCard
          title="Zoom"
          description="لإنشاء روابط اجتماعات فيديو للحصص."
          icon={<CustomZoomIcon />}
          isConnected={settings.zoom?.connected}
        >
          <div className="space-y-4 mt-4 flex flex-col flex-grow">
            <p className="text-sm text-white/80">
              اربط حسابك في Zoom لإنشاء روابط الاجتماعات تلقائياً عند الموافقة على الحجوزات.
            </p>
            <div className="flex-grow space-y-4">
              <div>
                <Label htmlFor="accountId" className="text-white/80">معرّف الحساب (Account ID)</Label>
                <Input id="accountId" value={zoomSettings.accountId} onChange={handleInputChange} placeholder="Account ID" className="bg-white/10 border-border" />
              </div>
              <div>
                <Label htmlFor="clientId" className="text-white/80">معرّف العميل (Client ID)</Label>
                <Input id="clientId" value={zoomSettings.clientId} onChange={handleInputChange} placeholder="Client ID" className="bg-white/10 border-border" />
              </div>
              <div>
                <Label htmlFor="clientSecret" className="text-white/80">الرمز السري للعميل (Client Secret)</Label>
                <Input id="clientSecret" type="password" value={zoomSettings.clientSecret} onChange={handleInputChange} placeholder="Client Secret" className="bg-white/10 border-border" />
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <Button onClick={handleSaveZoom} disabled={loading}>
                {loading ? <Loader2 className="h-4 w-4 animate-spin ml-2" /> : <Save className="h-4 w-4 ml-2" />}
                حفظ وتفعيل
              </Button>
            </div>
          </div>
        </IntegrationCard>

      </div>
    </div>
  );
};

export default IntegrationsContent;