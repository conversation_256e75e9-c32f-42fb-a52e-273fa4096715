import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, User, BookOpen, GraduationCap, Globe, Hourglass, CheckCircle, AlertCircle, Users, CalendarDays, Clock } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { format, parseISO, isAfter } from 'date-fns';
import { arSA } from 'date-fns/locale';

const StudentDetailRow = ({ icon: Icon, label, value, valueClassName = 'text-white' }) => (
  <div className="flex items-center gap-4 text-sm">
    <Icon className="h-5 w-5 text-primary" />
    <span className="font-semibold text-white/80 w-28">{label}:</span>
    <span className={`font-semibold ${valueClassName}`}>{value}</span>
  </div>
);

const StudentsContent = ({ openChat }) => {
  const { user: teacher, users } = useAuth();
  const { bookings } = useBooking();

  const myBookings = bookings.filter(b => b.teacherEmail === teacher.email);

  const getStatusInfo = (status) => {
    switch (status) {
      case 'approved': return { label: 'نشط', icon: CheckCircle, color: 'text-green-400' };
      case 'pending': return { label: 'قيد المراجعة', icon: Hourglass, color: 'text-yellow-400' };
      case 'completed': return { label: 'مكتمل', icon: CheckCircle, color: 'text-gray-400' };
      default: return { label: status, icon: AlertCircle, color: 'text-red-400' };
    }
  };

  const getNextSessionInfo = (booking) => {
    if (booking.status !== 'approved') return null;
    const upcomingSessions = (booking.sessions || [])
      .filter(s => s.status === 'scheduled' && isAfter(parseISO(`${s.date}T${s.time}`), new Date()))
      .sort((a, b) => parseISO(`${a.date}T${a.time}`) - parseISO(`${b.date}T${b.time}`));
    
    if (upcomingSessions.length > 0) {
      const nextSession = upcomingSessions[0];
      try {
        const sessionDateTime = parseISO(`${nextSession.date}T${nextSession.time}`);
        return {
            date: format(sessionDateTime, "eeee, d MMMM yyyy", { locale: arSA }),
            time: format(sessionDateTime, "p", { locale: arSA })
        };
      } catch (e) {
        return null;
      }
    }
    return null;
  };

  return (
    <div className="space-y-6">
       <h3 className="text-3xl font-bold text-white mb-6">قائمة طلابك واشتراكاتهم</h3>
      {myBookings.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {myBookings.map((booking) => {
            const studentNames = booking.studentEmails?.map(email => booking.studentNames[email]).join(', ') || booking.studentName;
            const isGroup = (booking.studentEmails?.length || 1) > 1;
            
            const totalHours = booking.totalHours || 0;
            const completedDuration = booking.completedDuration || 0;
            const remainingHours = (totalHours * 60 - completedDuration) / 60;
            const progress = totalHours > 0 ? (completedDuration / (totalHours * 60)) * 100 : 0;
            
            const statusInfo = getStatusInfo(booking.status);
            const nextSession = getNextSessionInfo(booking);

            return (
              <Card key={booking.id} className="glass-effect border-white/10 text-white shadow-lg hover-lift flex flex-col">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-xl font-bold text-gradient flex items-center gap-2">
                    {isGroup ? <Users /> : <User />} {studentNames}
                  </CardTitle>
                  <Button size="sm" variant="ghost" className="text-white/70 hover:text-white" onClick={() => openChat({ id: booking.studentEmails[0], name: studentNames, type: 'student' })}>
                    <MessageSquare className="h-5 w-5" />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4 pt-4 flex-grow">
                  {isGroup && <Badge variant="secondary" className="text-sm bg-purple-500/20 text-purple-300">حجز جماعي</Badge>}
                  <StudentDetailRow icon={BookOpen} label="المادة" value={booking.subjects.map(s => s.subject).join(', ')} />
                  <StudentDetailRow icon={GraduationCap} label="الصف" value={booking.grade || 'جامعي'} />
                  <StudentDetailRow icon={Globe} label="الدولة" value={booking.country?.name || 'غير محدد'} />
                  {nextSession && (
                    <>
                      <StudentDetailRow icon={CalendarDays} label="تاريخ الحصة القادمة" value={nextSession.date} />
                      <StudentDetailRow icon={Clock} label="وقت الحصة القادمة" value={nextSession.time} />
                    </>
                  )}
                  <StudentDetailRow icon={statusInfo.icon} label="حالة الاشتراك" value={statusInfo.label} valueClassName={statusInfo.color} />
                  <StudentDetailRow icon={Hourglass} label="الرصيد المتبقي" value={`${remainingHours.toFixed(2)} ساعة`} />
                </CardContent>
                <div className="p-4 pt-0">
                    <div className="flex justify-between text-xs text-white/80 mb-1">
                        <span>التقدم في الباقة</span>
                        <span>{`${(completedDuration / 60).toFixed(2)} / ${totalHours.toFixed(2)} ساعة`}</span>
                    </div>
                    <Progress value={progress} className="w-full h-2" />
                </div>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-16 glass-effect rounded-xl">
          <p className="text-white/70 text-lg">لم يتم إسناد أي طلاب لك بعد.</p>
        </div>
      )}
    </div>
  );
};

export default StudentsContent;