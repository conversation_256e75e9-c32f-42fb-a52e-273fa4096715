import React from 'react';
import { useChat } from '@/contexts/ChatContext';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { File as FileIcon, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';

const InboxContent = () => {
    const { announcements } = useChat();

    return (
        <div className="flex flex-col h-full bg-secondary text-white" dir="rtl">
            <header className="p-4 border-b border-white/10">
                <h2 className="text-lg font-semibold flex items-center gap-2">
                    <Send className="h-5 w-5" />
                    الأخبار والعروض
                </h2>
            </header>
            <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                    {announcements && announcements.length > 0 ? (
                        announcements.map(announcement => (
                            <Card key={announcement.id} className="bg-black/20 border-white/10">
                                <CardHeader>
                                    <CardTitle className="text-primary">{announcement.subject}</CardTitle>
                                    <CardDescription className="text-white/70">
                                        {formatDistanceToNow(new Date(announcement.timestamp), { addSuffix: true, locale: arSA })}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <p className="whitespace-pre-wrap">{announcement.message}</p>
                                </CardContent>
                                {announcement.file && announcement.file.link && (
                                    <CardFooter>
                                        <Button asChild variant="link" className="p-0 h-auto text-white/90 hover:text-primary transition-colors">
                                            <a href={announcement.file.link} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                                                <FileIcon className="h-4 w-4" />
                                                <span>{announcement.file.title || 'عرض المرفق'}</span>
                                            </a>
                                        </Button>
                                    </CardFooter>
                                )}
                            </Card>
                        ))
                    ) : (
                        <div className="text-center text-muted-foreground py-10">
                            <p>لا توجد أخبار أو عروض جديدة في الوقت الحالي.</p>
                        </div>
                    )}
                </div>
            </ScrollArea>
        </div>
    );
};

export default InboxContent;