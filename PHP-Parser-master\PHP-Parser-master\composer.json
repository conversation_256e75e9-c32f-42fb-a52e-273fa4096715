{"name": "nikic/php-parser", "type": "library", "description": "A PHP parser written in PHP", "keywords": ["php", "parser"], "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON><PERSON>"}], "require": {"php": ">=7.4", "ext-tokenizer": "*", "ext-json": "*", "ext-ctype": "*"}, "require-dev": {"phpunit/phpunit": "^9.0", "ircmaxell/php-yacc": "^0.0.7"}, "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "autoload-dev": {"psr-4": {"PhpParser\\": "test/PhpParser/"}}, "bin": ["bin/php-parse"]}