import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useExam } from '@/contexts/ExamContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group.jsx';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Helmet } from 'react-helmet';
import { CheckCircle, XCircle } from 'lucide-react';

const TakeExamPage = () => {
  const { examId } = useParams();
  const navigate = useNavigate();
  const { getExamById, submitExam } = useExam();
  const { user } = useAuth();
  const { toast } = useToast();

  const [exam, setExam] = useState(null);
  const [answers, setAnswers] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [result, setResult] = useState(null);

  useEffect(() => {
    const currentExam = getExamById(examId);
    if (currentExam) {
      setExam(currentExam);
    } else {
      navigate('/student/exams');
    }
  }, [examId, getExamById, navigate]);

  const handleAnswerChange = (questionIndex, answerIndex) => {
    setAnswers({
      ...answers,
      [questionIndex]: answerIndex,
    });
  };

  const handleSubmit = () => {
    if (Object.keys(answers).length !== exam.questions.length) {
      toast({ title: "الرجاء إكمال الاختبار", description: "يجب الإجابة على جميع الأسئلة قبل التسليم.", variant: "destructive" });
      return;
    }

    let score = 0;
    let totalPoints = 0;
    exam.questions.forEach((q, index) => {
      totalPoints += q.points;
      if (answers[index] === q.correctAnswerIndex) {
        score += q.points;
      }
    });

    const submissionResult = submitExam({
      examId,
      studentEmail: user.email,
      answers,
      score,
      totalPoints,
    });
    
    setResult(submissionResult);
    setSubmitted(true);
    toast({ title: "✅ تم تسليم الاختبار بنجاح!", description: `نتيجتك هي ${score} من ${totalPoints}` });
  };

  if (!exam) return <div className="gradient-bg min-h-screen flex items-center justify-center text-white">جاري تحميل الاختبار...</div>;

  if (submitted) {
    return (
      <div className="gradient-bg min-h-screen flex items-center justify-center p-4">
        <div className="glass-effect rounded-xl p-8 w-full max-w-3xl text-white text-center">
          <h1 className="text-3xl font-bold mb-4">نتيجة اختبار: {exam.title}</h1>
          <p className="text-5xl font-bold my-8 text-primary">{result.score} <span className="text-2xl text-white/70">/ {result.totalPoints}</span></p>
          <div className="space-y-6 text-right">
            {exam.questions.map((q, qIndex) => (
              <div key={qIndex} className={`p-4 rounded-lg ${answers[qIndex] === q.correctAnswerIndex ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
                <p className="font-bold mb-2">{qIndex + 1}. {q.questionText}</p>
                 {q.image && <img src={q.image} alt="صورة السؤال" className="my-2 rounded-md max-w-sm mx-auto"/>}
                <p>إجابتك: {q.options[answers[qIndex]].image && <img src={q.options[answers[qIndex]].image} alt="صورة الإجابة" className="inline-block h-20 mr-2 rounded-md"/>} <span className="font-semibold">{q.options[answers[qIndex]].text}</span> {answers[qIndex] === q.correctAnswerIndex ? <CheckCircle className="inline h-5 w-5 text-green-400" /> : <XCircle className="inline h-5 w-5 text-red-400" />}</p>
                {answers[qIndex] !== q.correctAnswerIndex && <p>الإجابة الصحيحة: {q.options[q.correctAnswerIndex].image && <img src={q.options[q.correctAnswerIndex].image} alt="صورة الإجابة الصحيحة" className="inline-block h-20 mr-2 rounded-md"/>} <span className="font-semibold">{q.options[q.correctAnswerIndex].text}</span></p>}
              </div>
            ))}
          </div>
          <Button onClick={() => navigate('/student/exams')} className="mt-8">العودة إلى قائمة الاختبارات</Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>أداء اختبار: {exam.title}</title>
      </Helmet>
      <div className="gradient-bg min-h-screen p-8">
        <div className="glass-effect rounded-xl p-8 max-w-4xl mx-auto text-white">
          <h1 className="text-3xl font-bold mb-2">{exam.title}</h1>
          <p className="text-white/70 mb-8">أجب على الأسئلة التالية. بالتوفيق!</p>
          
          <div className="space-y-8">
            {exam.questions.map((q, qIndex) => (
              <div key={qIndex} className="bg-secondary/50 p-6 rounded-lg">
                <p className="font-bold text-lg mb-4">{qIndex + 1}. {q.questionText} <span className="text-sm text-white/60">({q.points} نقاط)</span></p>
                {q.image && <img src={q.image} alt="صورة السؤال" className="my-4 rounded-md max-w-md"/>}
                <RadioGroup onValueChange={(value) => handleAnswerChange(qIndex, parseInt(value))}>
                  <div className="space-y-4">
                    {q.options.map((option, oIndex) => (
                      <div key={oIndex} className="flex items-center space-x-2 space-x-reverse bg-white/10 p-3 rounded-md">
                        <RadioGroupItem value={oIndex.toString()} id={`q${qIndex}o${oIndex}`} />
                        <Label htmlFor={`q${qIndex}o${oIndex}`} className="text-base flex items-center gap-4">
                          {option.image && <img src={option.image} alt={`خيار ${oIndex + 1}`} className="h-24 w-24 object-cover rounded-md" />}
                          <span>{option.text}</span>
                        </Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
            ))}
          </div>

          <div className="mt-8 flex justify-end">
            <Button onClick={handleSubmit} className="bg-primary text-primary-foreground hover:bg-primary/90 text-lg px-8 py-6">
              تسليم الاختبار
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default TakeExamPage;