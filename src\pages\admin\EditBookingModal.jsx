import React, { useState, useEffect } from 'react';
import { useBooking } from '@/contexts/BookingContext';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, UserX, AlertTriangle, Trash2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { arSA } from 'date-fns/locale';

const EditBookingModal = ({ booking, isOpen, onClose }) => {
  const { updateBooking, handlePostponementRequest, handleTeacherChangeRequest, handleSubscriptionDeletion, handleNewLinkRequest, getAvailableSlots } = useBooking();
  const { users } = useAuth();
  const { toast } = useToast();
  
  const [editedBooking, setEditedBooking] = useState(booking);
  const [newTeacherEmail, setNewTeacherEmail] = useState('');
  const [postponeDate, setPostponeDate] = useState(null);
  const [postponeTime, setPostponeTime] = useState('');
  const [availableTimes, setAvailableTimes] = useState([]);

  useEffect(() => {
    if (booking) {
      setEditedBooking(booking);
      setNewTeacherEmail('');
      setPostponeDate(null);
      setPostponeTime('');
    }
  }, [booking]);

  useEffect(() => {
    if (postponeDate && booking) {
      const slots = getAvailableSlots(booking.teacherEmail, postponeDate);
      setAvailableTimes(slots);
      setPostponeTime('');
    }
  }, [postponeDate, booking, getAvailableSlots]);

  const teachers = users.filter(u => u.userType === 'teacher');
  
  const handleSave = () => {
    updateBooking(booking.id, editedBooking);
    toast({ title: 'تم تحديث الحجز بنجاح' });
    onClose();
  };
  
  if (!booking) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-secondary border-border text-white">
        <DialogHeader>
          <DialogTitle>إدارة الطلب</DialogTitle>
          <DialogDescription>
            مراجعة ومعالجة الطلب الخاص بحجز الطالب {booking.studentName}.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 space-y-4 max-h-[60vh] overflow-y-auto pr-2">
            {booking.linkProblemRequest?.status === 'pending' && (
              <div className="p-4 bg-orange-900/50 rounded-lg">
                <h4 className="font-bold text-orange-300 flex items-center gap-2"><AlertTriangle/>طلب رابط جديد</h4>
                <p className="text-sm mt-2">أبلغ المستخدم عن مشكلة في رابط الحصة. هل توافق على إنشاء رابط جديد؟</p>
                <div className="flex gap-2 mt-3">
                  <Button size="sm" onClick={() => { handleNewLinkRequest(booking.id, true); onClose(); }}>موافقة وإنشاء رابط</Button>
                  <Button size="sm" variant="destructive" onClick={() => { handleNewLinkRequest(booking.id, false); onClose(); }}>رفض</Button>
                </div>
              </div>
            )}
            
            {booking.postponementRequest?.status === 'pending' && (
                <div className="p-4 bg-yellow-900/50 rounded-lg space-y-3">
                    <h4 className="font-bold text-yellow-300 flex items-center gap-2"><Clock/>طلب تأجيل</h4>
                    <p className="text-sm">سبب الطلب: {booking.postponementRequest.reason}</p>
                    <div>
                        <Label>حدد الموعد الجديد للموافقة</Label>
                        <div className="grid grid-cols-2 gap-2 mt-1">
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button variant="outline">{postponeDate ? format(postponeDate, 'PPP', { locale: arSA }) : "اختر التاريخ"}</Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0">
                                    <Calendar mode="single" selected={postponeDate} onSelect={setPostponeDate} initialFocus />
                                </PopoverContent>
                            </Popover>
                            <Select value={postponeTime} onValueChange={setPostponeTime} disabled={!postponeDate}>
                                <SelectTrigger><SelectValue placeholder="اختر الوقت" /></SelectTrigger>
                                <SelectContent>
                                    {availableTimes.map(time => <SelectItem key={time} value={time}>{time}</SelectItem>)}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="flex gap-2 mt-3">
                       <Button size="sm" onClick={() => { handlePostponementRequest(booking.id, true, format(postponeDate, 'yyyy-MM-dd'), postponeTime); onClose(); }} disabled={!postponeDate || !postponeTime}>موافقة وتأجيل</Button>
                       <Button size="sm" variant="destructive" onClick={() => { handlePostponementRequest(booking.id, false); onClose(); }}>رفض</Button>
                    </div>
                </div>
            )}

            {booking.teacherChangeRequest?.status === 'pending' && (
                <div className="p-4 bg-cyan-900/50 rounded-lg space-y-3">
                    <h4 className="font-bold text-cyan-300 flex items-center gap-2"><UserX />طلب تغيير معلم</h4>
                    <p className="text-sm">سبب الطلب: {booking.teacherChangeRequest.reason}</p>
                    <div>
                        <Label>اختر المعلم الجديد للموافقة</Label>
                        <Select onValueChange={setNewTeacherEmail}>
                          <SelectTrigger><SelectValue placeholder="اختر المعلم الجديد" /></SelectTrigger>
                          <SelectContent>
                            {teachers.filter(t => t.email !== booking.teacherEmail).map(teacher => (
                              <SelectItem key={teacher.id} value={teacher.email}>{teacher.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                    </div>
                    <div className="flex gap-2 mt-3">
                       <Button size="sm" onClick={() => { handleTeacherChangeRequest(booking.id, true, newTeacherEmail); onClose(); }} disabled={!newTeacherEmail}>موافقة وتغيير</Button>
                       <Button size="sm" variant="destructive" onClick={() => { handleTeacherChangeRequest(booking.id, false); onClose(); }}>رفض</Button>
                    </div>
                </div>
            )}
            
            {booking.deletionRequest?.status === 'pending' && (
                <div className="p-4 bg-red-900/50 rounded-lg">
                    <h4 className="font-bold text-red-300 flex items-center gap-2"><Trash2 />طلب إلغاء اشتراك</h4>
                     <p className="text-sm mt-2">سبب الطلب: {booking.deletionRequest.reason}</p>
                    <div className="flex gap-2 mt-3">
                       <Button size="sm" onClick={() => { handleSubscriptionDeletion(booking.id, true); onClose(); }}>الموافقة على الإلغاء</Button>
                       <Button size="sm" variant="destructive" onClick={() => { handleSubscriptionDeletion(booking.id, false); onClose(); }}>رفض</Button>
                    </div>
                </div>
            )}

        </div>
        <DialogFooter>
          <DialogClose asChild><Button variant="outline">إغلاق</Button></DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBookingModal;