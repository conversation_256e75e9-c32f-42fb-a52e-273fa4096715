import jsPDF from 'jspdf';
import 'jspdf-autotable';
import Amir<PERSON><PERSON>ont from '@/assets/fonts/Amiri-Regular.js';

export const generatePdf = (title, head, body, logoSrc) => {
  return new Promise((resolve, reject) => {
    try {
      const doc = new jsPDF();

      doc.addFileToVFS('Amiri-Regular.ttf', AmiriFont);
      doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
      doc.setFont('Amiri');

      const addHeader = () => {
        if (logoSrc) {
          const img = new Image();
          img.src = logoSrc;
          img.onload = () => {
            const imgWidth = 30;
            const imgHeight = (img.height * imgWidth) / img.width;
            doc.addImage(img, 'PNG', 15, 10, imgWidth, imgHeight);
            addTitle();
            addTable();
            resolve(doc);
          };
          img.onerror = () => {
            console.warn("Could not load logo for PDF. Proceeding without it.");
            addTitle();
            addTable();
            resolve(doc);
          };
        } else {
          addTitle();
          addTable();
          resolve(doc);
        }
      };

      const addTitle = () => {
        doc.setFontSize(22);
        doc.text(title, doc.internal.pageSize.getWidth() / 2, 20, { align: 'center' });
      };

      const addTable = () => {
        doc.autoTable({
          startY: 50,
          head: head,
          body: body,
          theme: 'grid',
          styles: {
            font: 'Amiri',
            halign: 'center',
            cellPadding: 2,
          },
          headStyles: {
            fillColor: [41, 128, 185],
            textColor: 255,
            fontStyle: 'bold',
          },
          didParseCell: function (data) {
            if (data.section === 'body' || data.section === 'head') {
              if(Array.isArray(data.cell.text)) {
                data.cell.text = data.cell.text.map(str => String(str).split(' ').reverse().join(' '));
              }
            }
          },
        });
      };

      addHeader();

    } catch (error) {
      console.error("Error generating PDF:", error);
      reject(error);
    }
  });
};

export default generatePdf;