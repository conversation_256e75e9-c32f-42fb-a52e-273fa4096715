
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { QrCode, KeyRound, Loader2, VideoOff } from 'lucide-react';
import QrScanner from 'qr-scanner';

const QrReaderModal = ({ onScan, onError, onClose }) => {
    const videoRef = useRef(null);
    const scannerRef = useRef(null);

    const stopScanner = useCallback(() => {
        if (scannerRef.current) {
            scannerRef.current.stop();
            scannerRef.current.destroy();
            scannerRef.current = null;
        }
    }, []);

    useEffect(() => {
        const videoElem = videoRef.current;
        if (!videoElem) return;

        const qrScanner = new QrScanner(
            videoElem,
            result => {
                onScan(result.data);
                stopScanner();
                onClose();
            },
            {
                onDecodeError: error => {
                    console.error("QR Decode Error:", error);
                },
                highlightScanRegion: true,
                highlightCodeOutline: true,
            }
        );

        scannerRef.current = qrScanner;

        qrScanner.start().catch(err => {
            console.error("QR Scanner Start Error:", err);
            if (err.name === 'NotAllowedError') {
                onError('الرجاء منح صلاحية الوصول للكاميرا.');
            } else {
                onError('لا يمكن تشغيل الكاميرا. تأكد من عدم استخدامها في تطبيق آخر.');
            }
            stopScanner();
            onClose();
        });

        return () => {
           stopScanner();
        };
    }, [onScan, onError, onClose, stopScanner]);

    return (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50" onClick={onClose}>
            <div className="bg-secondary p-4 rounded-lg relative w-full max-w-lg" onClick={(e) => e.stopPropagation()}>
                 <div className="relative w-full pb-[75%] bg-black rounded-md overflow-hidden">
                    <video ref={videoRef} className="absolute top-0 left-0 w-full h-full object-cover"></video>
                    <div className="absolute inset-0 border-4 border-primary/50 rounded-md animate-pulse"></div>
                 </div>
                <div className="absolute bottom-4 left-4 right-4 flex justify-center">
                    <Button variant="destructive" onClick={onClose}><VideoOff className="h-4 w-4 ml-2" /> إغلاق الكاميرا</Button>
                </div>
            </div>
        </div>
    );
};


const AccessCodeLogin = () => {
  const [accessCode, setAccessCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isQrReaderOpen, setIsQrReaderOpen] = useState(false);
  const { loginWithAccessCode } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (code) => {
    if (!code) return;
    setIsLoading(true);
    const result = await loginWithAccessCode(code);
    setIsLoading(false);
    if (result.success) {
      toast({ title: `✅ مرحباً ${result.user.name}`, description: 'تم تسجيل الدخول بنجاح.' });
      navigate(`/${result.user.userType}`);
    } else {
      toast({ title: '❌ فشل الدخول', description: result.message, variant: 'destructive' });
    }
  };

  const handleScanSuccess = useCallback((data) => {
      setAccessCode(data);
      handleSubmit(data);
  }, [handleSubmit]);

  const handleScanError = useCallback((errorMessage) => {
      toast({ title: 'خطأ في المسح', description: errorMessage, variant: 'destructive' });
  }, [toast]);


  return (
    <div className="min-h-screen flex items-center justify-center gradient-bg p-4">
      <Card className="w-full max-w-md glass-effect text-white border-white/20">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-gradient">دخول الطالب</CardTitle>
          <CardDescription className="text-white/70">استخدم كود الوصول الخاص بك للانضمام للحصص</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="accessCode" className="text-white/80">كود الوصول</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="accessCode"
                  type="text"
                  placeholder="أدخل كود الوصول هنا"
                  value={accessCode}
                  onChange={(e) => setAccessCode(e.target.value)}
                  className="bg-white/10 border-border"
                  dir="ltr"
                />
                 <Button variant="ghost" size="icon" onClick={() => setIsQrReaderOpen(true)}>
                    <QrCode className="h-6 w-6 text-primary"/>
                 </Button>
              </div>
            </div>
          </div>
          <Button onClick={() => handleSubmit(accessCode)} disabled={isLoading || !accessCode} className="w-full mt-6 bg-primary text-primary-foreground hover:bg-primary/90">
            {isLoading ? <Loader2 className="h-4 w-4 animate-spin ml-2" /> : <KeyRound className="h-4 w-4 ml-2" />}
            دخول
          </Button>
          <div className="mt-4 text-center text-sm">
            <Link to="/login" className="text-primary hover:underline">
              أو قم بتسجيل الدخول بكلمة المرور
            </Link>
          </div>
        </CardContent>
      </Card>
      {isQrReaderOpen && (
          <QrReaderModal 
              onScan={handleScanSuccess}
              onError={handleScanError}
              onClose={() => setIsQrReaderOpen(false)}
          />
      )}
    </div>
  );
};

export default AccessCodeLogin;