import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { Calendar as CalendarIcon, Save, Clock, Users } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';

const daysOfWeek = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

const generateTimeSlots = () => {
    const slots = [];
    for (let i = 6; i <= 23; i++) {
        slots.push(`${String(i).padStart(2, '0')}:00`);
        slots.push(`${String(i).padStart(2, '0')}:30`);
    }
    return slots;
};
const timeSlots = generateTimeSlots();

const DaySchedule = ({ day, schedule, onScheduleChange }) => {
    const daySchedule = schedule[day] || { startTime: '', endTime: '' };

    return (
        <div className="flex-1 min-w-[200px] bg-white/5 p-4 rounded-lg space-y-3">
            <h4 className="text-center font-bold text-white border-b border-white/20 pb-2">{day}</h4>
            <div className="space-y-2">
                <div>
                    <Label htmlFor={`${day}-start`} className="text-sm text-white/70">من الساعة</Label>
                    <Select value={daySchedule.startTime} onValueChange={val => onScheduleChange(day, 'startTime', val)}>
                        <SelectTrigger id={`${day}-start`} className="bg-white/10"><SelectValue placeholder="غير متاح" /></SelectTrigger>
                        <SelectContent className="bg-secondary text-white max-h-60">{timeSlots.map(t => <SelectItem key={t} value={t}>{t}</SelectItem>)}</SelectContent>
                    </Select>
                </div>
                <div>
                    <Label htmlFor={`${day}-end`} className="text-sm text-white/70">إلى الساعة</Label>
                    <Select value={daySchedule.endTime} onValueChange={val => onScheduleChange(day, 'endTime', val)} disabled={!daySchedule.startTime}>
                        <SelectTrigger id={`${day}-end`} className="bg-white/10"><SelectValue placeholder="غير متاح" /></SelectTrigger>
                        <SelectContent className="bg-secondary text-white max-h-60">{timeSlots.map(t => <SelectItem key={t} value={t} disabled={t <= daySchedule.startTime}>{t}</SelectItem>)}</SelectContent>
                    </Select>
                </div>
            </div>
        </div>
    );
};

const TeacherSchedulesContent = () => {
  const { toast } = useToast();
  const { teachers } = useAuth();
  const { availability, updateTeacherAvailability } = useBooking();
  
  const [selectedTeachers, setSelectedTeachers] = useState([]);
  const [localAvailability, setLocalAvailability] = useState({});
  const [isGroupEdit, setIsGroupEdit] = useState(false);

  useEffect(() => {
    if (selectedTeachers.length === 1 && !isGroupEdit) {
      setLocalAvailability(availability[selectedTeachers[0]] || {});
    } else {
      setLocalAvailability({});
    }
  }, [selectedTeachers, availability, isGroupEdit]);

  const handleTeacherSelection = (email) => {
    setSelectedTeachers(prev => 
      prev.includes(email) ? prev.filter(t => t !== email) : [...prev, email]
    );
  };

  const handleScheduleChange = (day, field, value) => {
    setLocalAvailability(prev => {
        const newDaySchedule = { ...prev[day], [field]: value };
        if (field === 'startTime' && !value) newDaySchedule.endTime = '';
        if (field === 'startTime' && value && newDaySchedule.endTime && value >= newDaySchedule.endTime) {
            newDaySchedule.endTime = '';
        }
        return { ...prev, [day]: newDaySchedule };
    });
  };

  const handleSave = () => {
    if (selectedTeachers.length === 0) {
      toast({ title: "خطأ", description: "الرجاء اختيار معلم واحد على الأقل.", variant: "destructive" });
      return;
    }
    selectedTeachers.forEach(teacherEmail => {
      const finalAvailability = isGroupEdit ? localAvailability : { ...availability[teacherEmail], ...localAvailability };
      updateTeacherAvailability(teacherEmail, finalAvailability);
    });
    toast({
      title: "✅ تم حفظ التغييرات",
      description: `تم تحديث جداول ${selectedTeachers.length} معلم بنجاح.`,
    });
    if(isGroupEdit) {
        setIsGroupEdit(false);
        setSelectedTeachers([]);
    }
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3"><Users className="h-6 w-6" />إدارة جداول المعلمين</h3>
        
        <div className="bg-white/5 p-4 rounded-lg mb-6">
            <Label className="text-lg font-semibold text-white mb-3 block">1. اختر المعلمين</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {teachers.map(teacher => (
                    <div key={teacher.email} className="flex items-center space-x-2 space-x-reverse bg-white/10 p-2 rounded-md">
                        <Checkbox 
                            id={`teacher-${teacher.email}`} 
                            checked={selectedTeachers.includes(teacher.email)}
                            onCheckedChange={() => handleTeacherSelection(teacher.email)}
                        />
                        <label htmlFor={`teacher-${teacher.email}`} className="text-sm font-medium text-white cursor-pointer">{teacher.name}</label>
                    </div>
                ))}
            </div>
        </div>

        {selectedTeachers.length > 0 && (
            <div className="bg-white/5 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-4">
                    <Label className="text-lg font-semibold text-white">2. تعديل الجدول</Label>
                    {selectedTeachers.length > 1 && (
                        <div className="flex items-center space-x-2 space-x-reverse">
                            <Switch id="group-edit-switch" checked={isGroupEdit} onCheckedChange={setIsGroupEdit} />
                            <Label htmlFor="group-edit-switch">تطبيق على المجموعة</Label>
                        </div>
                    )}
                </div>
                <p className="text-white/70 mb-4">
                    {isGroupEdit ? "سيتم تطبيق هذا الجدول على جميع المعلمين المختارين، مع مسح إعداداتهم السابقة." : "سيتم تعديل جدول المعلم المختار. إذا اخترت أكثر من معلم، سيتم تطبيق التغييرات على جدول كل منهم على حدة."}
                </p>
                <div className="flex gap-4 overflow-x-auto pb-4">
                    {daysOfWeek.map(day => (
                        <DaySchedule key={day} day={day} schedule={localAvailability} onScheduleChange={handleScheduleChange} />
                    ))}
                </div>
            </div>
        )}
        
        <div className="flex justify-end mt-6">
          <Button onClick={handleSave} size="lg" className="bg-primary hover:bg-primary/90" disabled={selectedTeachers.length === 0}>
            <Save className="h-5 w-5 ml-2" />
            حفظ التغييرات للجداول المحددة
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TeacherSchedulesContent;