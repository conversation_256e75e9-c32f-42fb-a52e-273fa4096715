import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Users, Clock, Star, CheckSquare, PlayCircle, BarChart } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useBooking } from '@/contexts/BookingContext';
import { useExam } from '@/contexts/ExamContext';
import { format, parseISO } from 'date-fns';
import { arSA } from 'date-fns/locale';
import ActiveSessionsList from '@/components/ActiveSessionsList';

const DashboardContent = ({ setActiveTab }) => {
  const { user: teacher, getStudentsForTeacher } = useAuth();
  const { bookings } = useBooking();
  const { exams } = useExam();

  const myStudents = getStudentsForTeacher(teacher.email, bookings);
  const myBookings = bookings.filter(b => b.teacherEmail === teacher.email);
  
  const totalCompletedHours = myBookings.reduce((acc, b) => acc + (b.completedDuration || 0), 0) / 60;

  const myExamsCount = exams.filter(e => e.teacherEmail === teacher.email).length;

  const getStatusClass = (status) => {
    switch (status) {
      case 'approved': return 'bg-green-500/20 text-green-300';
      case 'pending': return 'bg-yellow-500/20 text-yellow-300';
      default: return 'bg-gray-500/20 text-gray-300';
    }
  };

  const upcomingSessions = myBookings
    .flatMap(b => b.sessions.map(s => ({ ...s, studentName: b.studentName, subject: b.subjects.map(sub => sub.subject).join(', ') })))
    .filter(s => s.status === 'scheduled' && new Date(s.date) >= new Date())
    .sort((a, b) => new Date(a.date) - new Date(b.date))
    .slice(0, 3);

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-blue-500 p-3 rounded-full"><Users className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الطلاب النشطون</p><p className="text-2xl font-bold text-white">{myStudents.length}</p></div></div></div>
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-green-500 p-3 rounded-full"><Clock className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الحصص القادمة</p><p className="text-2xl font-bold text-white">{upcomingSessions.length}</p></div></div></div>
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-indigo-500 p-3 rounded-full"><BarChart className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الساعات المكتملة</p><p className="text-2xl font-bold text-white">{totalCompletedHours.toFixed(1)}</p></div></div></div>
        <div className="stats-card rounded-xl p-6"><div className="flex items-center gap-4"><div className="bg-purple-500 p-3 rounded-full"><CheckSquare className="h-6 w-6 text-white" /></div><div><p className="text-white/70 text-sm">الاختبارات المنشأة</p><p className="text-2xl font-bold text-white">{myExamsCount}</p></div></div></div>
      </div>
      
       <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center gap-2 mb-4">
            <PlayCircle className="h-6 w-6 text-green-400 animate-pulse" />
            <h3 className="text-xl font-bold text-white">الحصص الجارية الآن</h3>
          </div>
          <ActiveSessionsList />
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6"><h3 className="text-xl font-bold text-white">الحصص القادمة</h3><Button size="sm" className="bg-blue-600 hover:bg-blue-700" onClick={() => setActiveTab('upcoming-sessions')}>إدارة الحصص</Button></div>
          <div className="space-y-4">
            {upcomingSessions.length > 0 ? upcomingSessions.map((session) => (
              <div key={session.sessionId} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-white">{session.studentName}</p>
                    <p className="text-white/70 text-sm">{session.subject}</p>
                    <p className="text-white/60 text-sm">{format(parseISO(session.date), 'eeee, d MMMM', { locale: arSA })} - {session.time}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs ${getStatusClass('approved')}`}>
                    مؤكدة
                  </span>
                </div>
              </div>
            )) : <p className="text-center text-white/70 py-4">لا توجد حصص قادمة.</p>}
          </div>
        </div>
        <div className="glass-effect rounded-xl p-6">
          <div className="flex items-center justify-between mb-6"><h3 className="text-xl font-bold text-white">الطلاب</h3><Button size="sm" variant="outline" className="border-white/30 text-white hover:bg-white/10" onClick={() => setActiveTab('students')}>عرض الكل</Button></div>
          <div className="space-y-4">
            {myStudents.slice(0, 3).map((student) => (
              <div key={student.id} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-white">{student.name}</p>
                    <p className="text-white/70 text-sm">{student.email}</p>
                  </div>
                  <p className="text-white/60 text-sm">مسجل منذ {format(parseISO(student.registrationDate), 'MMMM yyyy', { locale: arSA })}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardContent;