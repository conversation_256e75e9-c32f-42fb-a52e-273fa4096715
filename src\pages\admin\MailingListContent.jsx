import React, { useState, useEffect } from 'react';
import { useSettings } from '@/contexts/SettingsContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Mail, Send, Trash2, Archive, Inbox } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

const MailingListContent = () => {
  const { settings, updateInquiry } = useSettings();
  const { toast } = useToast();
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [reply, setReply] = useState('');
  const [emails, setEmails] = useState([]);

  useEffect(() => {
    // In a real app, you'd fetch this from a backend.
    // Here, we'll use the inquiries from the contact form as a simulation.
    const newsletterSubscribers = settings.inquiries?.filter(inq => inq.type === 'newsletter') || [];
    const contactInquiries = settings.inquiries?.filter(inq => inq.type !== 'newsletter') || [];
    
    // We will display contact form submissions here as "emails"
    setEmails(contactInquiries);
  }, [settings.inquiries]);

  const handleReply = () => {
    if (!reply) {
      toast({ title: 'الرد فارغ', variant: 'destructive' });
      return;
    }
    // This is a simulation of sending an email.
    toast({ title: '✅ تم إرسال الرد (محاكاة)', description: `تم إرسال الرد إلى ${selectedEmail.email}` });
    
    // Update the status of the inquiry in our context
    updateInquiry({ ...selectedEmail, status: 'تم الرد' });
    
    setSelectedEmail(null);
    setReply('');
  };

  const handleStatusChange = (email, status) => {
    updateInquiry({ ...email, status });
    toast({ title: `✅ تم تغيير الحالة إلى "${status}"` });
  };

  const getStatusClass = (status) => {
    switch (status) {
      case 'جديد': return 'bg-blue-500/20 text-blue-300';
      case 'تم الرد': return 'bg-green-500/20 text-green-300';
      case 'مؤرشف': return 'bg-gray-500/20 text-gray-300';
      default: return 'bg-white/10 text-white/70';
    }
  };

  return (
    <div className="glass-effect rounded-xl p-6">
      <div className="flex items-center gap-4 mb-6">
        <Inbox className="h-8 w-8 text-primary" />
        <h3 className="text-2xl font-bold text-white">صندوق البريد الوارد</h3>
      </div>
      <p className="text-white/70 mb-6">
        هنا تظهر الرسائل المرسلة عبر نموذج "اتصل بنا" في الموقع.
      </p>
      <div className="space-y-4">
        {emails && emails.length > 0 ? (
          emails.map(email => (
            <div key={email.id} className="bg-secondary/50 rounded-lg p-4 flex items-center justify-between hover:bg-secondary/70 transition-colors duration-200 cursor-pointer" onClick={() => setSelectedEmail(email)}>
              <div>
                <p className="font-bold text-white">{email.subject}</p>
                <p className="text-white/70 text-sm">{email.name} ({email.email})</p>
                <p className="text-white/60 text-xs mt-1">
                  {new Date(email.date).toLocaleString('ar-SA')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-3 py-1 rounded-full text-xs ${getStatusClass(email.status)}`}>{email.status}</span>
                <Button size="icon" variant="ghost" className="text-yellow-400 hover:text-yellow-300" onClick={(e) => { e.stopPropagation(); handleStatusChange(email, 'مؤرشف'); }}><Archive className="h-4 w-4" /></Button>
                <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={(e) => { e.stopPropagation(); handleStatusChange(email, 'محذوف'); }}><Trash2 className="h-4 w-4" /></Button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-white/70 py-12">
            <Inbox className="h-16 w-16 mx-auto text-white/30 mb-4" />
            <p className="text-xl">صندوق البريد فارغ</p>
            <p>لم تصل أي رسائل جديدة بعد.</p>
          </div>
        )}
      </div>

      {selectedEmail && (
        <Dialog open={!!selectedEmail} onOpenChange={() => setSelectedEmail(null)}>
          <DialogContent className="bg-secondary border-border text-white max-w-2xl">
            <DialogHeader>
              <DialogTitle>الرد على: {selectedEmail.subject}</DialogTitle>
              <DialogDescription>من: {selectedEmail.name} ({selectedEmail.email})</DialogDescription>
            </DialogHeader>
            <div className="py-4 space-y-4">
              <div className="bg-white/5 p-4 rounded-md max-h-60 overflow-y-auto">
                <p className="text-white/80 whitespace-pre-wrap">{selectedEmail.message}</p>
              </div>
              <div>
                <Textarea value={reply} onChange={(e) => setReply(e.target.value)} placeholder="اكتب ردك هنا..." rows={5} className="bg-white/10 border-border text-white" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setSelectedEmail(null)}>إغلاق</Button>
              <Button onClick={handleReply} className="bg-primary hover:bg-primary/90"><Send className="h-4 w-4 ml-2" />إرسال الرد</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default MailingListContent;