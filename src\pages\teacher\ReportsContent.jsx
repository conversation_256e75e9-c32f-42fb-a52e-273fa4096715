import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { supabase } from '@/lib/customSupabaseClient';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';
import { Bar<PERSON>hart2, Filter, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSocialShare } from '@/lib/socialShare.jsx';

const ReportsContent = () => {
    const { user: teacher, users } = useAuth();
    const { toast } = useToast();
    const { share } = useSocialShare();
    const [logs, setLogs] = useState([]);
    const [loading, setLoading] = useState(true);
    const [studentFilter, setStudentFilter] = useState('all');

    const students = useMemo(() => users.filter(u => u.userType === 'student'), [users]);

    const fetchLogs = useCallback(async () => {
        setLoading(true);
        const { data, error } = await supabase
            .from('session_logs')
            .select('*')
            .eq('teacher_email', teacher.email);
            
        if (error) {
            toast({ title: 'خطأ', description: 'فشل في جلب سجلات الحصص.', variant: 'destructive' });
        } else {
            setLogs(data);
        }
        setLoading(false);
    }, [toast, teacher.email]);

    useEffect(() => {
        fetchLogs();
    }, [fetchLogs]);

    const filteredLogs = useMemo(() => {
        return logs.filter(log => studentFilter === 'all' || log.student_email === studentFilter);
    }, [logs, studentFilter]);

    const getStudentName = (email) => students.find(s => s.email === email)?.name || email;

    const handleShare = () => {
        const reportText = `تقرير الحصص من أكاديمية الخليج:\n\n${filteredLogs.map(log => 
            `- الطالب: ${getStudentName(log.student_email)}, المدة: ${log.actual_session_duration_minutes || 0} دقيقة`
        ).join('\n')}`;
        share({title: 'تقرير الحصص', text: reportText});
    };

    return (
        <div className="space-y-8">
            <div className="glass-effect rounded-xl p-6">
                <div className="flex flex-wrap items-center justify-between mb-6 gap-4">
                    <h3 className="text-2xl font-bold text-white flex items-center gap-3"><BarChart2 /> تقارير الحصص</h3>
                    <div className="flex gap-2">
                        <Button onClick={handleShare} variant="outline" className="border-border text-white hover:bg-secondary/50"><Share2 className="h-4 w-4 ml-2" /> مشاركة</Button>
                    </div>
                </div>

                <div className="bg-secondary/50 rounded-lg p-4 mb-6 flex gap-4 items-center">
                    <Filter className="h-5 w-5 text-primary" />
                    <div className="flex-1">
                        <Select value={studentFilter} onValueChange={setStudentFilter}>
                            <SelectTrigger><SelectValue placeholder="فلترة حسب الطالب..." /></SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">كل الطلاب</SelectItem>
                                {students.map(s => <SelectItem key={s.email} value={s.email}>{s.name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="overflow-x-auto">
                    <table className="w-full text-right whitespace-nowrap">
                        <thead>
                            <tr className="border-b border-white/20">
                                <th className="p-3 text-white font-medium">الطالب</th>
                                <th className="p-3 text-white font-medium">المادة/الصف/الدولة</th>
                                <th className="p-3 text-white font-medium">دخول الطالب</th>
                                <th className="p-3 text-white font-medium">خروج الطالب</th>
                                <th className="p-3 text-white font-medium">دخول المعلم</th>
                                <th className="p-3 text-white font-medium">خروج المعلم</th>
                                <th className="p-3 text-white font-medium">المدة الفعلية (د)</th>
                                <th className="p-3 text-white font-medium">ساعات الاشتراك</th>
                                <th className="p-3 text-white font-medium">المنجز</th>
                                <th className="p-3 text-white font-medium">المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {loading ? (
                                <tr><td colSpan="10" className="text-center p-8 text-white/70">جاري تحميل التقارير...</td></tr>
                            ) : filteredLogs.length === 0 ? (
                                <tr><td colSpan="10" className="text-center p-8 text-white/70">لا توجد بيانات لعرضها.</td></tr>
                            ) : (
                                filteredLogs.map(log => (
                                    <tr key={log.id} className="border-b border-white/10 text-sm hover:bg-secondary/30">
                                        <td className="p-3">{getStudentName(log.student_email)}</td>
                                        <td className="p-3">{log.subject || 'N/A'} / {log.grade || 'N/A'} / {log.country || 'N/A'}</td>
                                        <td className="p-3">{log.student_joined_at ? new Date(log.student_joined_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.student_left_at ? new Date(log.student_left_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.teacher_joined_at ? new Date(log.teacher_joined_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3">{log.teacher_left_at ? new Date(log.teacher_left_at).toLocaleTimeString('ar-SA') : '-'}</td>
                                        <td className="p-3 text-primary font-semibold">{log.actual_session_duration_minutes || 0}</td>
                                        <td className="p-3">{log.student_subscription_total_hours || 0}</td>
                                        <td className="p-3 text-green-400">{log.student_subscription_completed_hours || 0}</td>
                                        <td className="p-3 text-yellow-400">{log.student_subscription_remaining_hours || 0}</td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default ReportsContent;
