import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Edit, Trash2, Search, BookOpen } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const UsersContent = () => {
  const { toast } = useToast();
  const { users: allUsers, updateUser, deleteUser, register } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [originalEmail, setOriginalEmail] = useState(null);
  const [userToDelete, setUserToDelete] = useState(null);

  const specializations = [
    'الرياضيات', 'الفيزياء', 'الكيمياء', 'الأحياء', 'اللغة العربية',
    'اللغة الإنجليزية', 'التاريخ', 'الجغرافيا', 'الحاسوب', 'الاقتصاد', 'المرحلة الابتدائية', 'المرحلة المتوسطة', 'المرحلة الثانوية'
  ];

  const handleOpenModal = (user = null) => {
    if (user) {
      setEditingUser({ ...user });
      setOriginalEmail(user.email);
    } else {
      setEditingUser({ name: '', email: '', password: '', userType: '', specialization: '', otherSpecialization: '', status: 'نشط' });
      setOriginalEmail(null);
    }
    setIsModalOpen(true);
  };
  
  const handleSave = () => {
    if (!editingUser.name || !editingUser.email || !editingUser.userType || (!originalEmail && !editingUser.password)) {
        toast({ title: "بيانات غير مكتملة", description: "الاسم، البريد، كلمة المرور، ونوع المستخدم حقول إلزامية.", variant: "destructive" });
        return;
    }
    
    const finalUserData = { ...editingUser };
    if (finalUserData.specialization === 'other' && finalUserData.otherSpecialization) {
        finalUserData.specialization = finalUserData.otherSpecialization;
    }
    delete finalUserData.otherSpecialization;

    if (originalEmail) { // Editing existing user
        const success = updateUser(originalEmail, finalUserData);
        if (success) {
            toast({ title: "✅ تم تحديث المستخدم بنجاح" });
        } else {
            toast({ title: "❌ البريد الإلكتروني الجديد مستخدم بالفعل", variant: "destructive" });
            return; 
        }
    } else { // Adding new user
        const success = register(finalUserData, true); // true to indicate admin registration
        if (success) {
            toast({ title: "✅ تم إضافة المستخدم بنجاح" });
        } else {
            toast({ title: "❌ البريد الإلكتروني مستخدم بالفعل", variant: "destructive" });
            return;
        }
    }
    setIsModalOpen(false);
    setEditingUser(null);
    setOriginalEmail(null);
  };

  const handleDelete = () => {
    if (userToDelete) {
        deleteUser(userToDelete.email);
        toast({ title: '🗑️ تم حذف المستخدم بنجاح' });
        setUserToDelete(null);
    }
  };

  const getStatusClass = (status) => {
    return status === 'نشط' ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300';
  };
  
  const getTypeInArabic = (type) => {
    switch (type) {
        case 'student': return 'طالب';
        case 'teacher': return 'معلم';
        case 'admin': return 'مدير';
        default: return type;
    }
  }

  const filteredUsers = allUsers.filter(user =>
    (user.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (user.email?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-white">إدارة المستخدمين</h3>
          <Button className="bg-primary text-primary-foreground hover:bg-primary/90" onClick={() => handleOpenModal()}><Plus className="h-4 w-4 ml-2" />إضافة مستخدم جديد</Button>
        </div>
        <div className="relative mb-6">
          <Search className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            type="text"
            placeholder="ابحث بالاسم أو البريد الإلكتروني..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-secondary/50 border-border text-white placeholder:text-muted-foreground pr-10"
          />
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-right">
            <thead>
              <tr className="border-b border-white/20">
                <th className="py-3 px-4 text-white font-medium">الاسم</th>
                <th className="py-3 px-4 text-white font-medium">البريد الإلكتروني</th>
                <th className="py-3 px-4 text-white font-medium">النوع</th>
                <th className="py-3 px-4 text-white font-medium">التخصص</th>
                <th className="py-3 px-4 text-white font-medium">الحالة</th>
                <th className="py-3 px-4 text-white font-medium">تاريخ الانضمام</th>
                <th className="py-3 px-4 text-white font-medium text-center">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-white/10">
                  <td className="py-3 px-4 text-white">{user.name}</td>
                  <td className="py-3 px-4 text-white/70">{user.email}</td>
                  <td className="py-3 px-4 text-white/70">{getTypeInArabic(user.userType)}</td>
                  <td className="py-3 px-4 text-white/70">{user.specialization || 'غير محدد'}</td>
                  <td className="py-3 px-4"><span className={`px-2 py-1 rounded-full text-xs ${getStatusClass(user.status)}`}>{user.status}</span></td>
                  <td className="py-3 px-4 text-white/70">{user.registrationDate ? new Date(user.registrationDate).toLocaleDateString('ar-SA') : 'غير محدد'}</td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex gap-1 justify-center">
                      <Button size="icon" variant="ghost" className="text-white/70 hover:text-white" onClick={() => handleOpenModal(user)}><Edit className="h-4 w-4" /></Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                           <Button size="icon" variant="ghost" className="text-red-400 hover:text-red-300" onClick={() => setUserToDelete(user)}><Trash2 className="h-4 w-4" /></Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-secondary border-border text-white">
                           <AlertDialogHeader>
                            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
                            <AlertDialogDescription>
                              سيتم حذف المستخدم "{user.name}" بشكل نهائي. لا يمكن التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="border-border hover:bg-white/10">إلغاء</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/80">حذف</AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="bg-secondary border-border text-white">
          <DialogHeader>
            <DialogTitle>{originalEmail ? 'تعديل' : 'إضافة'} مستخدم</DialogTitle>
          </DialogHeader>
          {editingUser && (
            <div className="space-y-4 py-4">
              <div>
                  <Label htmlFor="userName">الاسم</Label>
                  <Input id="userName" value={editingUser.name} onChange={(e) => setEditingUser({ ...editingUser, name: e.target.value })} className="bg-white/10 border-border"/>
              </div>
              <div>
                  <Label htmlFor="userEmail">البريد الإلكتروني</Label>
                  <Input id="userEmail" type="email" value={editingUser.email} onChange={(e) => setEditingUser({ ...editingUser, email: e.target.value })} className="bg-white/10 border-border"/>
              </div>
              <div>
                  <Label htmlFor="userPassword">كلمة المرور</Label>
                  <Input id="userPassword" type="password" placeholder={originalEmail ? 'اتركه فارغاً لعدم التغيير' : ''} onChange={(e) => setEditingUser({ ...editingUser, password: e.target.value })} className="bg-white/10 border-border"/>
              </div>
              <div>
                  <Label htmlFor="userType">نوع المستخدم</Label>
                  <Select value={editingUser.userType} onValueChange={(value) => setEditingUser({ ...editingUser, userType: value })}>
                      <SelectTrigger className="bg-white/10 border-border"><SelectValue/></SelectTrigger>
                      <SelectContent className="bg-secondary border-border text-white">
                          <SelectItem value="student">طالب</SelectItem>
                          <SelectItem value="teacher">معلم</SelectItem>
                          <SelectItem value="admin">مدير</SelectItem>
                      </SelectContent>
                  </Select>
              </div>
              {editingUser.userType === 'teacher' && (
                <>
                <div>
                  <Label htmlFor="specialization">التخصص</Label>
                  <Select value={editingUser.specialization} onValueChange={(value) => setEditingUser({ ...editingUser, specialization: value })}>
                    <SelectTrigger className="bg-white/10 border-border">
                      <div className="flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        <SelectValue placeholder="اختر التخصص" />
                      </div>
                    </SelectTrigger>
                    <SelectContent className="bg-secondary border-border text-white">
                      {specializations.map((spec) => (
                        <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                      ))}
                       <SelectItem value="other">تخصص آخر...</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {editingUser.specialization === 'other' && (
                    <div>
                        <Label htmlFor="otherSpecialization">اكتب التخصص</Label>
                        <Input id="otherSpecialization" value={editingUser.otherSpecialization} onChange={(e) => setEditingUser({ ...editingUser, otherSpecialization: e.target.value })} className="bg-white/10 border-border" />
                    </div>
                )}
                </>
              )}
               <div>
                <Label htmlFor="status">الحالة</Label>
                <Select value={editingUser.status} onValueChange={(value) => setEditingUser({ ...editingUser, status: value })}>
                  <SelectTrigger className="w-full bg-white/10 border-border text-white"><SelectValue /></SelectTrigger>
                  <SelectContent className="bg-secondary border-border text-white">
                    <SelectItem value="نشط">نشط</SelectItem>
                    <SelectItem value="معطل">معطل</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)} className="border-border hover:bg-white/10">إلغاء</Button>
            <Button onClick={handleSave} className="bg-primary text-primary-foreground hover:bg-primary/90">حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UsersContent;