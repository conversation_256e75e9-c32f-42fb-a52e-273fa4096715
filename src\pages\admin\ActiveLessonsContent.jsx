import React, { useState, useEffect, useMemo } from 'react';
import { useBooking } from '@/contexts/BookingContext';
import { Button } from '@/components/ui/button';
import { Radio, Link as LinkIcon, Clock, User, Book } from 'lucide-react';

const ActiveLessonsContent = () => {
  const { bookings, sessionLogs } = useBooking();

  const activeSessions = useMemo(() => {
    const activeLogs = sessionLogs.filter(log => log.status === 'ongoing');
    const activeSessionDetails = activeLogs.map(log => {
      const booking = bookings.find(b => b.id === log.booking_id);
      if (!booking) return null;
      const session = booking.sessions.find(s => s.sessionId === log.session_id);
      if (!session) return null;
      return { ...session, booking };
    }).filter(Boolean); // Remove nulls

    return activeSessionDetails;
  }, [sessionLogs, bookings]);

  const handleJoin = (zoomLink) => {
    if (zoomLink) {
        window.open(zoomLink, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div className="space-y-8">
      <div className="glass-effect rounded-xl p-6">
        <div className="flex items-center gap-4 mb-6">
          <div className="p-3 bg-green-500/20 rounded-full">
            <Radio className="h-6 w-6 text-green-400 animate-pulse" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-white">الحصص النشطة الآن</h3>
            <p className="text-white/70">
              {activeSessions.length > 0
                ? `يوجد حالياً ${activeSessions.length} حصة نشطة.`
                : 'لا توجد حصص نشطة في الوقت الحالي.'}
            </p>
          </div>
        </div>
        
        {activeSessions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {activeSessions.map(session => (
              <div key={session.sessionId} className="bg-secondary/50 rounded-lg p-5 border-l-4 border-green-400 hover-lift">
                <h4 className="font-bold text-lg text-white mb-3 flex items-center gap-2">
                  <Book className="h-5 w-5 text-primary" />
                  {session.booking.subjects.map(s => s.subject).join(', ')}
                </h4>
                <div className="space-y-2 text-white/80">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>الطالب: {session.booking.studentName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>المعلم: {session.booking.teacherName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>الوقت: {session.time}</span>
                  </div>
                </div>
                <div className="mt-4 flex justify-end">
                  <Button
                    onClick={() => handleJoin(session.booking.zoomLink)}
                    size="sm"
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                    disabled={!session.booking.zoomLink}
                  >
                    <LinkIcon className="h-4 w-4 ml-2" />
                    الانضمام للمتابعة
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <Radio className="h-24 w-24 text-white/10 mx-auto mb-4" />
            <p className="text-xl text-white/70">كل شيء هادئ الآن</p>
            <p className="text-white/50">سيتم عرض الحصص النشطة هنا تلقائياً عند بدء موعدها.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActiveLessonsContent;