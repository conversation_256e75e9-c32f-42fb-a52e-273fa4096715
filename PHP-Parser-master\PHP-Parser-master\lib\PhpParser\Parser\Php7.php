<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\Parser;

use Php<PERSON><PERSON><PERSON>\Error;
use Php<PERSON>arser\Modifiers;
use Php<PERSON>arser\Node;
use PhpParser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php7 extends \PhpParser\ParserAbstract
{
    public const YYERRTOK = 256;
    public const T_VOID_CAST = 257;
    public const T_THROW = 258;
    public const T_INCLUDE = 259;
    public const T_INCLUDE_ONCE = 260;
    public const T_EVAL = 261;
    public const T_REQUIRE = 262;
    public const T_REQUIRE_ONCE = 263;
    public const T_LOGICAL_OR = 264;
    public const T_LOGICAL_XOR = 265;
    public const T_LOGICAL_AND = 266;
    public const T_PRINT = 267;
    public const T_YIELD = 268;
    public const T_DOUBLE_ARROW = 269;
    public const T_YIELD_FROM = 270;
    public const T_PLUS_EQUAL = 271;
    public const T_MINUS_EQUAL = 272;
    public const T_MUL_EQUAL = 273;
    public const T_DIV_EQUAL = 274;
    public const T_CONCAT_EQUAL = 275;
    public const T_MOD_EQUAL = 276;
    public const T_AND_EQUAL = 277;
    public const T_OR_EQUAL = 278;
    public const T_XOR_EQUAL = 279;
    public const T_SL_EQUAL = 280;
    public const T_SR_EQUAL = 281;
    public const T_POW_EQUAL = 282;
    public const T_COALESCE_EQUAL = 283;
    public const T_COALESCE = 284;
    public const T_BOOLEAN_OR = 285;
    public const T_BOOLEAN_AND = 286;
    public const T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG = 287;
    public const T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG = 288;
    public const T_IS_EQUAL = 289;
    public const T_IS_NOT_EQUAL = 290;
    public const T_IS_IDENTICAL = 291;
    public const T_IS_NOT_IDENTICAL = 292;
    public const T_SPACESHIP = 293;
    public const T_IS_SMALLER_OR_EQUAL = 294;
    public const T_IS_GREATER_OR_EQUAL = 295;
    public const T_SL = 296;
    public const T_SR = 297;
    public const T_INSTANCEOF = 298;
    public const T_INC = 299;
    public const T_DEC = 300;
    public const T_INT_CAST = 301;
    public const T_DOUBLE_CAST = 302;
    public const T_STRING_CAST = 303;
    public const T_ARRAY_CAST = 304;
    public const T_OBJECT_CAST = 305;
    public const T_BOOL_CAST = 306;
    public const T_UNSET_CAST = 307;
    public const T_POW = 308;
    public const T_NEW = 309;
    public const T_CLONE = 310;
    public const T_EXIT = 311;
    public const T_IF = 312;
    public const T_ELSEIF = 313;
    public const T_ELSE = 314;
    public const T_ENDIF = 315;
    public const T_LNUMBER = 316;
    public const T_DNUMBER = 317;
    public const T_STRING = 318;
    public const T_STRING_VARNAME = 319;
    public const T_VARIABLE = 320;
    public const T_NUM_STRING = 321;
    public const T_INLINE_HTML = 322;
    public const T_ENCAPSED_AND_WHITESPACE = 323;
    public const T_CONSTANT_ENCAPSED_STRING = 324;
    public const T_ECHO = 325;
    public const T_DO = 326;
    public const T_WHILE = 327;
    public const T_ENDWHILE = 328;
    public const T_FOR = 329;
    public const T_ENDFOR = 330;
    public const T_FOREACH = 331;
    public const T_ENDFOREACH = 332;
    public const T_DECLARE = 333;
    public const T_ENDDECLARE = 334;
    public const T_AS = 335;
    public const T_SWITCH = 336;
    public const T_MATCH = 337;
    public const T_ENDSWITCH = 338;
    public const T_CASE = 339;
    public const T_DEFAULT = 340;
    public const T_BREAK = 341;
    public const T_CONTINUE = 342;
    public const T_GOTO = 343;
    public const T_FUNCTION = 344;
    public const T_FN = 345;
    public const T_CONST = 346;
    public const T_RETURN = 347;
    public const T_TRY = 348;
    public const T_CATCH = 349;
    public const T_FINALLY = 350;
    public const T_USE = 351;
    public const T_INSTEADOF = 352;
    public const T_GLOBAL = 353;
    public const T_STATIC = 354;
    public const T_ABSTRACT = 355;
    public const T_FINAL = 356;
    public const T_PRIVATE = 357;
    public const T_PROTECTED = 358;
    public const T_PUBLIC = 359;
    public const T_READONLY = 360;
    public const T_PUBLIC_SET = 361;
    public const T_PROTECTED_SET = 362;
    public const T_PRIVATE_SET = 363;
    public const T_VAR = 364;
    public const T_UNSET = 365;
    public const T_ISSET = 366;
    public const T_EMPTY = 367;
    public const T_HALT_COMPILER = 368;
    public const T_CLASS = 369;
    public const T_TRAIT = 370;
    public const T_INTERFACE = 371;
    public const T_ENUM = 372;
    public const T_EXTENDS = 373;
    public const T_IMPLEMENTS = 374;
    public const T_OBJECT_OPERATOR = 375;
    public const T_NULLSAFE_OBJECT_OPERATOR = 376;
    public const T_LIST = 377;
    public const T_ARRAY = 378;
    public const T_CALLABLE = 379;
    public const T_CLASS_C = 380;
    public const T_TRAIT_C = 381;
    public const T_METHOD_C = 382;
    public const T_FUNC_C = 383;
    public const T_PROPERTY_C = 384;
    public const T_LINE = 385;
    public const T_FILE = 386;
    public const T_START_HEREDOC = 387;
    public const T_END_HEREDOC = 388;
    public const T_DOLLAR_OPEN_CURLY_BRACES = 389;
    public const T_CURLY_OPEN = 390;
    public const T_PAAMAYIM_NEKUDOTAYIM = 391;
    public const T_NAMESPACE = 392;
    public const T_NS_C = 393;
    public const T_DIR = 394;
    public const T_NS_SEPARATOR = 395;
    public const T_ELLIPSIS = 396;
    public const T_NAME_FULLY_QUALIFIED = 397;
    public const T_NAME_QUALIFIED = 398;
    public const T_NAME_RELATIVE = 399;
    public const T_ATTRIBUTE = 400;

    protected int $tokenToSymbolMapSize = 401;
    protected int $actionTableSize = 1578;
    protected int $gotoTableSize = 698;

    protected int $invalidSymbol = 173;
    protected int $errorSymbol = 1;
    protected int $defaultAction = -32766;
    protected int $unexpectedTokenRule = 32767;

    protected int $YY2TBLSTATE = 445;
    protected int $numNonLeafStates = 754;

    protected array $symbolToName = array(
        "EOF",
        "error",
        "T_VOID_CAST",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'.'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_PUBLIC_SET",
        "T_PROTECTED_SET",
        "T_PRIVATE_SET",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_PROPERTY_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'('",
        "')'",
        "'{'",
        "'}'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected array $tokenToSymbol = array(
            0,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,   57,  171,  173,  172,   56,  173,  173,
          166,  167,   54,   51,    9,   52,   53,   55,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,   32,  164,
           45,   17,   47,   31,   69,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,   71,  173,  165,   37,  173,  170,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  168,   36,  169,   59,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,  173,  173,  173,  173,
          173,  173,  173,  173,  173,  173,    1,    2,    3,    4,
            5,    6,    7,    8,   10,   11,   12,   13,   14,   15,
           16,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   30,   33,   34,   35,   38,   39,   40,
           41,   42,   43,   44,   46,   48,   49,   50,   58,   60,
           61,   62,   63,   64,   65,   66,   67,   68,   70,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158,  159,  160,  161,  162,
          163
    );

    protected array $action = array(
          133,  134,  135,  575,  136,  137, 1049,  766,  767,  768,
          138,   41,  850, -341,  495, 1390,-32766,-32766,-32766, 1008,
          841, 1145, 1146, 1147, 1141, 1140, 1139, 1148, 1142, 1143,
         1144,-32766,-32766,-32766, -195,  760,  759,-32766, -194,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767,    0,-32766,    3,    4,  769, 1145, 1146, 1147, 1141,
         1140, 1139, 1148, 1142, 1143, 1144,  388,  389,  448,  272,
           53,  391,  773,  774,  775,  776,  433,    5,  434,  571,
          337,   39,  254,   29,  298,  830,  777,  778,  779,  780,
          781,  782,  783,  784,  785,  786,  806,  576,  807,  808,
          809,  810,  798,  799,  353,  354,  801,  802,  787,  788,
          789,  791,  792,  793,  364,  833,  834,  835,  836,  837,
          577, -382,  306, -382,  794,  795,  578,  579,  244,  818,
          816,  817,  829,  813,  814, 1313,   38,  580,  581,  812,
          582,  583,  584,  585, 1325,  586,  587,  481,  482, -628,
          496, 1009,  815,  588,  589,  140,  139, -628,  133,  134,
          135,  575,  136,  137, 1085,  766,  767,  768,  138,   41,
        -32766, -341, 1046, 1041, 1040, 1039, 1045, 1042, 1043, 1044,
        -32766,-32766,-32766,-32767,-32767,-32767,-32767,  106,  107,  108,
          109,  110, -195,  760,  759, 1058, -194,-32766,-32766,-32766,
          149,-32766,  852,-32766,-32766,-32766,-32766,-32766,-32766,-32766,
          936,  303,  257,  769,-32766,-32766,-32766,  850,-32766,  297,
        -32766,-32766,-32766,-32766,-32766, 1371, 1355,  272,   53,  391,
          773,  774,  775,  776, -625,-32766,  434,-32766,-32766,-32766,
        -32766,  730, -625,  830,  777,  778,  779,  780,  781,  782,
          783,  784,  785,  786,  806,  576,  807,  808,  809,  810,
          798,  799,  353,  354,  801,  802,  787,  788,  789,  791,
          792,  793,  364,  833,  834,  835,  836,  837,  577, -579,
         -275,  317,  794,  795,  578,  579, -577,  818,  816,  817,
          829,  813,  814,  957,  926,  580,  581,  812,  582,  583,
          584,  585,  144,  586,  587,  841,  336,-32766,-32766,-32766,
          815,  588,  589, -628,  139, -628,  133,  134,  135,  575,
          136,  137, 1082,  766,  767,  768,  138,   41,-32766, 1375,
        -32766,-32766,-32766,-32766,-32766,-32766,-32766, 1374,  629,  388,
          389,-32766,-32766,-32766,-32766,-32766, -579, -579, 1081,  433,
          321,  760,  759, -577, -577,-32766, 1293,-32766,-32766,  111,
          112,  113, -579,  282,  843,  851,  623, 1400,  936, -577,
         1401,  769,  333,  938, -585,  114, -579,  725,  294,  298,
         1119, -584,  349, -577,  752,  272,   53,  391,  773,  774,
          775,  776,  145,   86,  434,  306,  336,  336, -625,  731,
         -625,  830,  777,  778,  779,  780,  781,  782,  783,  784,
          785,  786,  806,  576,  807,  808,  809,  810,  798,  799,
          353,  354,  801,  802,  787,  788,  789,  791,  792,  793,
          364,  833,  834,  835,  836,  837,  577, -576,  850, -578,
          794,  795,  578,  579,  845,  818,  816,  817,  829,  813,
          814,  727,  926,  580,  581,  812,  582,  583,  584,  585,
          740,  586,  587,  243, 1055,-32766,-32766,  -85,  815,  588,
          589,  878,  152,  879,  133,  134,  135,  575,  136,  137,
         1087,  766,  767,  768,  138,   41,  350,  961,  960, 1058,
         1058, 1058,-32766,-32766,-32766,  841,-32766,  131,  977,  978,
          400, 1055,   10,  979, -576, -576, -578, -578,  378,  760,
          759,  936,  973,  290,  297,  297,-32766,  846,  936,  154,
         -576,   79, -578,  382,  849,  936, 1058,  336,  878,  769,
          879,  938, -583,  -85, -576,  725, -578,  959,  108,  109,
          110, 1058,  732,  272,   53,  391,  773,  774,  775,  776,
          290,  155,  434,  470,  471,  472,  735,  760,  759,  830,
          777,  778,  779,  780,  781,  782,  783,  784,  785,  786,
          806,  576,  807,  808,  809,  810,  798,  799,  353,  354,
          801,  802,  787,  788,  789,  791,  792,  793,  364,  833,
          834,  835,  836,  837,  577,  926,  434,  847,  794,  795,
          578,  579,  926,  818,  816,  817,  829,  813,  814,  926,
          398,  580,  581,  812,  582,  583,  584,  585,  452,  586,
          587,  157,   87,   88,   89,  453,  815,  588,  589,  454,
          152,  790,  761,  762,  763,  764,  765,  158,  766,  767,
          768,  803,  804,   40,   27,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109,  110,  111,  112,  113, 1134,
          282, 1055,  455,-32766,  994, 1288, 1287, 1289,  725,  390,
          389,  938,  114,  856, 1120,  725,  769,  159,  938,  433,
          672,   23,  725, 1118,  691,  692, 1058,-32766,  153,  416,
          770,  771,  772,  773,  774,  775,  776,  -78, -619,  839,
         -619, -581,  386,  387,  392,  393,  830,  777,  778,  779,
          780,  781,  782,  783,  784,  785,  786,  806,  828,  807,
          808,  809,  810,  798,  799,  800,  827,  801,  802,  787,
          788,  789,  791,  792,  793,  832,  833,  834,  835,  836,
          837,  838,  161,  663,  664,  794,  795,  796,  797,   36,
          818,  816,  817,  829,  813,  814,  -58,  -57,  805,  811,
          812,  819,  820,  822,  821,  -87,  823,  824, -581, -581,
          128,  129,  141,  815,  826,  825,   54,   55,   56,   57,
          527,   58,   59,  142, -110,  148,  162,   60,   61, -110,
           62, -110,  936,  163,  164,  165,  313,  166, -581, -110,
         -110, -110, -110, -110, -110, -110, -110, -110, -110, -110,
         1293,  -84,  953,  -78,  -73,  -72,  -71,  -70,  -69,  -68,
          -67,  -66,  -65,  742,  -46,   63,   64,  -18, -575, 1286,
          146,   65,   51,   66,  251,  252,   67,   68,   69,   70,
           71,   72,   73,   74,  281,   31,  273,   47,  450,  528,
          291, -357,  741, 1319, 1320,  529,  744,  850,  935,  151,
          295, 1317,   45,   22,  530, 1284,  531, -309,  532, -305,
          533,  286,  936,  534,  535,  287,  926,  292,   48,   49,
          456,  385,  384,  293,   50,  536,  342,  296,  282, 1057,
          376,  348,  850,  299,  300, -575, -575, 1279,  114,  307,
          308,  701,  538,  539,  540,  150,  841,-32766, 1288, 1287,
         1289, -575,  850,  294,  542,  543, 1402, 1305, 1306, 1307,
         1308, 1310, 1302, 1303,  305, -575,  716, -110, -110,  130,
         1309, 1304, -110,  593, 1288, 1287, 1289,  306,   13,  673,
           75, -110, 1152,  678,  331,  332,  336, -154, -154, -154,
        -32766,  718,  694,   -4,  936,  938,  926,  314,  478,  725,
          506, 1324, -154,  705, -154,  679, -154,  695, -154,  974,
         1326, -541,  306,  312,  311,   79,  849,  661,  383,   43,
          320,  336,   37, 1252,    0,    0,   52,    0,    0,  977,
          978,    0,  760,  759,  537,-32766,    0,    0,    0,  706,
            0,    0,  912,  973, -110, -110, -110,   35,  115,  116,
          117,  118,  119,  120,  121,  122,  123,  124,  125,  126,
          127, -531,   11,  707,  708,   31,  274,   30,  380,  955,
          599, -613,  306,  627,    0,  938,    0,  850,  926,  725,
         -154, 1317, 1288, 1287, 1289,   44, -612,  749,  290,  750,
         1194, 1196,  869,  309,  310,  917, 1018,  995, 1002,  992,
          383, -575,  446, 1003,  915,  990, 1123,  304, 1126,  381,
         1127,  977,  978, 1124, 1125, 1131,  537, 1279, 1314,  861,
          330,  760,  759,  132,  541,  973, -110, -110, -110, 1341,
         1359, 1393, 1293,  666,  542,  543, -611, 1305, 1306, 1307,
         1308, 1310, 1302, 1303, -585, -584, -583, -582,   21, -525,
         1309, 1304,    1,   32,  760,  759,   33,  938,-32766, -278,
           77,  725,   -4,  -16, 1286,  332,  336,   42, -575, -575,
           46,-32766,-32766,-32766,   76,-32766,   80,-32766,   81,-32766,
           82,   83,-32766,   84, -575,   85,  147,-32766,-32766,-32766,
          156,-32766,  160,-32766,-32766,  249,  379, 1286, -575,-32766,
          430,   31,  273,  338,-32766,-32766,-32766,  365,-32766,  366,
        -32766,-32766,-32766,  850,  850,-32766,  367, 1317,  368,  369,
        -32766,-32766,-32766,  370,  371,  372,-32766,-32766,  373,  374,
          375,  377,-32766,  430,  447,  570,   31,  274, -276, -275,
           15,   16,   78,   17,-32766,   18,   20,  414,  850, -110,
         -110,  497, 1317, 1279, -110,  498,  505,  508,  509,  510,
          511,  515,  516, -110,  517,  525,  604,  711, 1088, 1084,
         1234,  543,-32766, 1305, 1306, 1307, 1308, 1310, 1302, 1303,
         1315, 1086, 1083,  -50, 1064, 1274, 1309, 1304, 1279, 1060,
         -280, -102,   14,   19,  306,   24,   77,   79,  415,  303,
          413,  332,  336,  336,  618,  624,  543,  652, 1305, 1306,
         1307, 1308, 1310, 1302, 1303,  717,  143, 1238, 1292, 1235,
         1372, 1309, 1304,  726,  729,  733,-32766,  734,  736,  737,
          738,   77, 1286,  419,  739,  743,  332,  336,  728,-32766,
        -32766,-32766,  746,-32766,  913,-32766, 1397,-32766, 1399,  872,
        -32766,  871,  967, 1010, 1398,-32766,-32766,-32766,  966,-32766,
          964,-32766,-32766,  965,  968, 1286, 1267,-32766,  430,  946,
          956,  944,-32766,-32766,-32766, 1000,-32766, 1001,-32766,-32766,
        -32766,  650, 1396,-32766, 1353, 1342, 1360, 1369,-32766,-32766,
        -32766, 1318,-32766,  336,-32766,-32766,  936,    0, 1286,    0,
        -32766,  430,    0,    0,    0,-32766,-32766,-32766,    0,-32766,
            0,-32766,-32766,-32766,    0,    0,-32766,    0,    0,  936,
            0,-32766,-32766,-32766,    0,-32766,    0,-32766,-32766,    0,
            0, 1286,    0,-32766,  430,    0,    0,    0,-32766,-32766,
        -32766,    0,-32766,    0,-32766,-32766,-32766,    0,    0,-32766,
            0,    0,    0,  501,-32766,-32766,-32766,    0,-32766,    0,
        -32766,-32766,    0,    0, 1286,  606,-32766,  430,    0,    0,
            0,-32766,-32766,-32766,    0,-32766,    0,-32766,-32766,-32766,
          926,    0,-32766,    2,    0,    0,    0,-32766,-32766,-32766,
            0,    0,    0,-32766,-32766,    0, -253, -253, -253,-32766,
          430,    0,  383,  926,    0,    0,    0,    0,    0,    0,
            0,-32766,    0,  977,  978,    0,    0,    0,  537, -252,
         -252, -252,    0,    0,    0,  383,  912,  973, -110, -110,
         -110,    0,    0,    0,    0,    0,  977,  978,    0,    0,
            0,  537,    0,    0,    0,    0,    0,    0,    0,  912,
          973, -110, -110, -110,-32766,    0,    0,    0,    0,  938,
         1286,    0,    0,  725, -253,    0,    0,-32766,-32766,-32766,
            0,-32766,    0,-32766,    0,-32766,    0,    0,-32766,    0,
            0,    0,  938,-32766,-32766,-32766,  725, -252,    0,-32766,
        -32766,    0,    0,    0,    0,-32766,  430,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,-32766
    );

    protected array $actionCheck = array(
            3,    4,    5,    6,    7,    8,    1,   10,   11,   12,
           13,   14,   83,    9,   32,   86,   10,   11,   12,   32,
           81,  117,  118,  119,  120,  121,  122,  123,  124,  125,
          126,   10,   11,   12,    9,   38,   39,   31,    9,   33,
           34,   35,   36,   37,   38,   39,   40,   41,   42,   43,
           44,    0,   31,    9,    9,   58,  117,  118,  119,  120,
          121,  122,  123,  124,  125,  126,  107,  108,  109,   72,
           73,   74,   75,   76,   77,   78,  117,    9,   81,   86,
           71,  152,  153,    9,   31,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  107,  163,  109,  127,  128,  129,  130,   15,  132,
          133,  134,  135,  136,  137,    1,    9,  140,  141,  142,
          143,  144,  145,  146,  151,  148,  149,  138,  139,    1,
          168,  164,  155,  156,  157,    9,  159,    9,    3,    4,
            5,    6,    7,    8,  167,   10,   11,   12,   13,   14,
          117,  167,  119,  120,  121,  122,  123,  124,  125,  126,
           10,   11,   12,   45,   46,   47,   48,   49,   50,   51,
           52,   53,  167,   38,   39,  142,  167,   10,   11,   12,
            9,   31,    1,   33,   34,   35,   36,   37,   38,   39,
            1,  167,    9,   58,   10,   11,   12,   83,   31,  166,
           33,   34,   35,   36,   37,    1,    1,   72,   73,   74,
           75,   76,   77,   78,    1,   31,   81,   33,   34,   35,
           36,   32,    9,   88,   89,   90,   91,   92,   93,   94,
           95,   96,   97,   98,   99,  100,  101,  102,  103,  104,
          105,  106,  107,  108,  109,  110,  111,  112,  113,  114,
          115,  116,  117,  118,  119,  120,  121,  122,  123,   71,
          167,    9,  127,  128,  129,  130,   71,  132,  133,  134,
          135,  136,  137,    1,   85,  140,  141,  142,  143,  144,
          145,  146,  168,  148,  149,   81,  172,   10,   11,   12,
          155,  156,  157,  165,  159,  167,    3,    4,    5,    6,
            7,    8,  167,   10,   11,   12,   13,   14,   31,    1,
           33,   34,   35,   10,   10,   11,   12,    9,   52,  107,
          108,   10,   11,   12,   10,   11,  138,  139,    1,  117,
            9,   38,   39,  138,  139,   31,    1,   33,   34,   54,
           55,   56,  154,   58,   81,  164,    1,   81,    1,  154,
           84,   58,    9,  164,  166,   70,  168,  168,   31,   31,
          164,  166,    9,  168,  168,   72,   73,   74,   75,   76,
           77,   78,  168,  168,   81,  163,  172,  172,  165,   32,
          167,   88,   89,   90,   91,   92,   93,   94,   95,   96,
           97,   98,   99,  100,  101,  102,  103,  104,  105,  106,
          107,  108,  109,  110,  111,  112,  113,  114,  115,  116,
          117,  118,  119,  120,  121,  122,  123,   71,   83,   71,
          127,  128,  129,  130,  161,  132,  133,  134,  135,  136,
          137,  168,   85,  140,  141,  142,  143,  144,  145,  146,
          168,  148,  149,   98,  117,  117,  117,   32,  155,  156,
          157,  107,  159,  109,    3,    4,    5,    6,    7,    8,
          167,   10,   11,   12,   13,   14,    9,   73,   74,  142,
          142,  142,   10,   11,   12,   81,  141,   15,  118,  119,
          107,  117,  109,  123,  138,  139,  138,  139,    9,   38,
           39,    1,  132,  166,  166,  166,  117,   81,    1,   15,
          154,  166,  154,    9,  160,    1,  142,  172,  107,   58,
          109,  164,  166,   98,  168,  168,  168,  123,   51,   52,
           53,  142,   32,   72,   73,   74,   75,   76,   77,   78,
          166,   15,   81,  133,  134,  135,   32,   38,   39,   88,
           89,   90,   91,   92,   93,   94,   95,   96,   97,   98,
           99,  100,  101,  102,  103,  104,  105,  106,  107,  108,
          109,  110,  111,  112,  113,  114,  115,  116,  117,  118,
          119,  120,  121,  122,  123,   85,   81,  161,  127,  128,
          129,  130,   85,  132,  133,  134,  135,  136,  137,   85,
            9,  140,  141,  142,  143,  144,  145,  146,    9,  148,
          149,   15,   10,   11,   12,    9,  155,  156,  157,    9,
          159,    3,    4,    5,    6,    7,    8,   15,   10,   11,
           12,   13,   14,   31,  102,   33,   34,   35,   36,   37,
           38,   39,   40,   41,   42,   43,   44,   45,   46,   47,
           48,   49,   50,   51,   52,   53,   54,   55,   56,  127,
           58,  117,    9,  117,  164,  160,  161,  162,  168,  107,
          108,  164,   70,    9,  169,  168,   58,   15,  164,  117,
           76,   77,  168,    1,   76,   77,  142,  141,  102,  103,
           72,   73,   74,   75,   76,   77,   78,   17,  165,   81,
          167,   71,  107,  108,  107,  108,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,  123,   15,  112,  113,  127,  128,  129,  130,   15,
          132,  133,  134,  135,  136,  137,   17,   17,  140,  141,
          142,  143,  144,  145,  146,   32,  148,  149,  138,  139,
           17,   17,   17,  155,  156,  157,    2,    3,    4,    5,
            6,    7,    8,   17,  102,   17,   17,   13,   14,  107,
           16,  109,    1,   17,   17,   17,  114,   17,  168,  117,
          118,  119,  120,  121,  122,  123,  124,  125,  126,  127,
            1,   32,   39,   32,   32,   32,   32,   32,   32,   32,
           32,   32,   32,   32,   32,   51,   52,   32,   71,   81,
           32,   57,   71,   59,   60,   61,   62,   63,   64,   65,
           66,   67,   68,   69,   32,   71,   72,   73,   74,   75,
           32,  169,   32,   79,   80,   81,   32,   83,   32,   32,
           38,   87,   88,   89,   90,  117,   92,   36,   94,   36,
           96,   36,    1,   99,  100,   36,   85,   36,  104,  105,
          106,  107,  108,   36,  110,  111,   36,   38,   58,  141,
          116,  117,   83,   38,   38,  138,  139,  123,   70,  138,
          139,   78,  128,  129,  130,   71,   81,   86,  160,  161,
          162,  154,   83,   31,  140,  141,   84,  143,  144,  145,
          146,  147,  148,  149,  150,  168,   81,  118,  119,  168,
          156,  157,  123,   90,  160,  161,  162,  163,   98,   91,
          166,  132,   83,   97,  170,  171,  172,   76,   77,   78,
          141,   93,   95,    0,    1,  164,   85,  115,   98,  168,
           98,  151,   91,   81,   93,  101,   95,  101,   97,  132,
          151,  154,  163,  137,  136,  166,  160,  114,  107,  164,
          136,  172,  168,  170,   -1,   -1,   71,   -1,   -1,  118,
          119,   -1,   38,   39,  123,  141,   -1,   -1,   -1,  117,
           -1,   -1,  131,  132,  133,  134,  135,   17,   18,   19,
           20,   21,   22,   23,   24,   25,   26,   27,   28,   29,
           30,  154,  154,  141,  142,   71,   72,  154,  154,  159,
          158,  166,  163,  158,   -1,  164,   -1,   83,   85,  168,
          169,   87,  160,  161,  162,  164,  166,  164,  166,  164,
           60,   61,  164,  138,  139,  164,  164,  164,  164,  164,
          107,   71,  109,  164,  164,  164,  164,  114,  164,  154,
          164,  118,  119,  164,  164,  164,  123,  123,  165,  165,
          168,   38,   39,  168,  131,  132,  133,  134,  135,  165,
          165,  165,    1,  165,  140,  141,  166,  143,  144,  145,
          146,  147,  148,  149,  166,  166,  166,  166,  155,  166,
          156,  157,  166,  166,   38,   39,  166,  164,   75,  167,
          166,  168,  169,   32,   81,  171,  172,  166,  138,  139,
          166,   88,   89,   90,  166,   92,  166,   94,  166,   96,
          166,  166,   99,  166,  154,  166,  166,  104,  105,  106,
          166,   75,  166,  110,  111,  166,  168,   81,  168,  116,
          117,   71,   72,  166,   88,   89,   90,  166,   92,  166,
           94,  128,   96,   83,   83,   99,  166,   87,  166,  166,
          104,  105,  106,  166,  166,  166,  110,  111,  166,  166,
          166,  166,  116,  117,  166,  166,   71,   72,  167,  167,
          167,  167,  159,  167,  128,  167,  167,  167,   83,  118,
          119,  167,   87,  123,  123,  167,  167,  167,  167,  167,
          167,  167,  167,  132,  167,  167,  167,  167,  167,  167,
          167,  141,  141,  143,  144,  145,  146,  147,  148,  149,
          167,  167,  167,   32,  167,  167,  156,  157,  123,  167,
          167,  167,  167,  167,  163,  167,  166,  166,  169,  167,
          167,  171,  172,  172,  167,  167,  141,  167,  143,  144,
          145,  146,  147,  148,  149,  167,   32,  167,  167,  167,
          167,  156,  157,  168,  168,  168,   75,  168,  168,  168,
          168,  166,   81,  169,  168,  168,  171,  172,  168,   88,
           89,   90,  169,   92,  169,   94,  169,   96,  169,  169,
           99,  169,  169,  169,  169,  104,  105,  106,  169,   75,
          169,  110,  111,  169,  169,   81,  169,  116,  117,  169,
          169,  169,   88,   89,   90,  169,   92,  169,   94,  128,
           96,  169,  169,   99,  169,  169,  169,  169,  104,  105,
          106,  171,   75,  172,  110,  111,    1,   -1,   81,   -1,
          116,  117,   -1,   -1,   -1,   88,   89,   90,   -1,   92,
           -1,   94,  128,   96,   -1,   -1,   99,   -1,   -1,    1,
           -1,  104,  105,  106,   -1,   75,   -1,  110,  111,   -1,
           -1,   81,   -1,  116,  117,   -1,   -1,   -1,   88,   89,
           90,   -1,   92,   -1,   94,  128,   96,   -1,   -1,   99,
           -1,   -1,   -1,  103,  104,  105,  106,   -1,   75,   -1,
          110,  111,   -1,   -1,   81,   82,  116,  117,   -1,   -1,
           -1,   88,   89,   90,   -1,   92,   -1,   94,  128,   96,
           85,   -1,   99,  166,   -1,   -1,   -1,  104,  105,  106,
           -1,   -1,   -1,  110,  111,   -1,  101,  102,  103,  116,
          117,   -1,  107,   85,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,  128,   -1,  118,  119,   -1,   -1,   -1,  123,  101,
          102,  103,   -1,   -1,   -1,  107,  131,  132,  133,  134,
          135,   -1,   -1,   -1,   -1,   -1,  118,  119,   -1,   -1,
           -1,  123,   -1,   -1,   -1,   -1,   -1,   -1,   -1,  131,
          132,  133,  134,  135,   75,   -1,   -1,   -1,   -1,  164,
           81,   -1,   -1,  168,  169,   -1,   -1,   88,   89,   90,
           -1,   92,   -1,   94,   -1,   96,   -1,   -1,   99,   -1,
           -1,   -1,  164,  104,  105,  106,  168,  169,   -1,  110,
          111,   -1,   -1,   -1,   -1,  116,  117,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,  128
    );

    protected array $actionBase = array(
            0,  155,   -3,  313,  471,  471,  881,  963, 1365, 1388,
          892,  134,  515,  -61,  367,  524,  524,  801,  524,  209,
          510,  283,  517,  517,  517,  920,  855,  628,  628,  855,
          628, 1053, 1053, 1053, 1053, 1086, 1086, 1320, 1320, 1353,
         1254, 1221, 1449, 1449, 1449, 1449, 1449, 1287, 1449, 1449,
         1449, 1449, 1449, 1287, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449, 1449,
         1449, 1449, 1449, 1449, 1449, 1449, 1449,  201,  -13,   44,
          365,  744, 1102, 1120, 1107, 1121, 1096, 1095, 1103, 1108,
         1122, 1183, 1185,  837, 1186, 1187, 1182, 1188, 1110,  938,
         1098, 1118,  612,  612,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,  612,  612,  612,  612,
          323,  482,  334,  331,  331,  331,  331,  331,  331,  331,
          331,  331,  331,  331,  331,  331,  331,  331,  331,  331,
          331,  331,  331,  964,  964,   21,   21,   21,  324, 1135,
         1100, 1135, 1135, 1135, 1135, 1135, 1135, 1135, 1135,  297,
          204, 1000,  187,  170,  170,    6,    6,    6,    6,    6,
          692,   53, 1101,  819,  819,  138,  138,  138,  138,  542,
           14,  347,  355,  -41,  348,  232,  384,  384,  487,  487,
          554,  554,  349,  349,  554,  554,  554,  399,  399,  399,
          399,  208,  215,  366,  364,   -7,  864,  224,  224,  224,
          224,  864,  864,  864,  864,  829, 1190,  864, 1011, 1027,
          864,  864,  368,  767,  767,  925,  305,  305,  305,  767,
          421,  -71,  -71,  421,  380,  -71,  225,  286,  556,  847,
          572,  543,  556,  640,  771,  233,  148,  826,  605,  826,
         1094,  831,  831,  802,  792,  921, 1140, 1123,  874, 1176,
          876, 1178,  420,    9,  791, 1093, 1093, 1093, 1093, 1093,
         1093, 1093, 1093, 1093, 1093, 1093, 1191,  519, 1094,  436,
         1191, 1191, 1191,  519,  519,  519,  519,  519,  519,  519,
          519,  805,  519,  519,  641,  436,  614,  618,  436,  860,
          519,  877,  201,  201,  201,  201,  201,  201,  201,  201,
          201,  201,  201,  -18,  201,  201,  -13,  292,  292,  201,
          216,    5,  292,  292,  292,  292,  201,  201,  201,  201,
          605,  840,  882,  607,  435,  885,   29,  840,  840,  840,
            4,  113,   25,  841,  843,  393,  835,  835,  835,  869,
          956,  956,  835,  839,  835,  869,  835,  835,  956,  956,
          879,  956,  146,  609,  373,  514,  616,  956,  272,  835,
          835,  835,  835,  854,  956,   45,   68,  620,  835,  203,
          191,  835,  835,  854,  848,  828,  846,  956,  956,  956,
          854,  499,  846,  846,  846,  893,  895,  873,  822,  363,
          341,  674,  127,  783,  822,  822,  835,  601,  873,  822,
          873,  822,  880,  822,  822,  822,  873,  822,  839,  477,
          822,  779,  786,  663,   74,  822,   51,  978,  980,  743,
          982,  971,  984, 1038,  985,  987, 1125,  953,  999,  974,
          989, 1039,  960,  957,  836,  763,  764,  878,  827,  951,
          838,  838,  838,  948,  949,  838,  838,  838,  838,  838,
          838,  838,  838,  763,  923,  884,  853, 1013,  765,  776,
         1069,  820, 1145,  823, 1011,  978,  987,  789,  974,  989,
          960,  957,  800,  799,  797,  798,  796,  795,  793,  794,
          808, 1071, 1072,  990,  825,  778, 1049, 1020, 1143,  922,
         1022, 1023, 1050, 1073,  898, 1083, 1147,  844, 1149, 1150,
          924, 1028, 1126,  838,  940,  875,  934, 1027,  950,  763,
          935, 1084, 1085, 1043,  824, 1054, 1058,  998,  870,  842,
          936, 1152, 1029, 1032, 1033, 1127, 1129,  891, 1044,  962,
         1059,  872, 1099, 1060, 1061, 1062, 1063, 1130, 1153, 1131,
          890, 1132,  901,  858, 1041,  856, 1154,  504,  851,  857,
          866, 1035,  536, 1007, 1136, 1134, 1155, 1064, 1065, 1067,
         1159, 1161,  994,  902, 1046,  867, 1048, 1042,  903,  904,
          606,  865, 1087,  845,  849,  859,  622,  672, 1164, 1165,
         1167,  996,  830,  833,  905,  909, 1088,  832, 1092, 1170,
          737,  910, 1171, 1070,  787,  788,  690,  750,  749,  790,
          868, 1137,  883,  852,  850, 1034,  788,  834,  911, 1172,
          912,  914,  916, 1068,  919,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  628,  628,  628,  628,  784,  784,
          784,  784,  784,  784,  784,  628,  784,  784,  784,  628,
          628,    0,    0,  628,    0,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
          784,  612,  612,  612,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,  612,  612,  612,  612,
          612,  612,  612,  612,  612,  612,  612,  758,  758,  612,
          612,  612,  612,  758,  758,  758,  758,  758,  758,  758,
          758,  758,  758,  612,  612,    0,  612,  612,  612,  612,
          612,  612,  612,  612,  879,  758,  758,  758,  758,  305,
          305,  305,  305,  -96,  -96,  758,  758,  380,  758,  380,
          758,  758,  305,  305,  758,  758,  758,  758,  758,  758,
          758,  758,  758,  758,  758,    0,    0,    0,  436,  -71,
          758,  839,  839,  839,  839,  758,  758,  758,  758,  -71,
          -71,  758,  414,  414,  758,  758,    0,    0,    0,    0,
            0,    0,    0,    0,  436,    0,    0,  436,    0,    0,
          839,  839,  758,  380,  879,  328,  758,    0,    0,    0,
            0,  436,  839,  436,  519,  -71,  -71,  519,  519,  292,
          201,  328,  596,  596,  596,  596,    0,    0,  605,  879,
          879,  879,  879,  879,  879,  879,  879,  879,  879,  879,
          839,    0,  879,    0,  839,  839,  839,    0,    0,    0,
            0,    0,    0,    0,    0,  956,    0,    0,    0,    0,
            0,    0,    0,  839,    0,  956,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  839,    0,    0,    0,    0,
            0,    0,    0,    0,    0,  838,  870,    0,    0,  870,
            0,  838,  838,  838,    0,    0,    0,  865,  832
    );

    protected array $actionDefault = array(
            3,32767,32767,32767,  102,  102,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  100,
        32767,  631,  631,  631,  631,32767,32767,  257,  102,32767,
        32767,  500,  415,  415,  415,32767,32767,32767,  573,  573,
          573,  573,  573,   17,32767,32767,32767,32767,32767,32767,
        32767,  500,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,   36,    7,    8,   10,   11,   49,  338,
          100,32767,32767,32767,32767,32767,32767,32767,32767,  102,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  624,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  403,  494,  504,  482,  483,  485,  486,  414,
          574,  630,  344,  627,  342,  413,  146,  354,  343,  245,
          261,  505,  262,  506,  509,  510,  218,  400,  150,  151,
          446,  501,  448,  499,  503,  447,  420,  427,  428,  429,
          430,  431,  432,  433,  434,  435,  436,  437,  438,  439,
          418,  419,  502,32767,32767,  479,  478,  477,  444,32767,
        32767,32767,32767,32767,32767,32767,32767,  102,32767,  445,
          449,  417,  452,  450,  451,  468,  469,  466,  467,  470,
        32767,  323,32767,32767,32767,  471,  472,  473,  474,  381,
          379,32767,32767,  111,  323,  111,32767,32767,  459,  460,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,  517,  567,  476,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  102,32767,32767,
        32767,  100,  569,  441,  443,  537,  454,  455,  453,  421,
        32767,  542,32767,  102,32767,  544,32767,32767,32767,32767,
        32767,32767,32767,  568,32767,  575,  575,32767,  530,  100,
          196,32767,  543,  196,  196,32767,32767,32767,32767,32767,
        32767,32767,32767,  638,  530,  110,  110,  110,  110,  110,
          110,  110,  110,  110,  110,  110,32767,  196,  110,32767,
        32767,32767,  100,  196,  196,  196,  196,  196,  196,  196,
          196,  545,  196,  196,  191,32767,  271,  273,  102,  592,
          196,  547,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          530,  464,  139,32767,  532,  139,  575,  456,  457,  458,
          575,  575,  575,  319,  296,32767,32767,32767,32767,32767,
          545,  545,  100,  100,  100,  100,32767,32767,32767,32767,
          111,  516,   99,   99,   99,   99,   99,  103,  101,32767,
        32767,32767,32767,  226,32767,  101,  101,   99,32767,  101,
          101,32767,32767,  226,  228,  215,  230,32767,  596,  597,
          226,  101,  230,  230,  230,  250,  250,  519,  325,  101,
           99,  101,  101,  198,  325,  325,32767,  101,  519,  325,
          519,  325,  200,  325,  325,  325,  519,  325,32767,  101,
          325,  217,  403,   99,   99,  325,32767,32767,32767,  532,
        32767,32767,32767,32767,32767,32767,32767,  225,32767,32767,
        32767,32767,32767,32767,32767,32767,  562,32767,  580,  594,
          462,  463,  465,  579,  577,  487,  488,  489,  490,  491,
          492,  493,  496,  626,32767,  536,32767,32767,32767,  353,
        32767,  636,32767,32767,32767,    9,   74,  525,   42,   43,
           51,   57,  551,  552,  553,  554,  548,  549,  555,  550,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  637,32767,  575,32767,
        32767,32767,32767,  461,  557,  602,32767,32767,  576,  629,
        32767,32767,32767,32767,32767,32767,32767,32767,  139,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  562,
        32767,  137,32767,32767,32767,32767,32767,32767,32767,32767,
          558,32767,32767,32767,  575,32767,32767,32767,32767,  321,
          318,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  575,32767,32767,
        32767,32767,32767,  298,32767,  315,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  399,  532,  301,  303,  304,32767,
        32767,32767,32767,  375,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  153,  153,    3,    3,  356,
          153,  153,  153,  356,  356,  153,  356,  356,  356,  153,
          153,  153,  153,  153,  153,  283,  186,  265,  268,  250,
          250,  153,  367,  153
    );

    protected array $goto = array(
          202,  169,  202,  202,  202, 1056,  842,  712,  359,  670,
          671,  598,  688,  689,  690,  748,  653,  655,  591,  929,
          675,  930, 1090,  721,  699,  702, 1028,  710,  719, 1024,
          171,  171,  171,  171,  226,  203,  199,  199,  181,  183,
          221,  199,  199,  199,  199,  199, 1180,  200,  200,  200,
          200,  200, 1180,  193,  194,  195,  196,  197,  198,  223,
          221,  224,  550,  551,  431,  552,  555,  556,  557,  558,
          559,  560,  561,  562,  172,  173,  174,  201,  175,  176,
          177,  170,  178,  179,  180,  182,  220,  222,  225,  245,
          248,  259,  260,  262,  263,  264,  265,  266,  267,  268,
          269,  275,  276,  277,  278,  288,  289,  326,  327,  328,
          437,  438,  439,  613,  227,  228,  229,  230,  231,  232,
          233,  234,  235,  236,  237,  238,  239,  240,  241,  184,
          242,  185,  194,  195,  196,  197,  198,  223,  204,  205,
          206,  207,  246,  186,  187,  208,  188,  209,  205,  189,
          247,  204,  168,  210,  211,  190,  212,  213,  214,  191,
          215,  216,  192,  217,  218,  219,  285,  283,  285,  285,
          870, 1089, 1091, 1094,  615,  255,  255,  255,  255,  255,
          441,  677,  614, 1130,  884,  867,  436,  329,  323,  324,
          345,  608,  440,  346,  442,  654,  724,  492,  521,  715,
          896, 1128,  993,  883,  494,  253,  253,  253,  253,  250,
          256,  489, 1361, 1362, 1386, 1386,  925,  920,  921,  934,
          876,  922,  873,  923,  924,  874,  877,  363,  928,  881,
          480,  480,  868,  880, 1386,  848,  474,  363,  363,  480,
         1117, 1112, 1113, 1114, 1229,  351,  362,  362,  362,  362,
         1389, 1389,  429,  363,  363, 1017,  902,  363,  989, 1403,
          747,  360,  361,  566, 1026, 1021, 1056, 1285, 1285, 1285,
          569,  352,  351,  363,  363,  605, 1056, 1285,  848, 1056,
          848, 1056, 1056, 1137, 1138, 1056, 1056, 1056, 1056, 1056,
         1056, 1056, 1056, 1056, 1056, 1056,  357, 1261,  962,  637,
          674, 1285, 1262, 1265,  963, 1266, 1285, 1285, 1285, 1285,
         1376,  435, 1285,  628,  402, 1285, 1285, 1368, 1368, 1368,
         1368, 1347,  574,  567, 1062, 1061, 1059, 1059,  958,  958,
          697,  970, 1014,  942, 1051, 1067, 1068,  943,  565,  565,
          565,  603,  513,  522,  514,  863,  676,  863,  565,  709,
          520, 1176,  318,  567,  574,  600,  601,  319,  611,  617,
          844,  633,  634, 1080,    8,  709,    9,  449,  709,   28,
         1065, 1066,  467,  335,  316,  569,  698,  987,  987,  987,
          987, 1363, 1364,  467,  639,  639,  981,  988,  609,  631,
         1316, 1316, 1316, 1316, 1316, 1316, 1316, 1316, 1316, 1316,
         1335, 1335,  863,  469,  682,  469, 1335, 1335, 1335, 1335,
         1335, 1335, 1335, 1335, 1335, 1335,  347,  258,  258,  626,
          640,  643,  644,  645,  646,  667,  668,  669,  723,  632,
          460,  860,  460,  460,  460, 1358, 1358, 1358,  553,  553,
         1278,  985,  420,  720,  553, 1358,  553,  553,  553,  553,
          553,  553,  553,  553,  451,  889,  568,  595,  568,  647,
          649,  651,  568,  976,  595,  411,  405,  473,  886, 1276,
         1370, 1370, 1370, 1370,  909,  866,  909,  909, 1036,  483,
          612,  484,  485,  751,  563,  563,  563,  563,  894,  619,
         1101, 1394, 1395,  412, 1332, 1332,  898,  490, 1151, 1354,
         1332, 1332, 1332, 1332, 1332, 1332, 1332, 1332, 1332, 1332,
          279, 1105,  334,  334,  334,  998,  892,    0, 1280, 1047,
            0,    0,  863,    0,    0,  460,  460,  460,  460,  460,
          460,  460,  460,  460,  460,  460,    0,    0,  460, 1103,
          554,  554,    0, 1356, 1356, 1103,  554,  554,  554,  554,
          554,  554,  554,  554,  554,  554,  621,  622,  417,  418,
          947, 1166,    0,  686,    0,  687,    0,  422,  423,  424,
            0,  700, 1033,    0,  425, 1281, 1282,    0, 1268,  355,
          888,    0,  680, 1012,  858,    0,    0,    0,  882,  443,
            0, 1268,    0,  897,  885, 1100, 1104,    0,    0,    0,
         1275,    0,  443,    0, 1283, 1344, 1345,  996,    0,    0,
         1063, 1063,    0,    0,    0,  681, 1074, 1070, 1071,  404,
          407,  616,  620,    0,    0,    0,    0,    0,    0,    0,
          986,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0, 1149,  901,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0, 1031, 1031
    );

    protected array $gotoCheck = array(
           42,   42,   42,   42,   42,   73,    6,   73,   97,   86,
           86,   48,   86,   86,   86,   48,   48,   48,  127,   65,
           48,   65,  131,    9,   48,   48,   48,   48,   48,   48,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   23,   23,   23,   23,
           15,  130,  130,  130,  134,    5,    5,    5,    5,    5,
           66,   66,    8,    8,   35,   26,   66,   66,   66,   66,
           66,   66,   66,   66,   66,   66,    8,   84,    8,    8,
           35,    8,   49,   35,   84,    5,    5,    5,    5,    5,
            5,  185,  185,  185,  191,  191,   15,   15,   15,   15,
           15,   15,   15,   15,   15,   15,   15,   14,   15,   15,
          157,  157,   27,   15,  191,   12,  159,   14,   14,  157,
           15,   15,   15,   15,  159,  177,   24,   24,   24,   24,
          191,  191,   43,   14,   14,   50,   45,   14,   50,   14,
           50,   97,   97,   50,   50,   50,   73,   73,   73,   73,
           14,  177,  177,   14,   14,  181,   73,   73,   12,   73,
           12,   73,   73,  148,  148,   73,   73,   73,   73,   73,
           73,   73,   73,   73,   73,   73,  188,   79,   79,   56,
           56,   73,   79,   79,   79,   79,   73,   73,   73,   73,
          190,   13,   73,   13,   62,   73,   73,    9,    9,    9,
            9,   14,   76,   76,  119,  119,   89,   89,    9,    9,
           89,   89,  103,   73,   89,   89,   89,   73,   19,   19,
           19,  104,  163,   14,  163,   22,   64,   22,   19,    7,
          163,  158,   76,   76,   76,   76,   76,   76,   76,   76,
            7,   76,   76,  115,   46,    7,   46,  113,    7,   76,
          120,  120,   19,  178,  178,   14,  117,   19,   19,   19,
           19,  187,  187,   19,  108,  108,   19,   19,    2,    2,
          108,  108,  108,  108,  108,  108,  108,  108,  108,  108,
          179,  179,   22,   83,  121,   83,  179,  179,  179,  179,
          179,  179,  179,  179,  179,  179,   29,    5,    5,   81,
           81,   81,   81,   81,   81,   81,   81,   81,   81,   80,
           23,   18,   23,   23,   23,  134,  134,  134,  165,  165,
           14,   93,   93,   93,  165,  134,  165,  165,  165,  165,
          165,  165,  165,  165,   83,   39,    9,    9,    9,   85,
           85,   85,    9,   92,    9,   28,    9,    9,   37,  169,
          134,  134,  134,  134,   25,   25,   25,   25,  110,    9,
            9,    9,    9,   99,  107,  107,  107,  107,    9,  107,
          133,    9,    9,   31,  180,  180,   41,  160,  151,  134,
          180,  180,  180,  180,  180,  180,  180,  180,  180,  180,
           24,  136,   24,   24,   24,   96,    9,   -1,   20,  114,
           -1,   -1,   22,   -1,   -1,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,   23,   -1,   -1,   23,  134,
          182,  182,   -1,  134,  134,  134,  182,  182,  182,  182,
          182,  182,  182,  182,  182,  182,   17,   17,   82,   82,
           17,   17,   -1,   82,   -1,   82,   -1,   82,   82,   82,
           -1,   82,   17,   -1,   82,   20,   20,   -1,   20,   82,
           17,   -1,   17,   17,   20,   -1,   -1,   -1,   17,  118,
           -1,   20,   -1,   16,   16,   16,   16,   -1,   -1,   -1,
           17,   -1,  118,   -1,   20,   20,   20,   16,   -1,   -1,
          118,  118,   -1,   -1,   -1,  118,  118,  118,  118,   59,
           59,   59,   59,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           16,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   16,   16,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,  107,  107
    );

    protected array $gotoBase = array(
            0,    0, -339,    0,    0,  174,   -7,  339,  171,   10,
            0,    0,  -69,  -36,  -78, -186,  130,   81,  114,   66,
          117,    0,   62,  160,  240,  468,  178,  225,  118,  112,
            0,   45,    0,    0,    0, -195,    0,  119,    0,  122,
            0,   44,   -1,  226,    0,  227, -387,    0, -715,  182,
          241,    0,    0,    0,    0,    0,  256,    0,    0,  570,
            0,    0,  269,    0,  102,    3,  -63,    0,    0,    0,
            0,    0,    0,   -5,    0,    0,  -31,    0,    0, -120,
          110,   53,   54,  120, -286,  -33, -724,    0,    0,   40,
            0,    0,  124,  129,    0,    0,   61, -488,    0,   67,
            0,    0,    0,  294,  295,    0,    0,  453,  141,    0,
          100,    0,    0,   83,   -3,   82,    0,   86,  318,   38,
           78,  107,    0,    0,    0,    0,    0,   16,    0,    0,
          168,   20,    0,  108,  163,    0,   58,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    4,    0,
            0,   43,    0,    0,    0,    0,    0,  193,  101,  -38,
           46,    0,    0, -166,    0,  195,    0,    0,    0,   92,
            0,    0,    0,    0,    0,    0,    0,  -60,   42,  157,
          251,  243,  297,    0,    0,  -97,    0,    1,  263,    0,
          276, -101,    0,    0
    );

    protected array $gotoDefault = array(
        -32768,  526,  755,    7,  756,  951,  831,  840,  590,  544,
          722,  356,  641,  432, 1352,  927, 1165,  610,  859, 1294,
         1300,  468,  862,  340,  745,  939,  910,  911,  408,  395,
          875,  406,  665,  642,  507,  895,  464,  887,  499,  890,
          463,  899,  167,  428,  524,  903,    6,  906,  572,  937,
          991,  396,  914,  397,  693,  916,  594,  918,  919,  403,
          409,  410, 1170,  602,  638,  931,  261,  596,  932,  394,
          933,  941,  399,  401,  703,  479,  518,  512,  421, 1132,
          597,  625,  662,  457,  486,  636,  648,  635,  493,  444,
          426,  339,  975,  983,  500,  477,  997,  358, 1005,  753,
         1178,  656,  502, 1013,  657, 1020, 1023,  545,  546,  491,
         1035,  271, 1038,  503, 1048,   26,  683, 1053, 1054,  684,
          658, 1076,  659,  685,  660, 1078,  476,  592, 1179,  475,
         1093, 1099,  465, 1102, 1340,  466, 1106,  270, 1109,  284,
          427,  445, 1115, 1116,   12, 1122,  713,  714,   25,  280,
          523, 1150,  704,-32768,-32768,-32768,-32768,  462, 1177,  461,
         1249, 1251,  573,  504, 1269,  301, 1272,  696,  519, 1277,
          458, 1343,  459,  547,  487,  325,  548, 1387,  315,  343,
          322,  564,  302,  344,  549,  488, 1349, 1357,  341,   34,
         1377, 1388,  607,  630
    );

    protected array $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    7,    8,    8,    9,   10,   11,
           11,   11,   12,   12,   13,   13,   14,   15,   15,   16,
           16,   17,   17,   18,   18,   21,   21,   22,   23,   23,
           24,   24,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   45,   46,   46,   48,   47,   47,   47,   47,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   25,   25,   50,   69,   69,   72,   72,
           71,   70,   70,   63,   75,   75,   76,   76,   77,   77,
           78,   78,   79,   79,   80,   80,   80,   80,   26,   26,
           27,   27,   27,   27,   27,   88,   88,   90,   90,   83,
           83,   91,   91,   92,   92,   92,   84,   84,   87,   87,
           85,   85,   93,   94,   94,   57,   57,   65,   65,   68,
           68,   68,   67,   95,   95,   96,   58,   58,   58,   58,
           97,   97,   98,   98,   99,   99,  100,  101,  101,  102,
          102,  103,  103,   55,   55,   51,   51,  105,   53,   53,
          106,   52,   52,   54,   54,   64,   64,   64,   64,   81,
           81,  109,  109,  111,  111,  112,  112,  112,  112,  112,
          112,  112,  112,  110,  110,  110,  115,  115,  115,  115,
           89,   89,  118,  118,  118,  119,  119,  116,  116,  120,
          120,  122,  122,  123,  123,  117,  124,  124,  121,  125,
          125,  125,  125,  113,  113,   82,   82,   82,   20,   20,
           20,  128,  128,  128,  128,  129,  129,  129,  127,  126,
          126,  131,  131,  131,  130,  130,   60,  132,  132,  133,
           61,  135,  135,  136,  136,  137,  137,   86,  138,  138,
          138,  138,  138,  138,  138,  143,  143,  144,  144,  145,
          145,  145,  145,  145,  146,  147,  147,  142,  142,  139,
          139,  141,  141,  149,  149,  148,  148,  148,  148,  148,
          148,  148,  148,  148,  148,  140,  150,  150,  152,  151,
          151,  153,  153,  114,  154,  154,  156,  156,  156,  155,
          155,   62,  104,  157,  157,   56,   56,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,  164,  165,  165,  166,  158,  158,  163,
          163,  167,  168,  168,  169,  170,  171,  171,  171,  171,
           19,   19,   73,   73,   73,   73,  159,  159,  159,  159,
          173,  173,  162,  162,  162,  160,  160,  179,  179,  179,
          179,  179,  179,  179,  179,  179,  179,  180,  180,  180,
          108,  182,  182,  182,  182,  161,  161,  161,  161,  161,
          161,  161,  161,   59,   59,  176,  176,  176,  176,  176,
          183,  183,  172,  172,  172,  172,  184,  184,  184,  184,
          184,  184,   74,   74,   66,   66,   66,   66,  134,  134,
          134,  134,  187,  186,  175,  175,  175,  175,  175,  175,
          175,  174,  174,  174,  185,  185,  185,  185,  107,  181,
          189,  189,  188,  188,  190,  190,  190,  190,  190,  190,
          190,  190,  178,  178,  178,  178,  177,  192,  191,  191,
          191,  191,  191,  191,  191,  191,  193,  193,  193,  193
    );

    protected array $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    0,
            1,    0,    1,    1,    2,    1,    3,    4,    1,    2,
            0,    1,    1,    1,    1,    4,    3,    5,    4,    3,
            4,    1,    3,    4,    1,    1,    8,    7,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    3,    2,    0,    1,    1,    1,    1,    1,    3,
            7,   10,    5,    7,    9,    5,    3,    3,    3,    3,
            3,    3,    1,    2,    5,    7,    9,    6,    5,    6,
            3,    2,    1,    1,    1,    1,    0,    2,    1,    3,
            8,    0,    4,    2,    1,    3,    0,    1,    0,    1,
            0,    1,    3,    1,    1,    1,    1,    1,    8,    9,
            7,    8,    7,    6,    8,    0,    2,    0,    2,    1,
            2,    1,    2,    1,    1,    1,    0,    2,    0,    2,
            0,    2,    2,    1,    3,    1,    4,    1,    4,    1,
            1,    4,    2,    1,    3,    3,    3,    4,    4,    5,
            0,    2,    4,    3,    1,    1,    7,    0,    2,    1,
            3,    3,    4,    1,    4,    0,    2,    5,    0,    2,
            6,    0,    2,    0,    3,    1,    2,    1,    1,    2,
            0,    1,    3,    0,    2,    1,    1,    1,    1,    1,
            1,    1,    1,    7,    9,    6,    1,    2,    1,    1,
            1,    1,    1,    1,    1,    1,    3,    3,    3,    1,
            3,    3,    3,    3,    3,    1,    3,    3,    1,    1,
            2,    1,    1,    0,    1,    0,    2,    2,    2,    4,
            3,    2,    4,    4,    3,    3,    1,    3,    1,    1,
            3,    2,    2,    3,    1,    1,    2,    3,    1,    1,
            2,    3,    1,    1,    3,    2,    0,    1,    5,    5,
            6,   10,    3,    5,    1,    1,    3,    0,    2,    4,
            5,    4,    4,    4,    3,    1,    1,    1,    1,    1,
            1,    0,    1,    1,    2,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    2,    1,    3,    1,    1,
            3,    0,    2,    0,    5,    8,    1,    3,    3,    0,
            2,    2,    2,    3,    1,    0,    1,    1,    3,    3,
            3,    4,    4,    1,    1,    2,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            2,    2,    2,    2,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    2,    2,    2,    2,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    5,    4,    3,
            4,    4,    2,    2,    4,    2,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    2,    1,    3,    2,
            1,    2,    4,    2,    2,    8,    9,    8,    9,    9,
           10,    9,   10,    8,    3,    2,    2,    1,    1,    0,
            4,    2,    1,    3,    2,    1,    2,    2,    2,    4,
            1,    1,    1,    1,    1,    1,    1,    1,    3,    1,
            1,    1,    0,    1,    1,    0,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    5,    3,
            3,    4,    1,    1,    3,    1,    1,    1,    1,    1,
            3,    2,    3,    0,    1,    1,    3,    1,    1,    1,
            1,    1,    1,    3,    1,    1,    1,    4,    4,    1,
            4,    4,    0,    1,    1,    1,    3,    3,    1,    4,
            2,    2,    1,    3,    1,    4,    4,    3,    3,    3,
            3,    1,    3,    1,    1,    3,    1,    1,    4,    1,
            1,    1,    3,    1,    1,    2,    1,    3,    4,    3,
            2,    0,    2,    2,    1,    2,    1,    1,    1,    4,
            3,    3,    3,    3,    6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
            0 => null,
            1 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleNamespaces($self->semStack[$stackPos-(1-1)]);
            },
            2 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            3 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            4 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            5 => null,
            6 => null,
            7 => null,
            8 => null,
            9 => null,
            10 => null,
            11 => null,
            12 => null,
            13 => null,
            14 => null,
            15 => null,
            16 => null,
            17 => null,
            18 => null,
            19 => null,
            20 => null,
            21 => null,
            22 => null,
            23 => null,
            24 => null,
            25 => null,
            26 => null,
            27 => null,
            28 => null,
            29 => null,
            30 => null,
            31 => null,
            32 => null,
            33 => null,
            34 => null,
            35 => null,
            36 => null,
            37 => null,
            38 => null,
            39 => null,
            40 => null,
            41 => null,
            42 => null,
            43 => null,
            44 => null,
            45 => null,
            46 => null,
            47 => null,
            48 => null,
            49 => null,
            50 => null,
            51 => null,
            52 => null,
            53 => null,
            54 => null,
            55 => null,
            56 => null,
            57 => null,
            58 => null,
            59 => null,
            60 => null,
            61 => null,
            62 => null,
            63 => null,
            64 => null,
            65 => null,
            66 => null,
            67 => null,
            68 => null,
            69 => null,
            70 => null,
            71 => null,
            72 => null,
            73 => null,
            74 => null,
            75 => null,
            76 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; if ($self->semValue === "<?=") $self->emitError(new Error('Cannot use "<?=" as an identifier', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            77 => null,
            78 => null,
            79 => null,
            80 => null,
            81 => null,
            82 => null,
            83 => null,
            84 => null,
            85 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            86 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            87 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            88 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            89 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            90 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            91 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            92 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            93 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            94 => null,
            95 => static function ($self, $stackPos) {
                 $self->semValue = new Name(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            96 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            97 => static function ($self, $stackPos) {
                 /* nothing */
            },
            98 => static function ($self, $stackPos) {
                 /* nothing */
            },
            99 => static function ($self, $stackPos) {
                 /* nothing */
            },
            100 => static function ($self, $stackPos) {
                 $self->emitError(new Error('A trailing comma is not allowed here', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            101 => null,
            102 => null,
            103 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(1-1)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            104 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            105 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            106 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            107 => static function ($self, $stackPos) {
                 $self->semValue = new Node\AttributeGroup($self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            108 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            109 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            110 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            111 => null,
            112 => null,
            113 => null,
            114 => null,
            115 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\HaltCompiler($self->handleHaltCompiler(), $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            116 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(3-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $self->checkNamespace($self->semValue);
            },
            117 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            118 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_(null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            119 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            120 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            121 => null,
            122 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Const_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), []);
            },
            123 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Const_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(4-1)]);
            $self->checkConstantAttributes($self->semValue);
            },
            124 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-6)], $self->semStack[$stackPos-(8-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            127 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-5)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            128 => null,
            129 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            130 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            131 => null,
            132 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            133 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            134 => null,
            135 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            136 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            137 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            138 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            139 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            140 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            141 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)]; $self->semValue->type = $self->semStack[$stackPos-(2-1)];
            },
            143 => null,
            144 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            145 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            146 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            147 => null,
            148 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            149 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            150 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            151 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            152 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            153 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            154 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            155 => null,
            156 => null,
            157 => null,
            158 => static function ($self, $stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            159 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Block($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            160 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(7-3)], ['stmts' => $self->semStack[$stackPos-(7-5)], 'elseifs' => $self->semStack[$stackPos-(7-6)], 'else' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            161 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(10-3)], ['stmts' => $self->semStack[$stackPos-(10-6)], 'elseifs' => $self->semStack[$stackPos-(10-7)], 'else' => $self->semStack[$stackPos-(10-8)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            162 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\While_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            163 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Do_($self->semStack[$stackPos-(7-5)], $self->semStack[$stackPos-(7-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            164 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\For_(['init' => $self->semStack[$stackPos-(9-3)], 'cond' => $self->semStack[$stackPos-(9-5)], 'loop' => $self->semStack[$stackPos-(9-7)], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            165 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Switch_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            166 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Break_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            167 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Continue_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            168 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Return_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            169 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Global_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            170 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Static_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            171 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Echo_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            172 => static function ($self, $stackPos) {

        $self->semValue = new Stmt\InlineHTML($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
        $self->semValue->setAttribute('hasLeadingNewline', $self->inlineHtmlHasLeadingNewline($stackPos-(1-1)));

            },
            173 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Expression($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            174 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Unset_($self->semStack[$stackPos-(5-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            175 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $self->semStack[$stackPos-(7-5)][1], 'stmts' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            176 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-7)][0], ['keyVar' => $self->semStack[$stackPos-(9-5)], 'byRef' => $self->semStack[$stackPos-(9-7)][1], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            177 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(6-3)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-4)],  $self->tokenEndStack[$stackPos-(6-4)])), ['stmts' => $self->semStack[$stackPos-(6-6)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            178 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Declare_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            179 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TryCatch($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->checkTryCatch($self->semValue);
            },
            180 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Goto_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            181 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Label($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            182 => static function ($self, $stackPos) {
                 $self->semValue = null; /* means: no statement */
            },
            183 => null,
            184 => static function ($self, $stackPos) {
                 $self->semValue = $self->maybeCreateNop($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]);
            },
            185 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            186 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            187 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            188 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            189 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            190 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Catch_($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-7)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            191 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            192 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Finally_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            193 => null,
            194 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            195 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            196 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            197 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            198 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            199 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            200 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            201 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            202 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            203 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            204 => null,
            205 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            206 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            207 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            208 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(8-3)], ['byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-5)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            209 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(9-4)], ['byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-6)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            210 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(7-2)], ['type' => $self->semStack[$stackPos-(7-1)], 'extends' => $self->semStack[$stackPos-(7-3)], 'implements' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(7-2));
            },
            211 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(8-3)], ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(8-3));
            },
            212 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Interface_($self->semStack[$stackPos-(7-3)], ['extends' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => $self->semStack[$stackPos-(7-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkInterface($self->semValue, $stackPos-(7-3));
            },
            213 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Trait_($self->semStack[$stackPos-(6-3)], ['stmts' => $self->semStack[$stackPos-(6-5)], 'attrGroups' => $self->semStack[$stackPos-(6-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            214 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Enum_($self->semStack[$stackPos-(8-3)], ['scalarType' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkEnum($self->semValue, $stackPos-(8-3));
            },
            215 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            216 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            217 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            218 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            219 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            220 => null,
            221 => null,
            222 => static function ($self, $stackPos) {
                 $self->checkClassModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            223 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            224 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            225 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            226 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            227 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            228 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            229 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            230 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            231 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            232 => null,
            233 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            234 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            235 => null,
            236 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            237 => null,
            238 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            239 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            240 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            241 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            242 => null,
            243 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            244 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            245 => static function ($self, $stackPos) {
                 $self->semValue = new Node\DeclareItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            246 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            247 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            248 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            249 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(5-3)];
            },
            250 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            251 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            252 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_($self->semStack[$stackPos-(4-2)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            253 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_(null, $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            254 => null,
            255 => null,
            256 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Match_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            257 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            258 => null,
            259 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            260 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            261 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            262 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm(null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            263 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            264 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            265 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            266 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            267 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            268 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            269 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            270 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            271 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            272 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            273 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            274 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            275 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            276 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-2)], true);
            },
            277 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            278 => static function ($self, $stackPos) {
                 $self->semValue = array($self->fixupArrayDestructuring($self->semStack[$stackPos-(1-1)]), false);
            },
            279 => null,
            280 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            281 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            282 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            283 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            284 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            285 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            286 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            287 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            288 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            289 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            290 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            291 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            292 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            293 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(7-6)], null, $self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-4)], $self->semStack[$stackPos-(7-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-1)], $self->semStack[$stackPos-(7-7)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            294 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(9-6)], $self->semStack[$stackPos-(9-8)], $self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-4)], $self->semStack[$stackPos-(9-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(9-2)], $self->semStack[$stackPos-(9-1)], $self->semStack[$stackPos-(9-9)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            295 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])), null, $self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-4)], $self->semStack[$stackPos-(6-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-1)]);
            },
            296 => null,
            297 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            298 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            299 => null,
            300 => null,
            301 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Name('static', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            302 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleBuiltinTypes($self->semStack[$stackPos-(1-1)]);
            },
            303 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('array', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            304 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('callable', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            305 => null,
            306 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            307 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            308 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            309 => null,
            310 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            311 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            312 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            313 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            314 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            315 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            316 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            317 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            318 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            319 => null,
            320 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            321 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            322 => null,
            323 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            324 => null,
            325 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            326 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            327 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            328 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            329 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            330 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-2)]);
            },
            331 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            332 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            333 => static function ($self, $stackPos) {
                 $self->semValue = array(new Node\Arg($self->semStack[$stackPos-(4-2)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])));
            },
            334 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-2)]);
            },
            335 => static function ($self, $stackPos) {
                 $self->semValue = array(new Node\Arg($self->semStack[$stackPos-(3-1)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)]);
            },
            336 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            337 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            338 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VariadicPlaceholder($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            339 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            340 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            341 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], true, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            342 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], false, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            343 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(3-3)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(3-1)]);
            },
            344 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(1-1)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            345 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            346 => null,
            347 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            348 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            349 => null,
            350 => null,
            351 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            352 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            353 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            354 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            355 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)]; } else { $self->semValue = $self->semStack[$stackPos-(2-1)]; }
            },
            356 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            357 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            358 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Property($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-1)]);
            },
            359 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-1)]);
            $self->checkClassConst($self->semValue, $stackPos-(5-2));
            },
            360 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-1)], $self->semStack[$stackPos-(6-4)]);
            $self->checkClassConst($self->semValue, $stackPos-(6-2));
            },
            361 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassMethod($self->semStack[$stackPos-(10-5)], ['type' => $self->semStack[$stackPos-(10-2)], 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-7)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClassMethod($self->semValue, $stackPos-(10-2));
            },
            362 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUse($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            363 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\EnumCase($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            364 => static function ($self, $stackPos) {
                 $self->semValue = null; /* will be skipped */
            },
            365 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            366 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            367 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            368 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            369 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Precedence($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            370 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(5-1)][0], $self->semStack[$stackPos-(5-1)][1], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            371 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            372 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            373 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            374 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            375 => null,
            376 => static function ($self, $stackPos) {
                 $self->semValue = array(null, $self->semStack[$stackPos-(1-1)]);
            },
            377 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            378 => null,
            379 => null,
            380 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            381 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            382 => null,
            383 => null,
            384 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            385 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            386 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            387 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            388 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            389 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            390 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            391 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::STATIC;
            },
            392 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            393 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            394 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            395 => null,
            396 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            397 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            398 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VarLikeIdentifier(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            399 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            400 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            401 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            402 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            403 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            404 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-5)], ['flags' => $self->semStack[$stackPos-(5-2)], 'byRef' => $self->semStack[$stackPos-(5-3)], 'params' => [], 'attrGroups' => $self->semStack[$stackPos-(5-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, null);
            },
            405 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-8)], ['flags' => $self->semStack[$stackPos-(8-2)], 'byRef' => $self->semStack[$stackPos-(8-3)], 'params' => $self->semStack[$stackPos-(8-6)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, $stackPos-(8-5));
            },
            406 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            407 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            408 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            409 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            410 => static function ($self, $stackPos) {
                 $self->checkPropertyHookModifiers($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            411 => null,
            412 => null,
            413 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            414 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            415 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            416 => null,
            417 => null,
            418 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            419 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->fixupArrayDestructuring($self->semStack[$stackPos-(3-1)]), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            420 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            421 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            422 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            if (!$self->phpVersion->allowsAssignNewByReference()) {
                $self->emitError(new Error('Cannot assign new by reference', $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])));
            }

            },
            423 => null,
            424 => null,
            425 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall(new Node\Name($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)],  $self->tokenEndStack[$stackPos-(2-1)])), $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            426 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Clone_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            427 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            428 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            429 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            430 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            431 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            432 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            433 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            434 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            435 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            436 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            437 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            438 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            439 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            440 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostInc($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            441 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreInc($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            442 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostDec($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            443 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreDec($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            444 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            445 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            446 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            447 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            448 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            449 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            450 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            451 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            452 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            453 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            454 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            455 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            456 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            457 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            458 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            459 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            460 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            461 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            462 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryPlus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            463 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryMinus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            464 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BooleanNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            465 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BitwiseNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            466 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Identical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            467 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotIdentical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            468 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Equal($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            469 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            470 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Spaceship($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            471 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Smaller($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            472 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\SmallerOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            473 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Greater($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            474 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\GreaterOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            475 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Instanceof_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            476 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            477 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            478 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(4-1)], null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            479 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            480 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Isset_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            481 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Empty_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            482 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            483 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            484 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Eval_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            485 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            486 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            487 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getIntCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\Int_($self->semStack[$stackPos-(2-2)], $attrs);
            },
            488 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getFloatCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\Double($self->semStack[$stackPos-(2-2)], $attrs);
            },
            489 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getStringCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\String_($self->semStack[$stackPos-(2-2)], $attrs);
            },
            490 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Array_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            491 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Object_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            492 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getBoolCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\Bool_($self->semStack[$stackPos-(2-2)], $attrs);
            },
            493 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Unset_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            494 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Void_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            495 => static function ($self, $stackPos) {
                 $self->semValue = $self->createExitExpr($self->semStack[$stackPos-(2-1)], $stackPos-(2-1), $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            496 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ErrorSuppress($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            497 => null,
            498 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ShellExec($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            499 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Print_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            500 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_(null, null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            501 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(2-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            502 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            503 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\YieldFrom($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            504 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Throw_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            505 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'returnType' => $self->semStack[$stackPos-(8-6)], 'expr' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            506 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            507 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'uses' => $self->semStack[$stackPos-(8-6)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            508 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            509 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            510 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'returnType' => $self->semStack[$stackPos-(10-8)], 'expr' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            511 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            512 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'uses' => $self->semStack[$stackPos-(10-8)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            513 => static function ($self, $stackPos) {
                 $self->semValue = array(new Stmt\Class_(null, ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos])), $self->semStack[$stackPos-(8-3)]);
            $self->checkClass($self->semValue[0], -1);
            },
            514 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            515 => static function ($self, $stackPos) {
                 list($class, $ctorArgs) = $self->semStack[$stackPos-(2-2)]; $self->semValue = new Expr\New_($class, $ctorArgs, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            516 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(2-2)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            517 => null,
            518 => null,
            519 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            520 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            521 => null,
            522 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            523 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            524 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ClosureUse($self->semStack[$stackPos-(2-2)], $self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            525 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            526 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            527 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            528 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            529 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            530 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            531 => null,
            532 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            533 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            534 => static function ($self, $stackPos) {
                 $self->semValue = new Name\FullyQualified(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            535 => static function ($self, $stackPos) {
                 $self->semValue = new Name\Relative(substr($self->semStack[$stackPos-(1-1)], 10), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            536 => null,
            537 => null,
            538 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            539 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            540 => null,
            541 => null,
            542 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            543 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]); foreach ($self->semValue as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } };
            },
            544 => static function ($self, $stackPos) {
                 foreach ($self->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            545 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            546 => null,
            547 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ConstFetch($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            548 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Line($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            549 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\File($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            550 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Dir($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            551 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Class_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            552 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Trait_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            553 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Method($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            554 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Function_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            555 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Namespace_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            556 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Property($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            557 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            558 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            559 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)])), $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            560 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(3-2)], $attrs);
            },
            561 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_LONG;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(4-3)], $attrs);
            $self->createdArrays->attach($self->semValue);
            },
            562 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->createdArrays->attach($self->semValue);
            },
            563 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\String_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->supportsUnicodeEscapes());
            },
            564 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($self->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = new Scalar\InterpolatedString($self->semStack[$stackPos-(3-2)], $attrs);
            },
            565 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseLNumber($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->allowsInvalidOctals());
            },
            566 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\Float_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            567 => null,
            568 => null,
            569 => null,
            570 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            571 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(2-1)], '', $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(2-2)],  $self->tokenEndStack[$stackPos-(2-2)]), true);
            },
            572 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            573 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            574 => null,
            575 => null,
            576 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            577 => null,
            578 => null,
            579 => null,
            580 => null,
            581 => null,
            582 => null,
            583 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            584 => null,
            585 => null,
            586 => null,
            587 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            588 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            589 => null,
            590 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\MethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            591 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafeMethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            592 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            593 => null,
            594 => null,
            595 => null,
            596 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            597 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            598 => null,
            599 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            600 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            601 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])), $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            602 => static function ($self, $stackPos) {
                 $var = $self->semStack[$stackPos-(1-1)]->name; $self->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])) : $var;
            },
            603 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            604 => null,
            605 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            606 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            607 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            608 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            609 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            610 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            611 => null,
            612 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            613 => null,
            614 => null,
            615 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            616 => null,
            617 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            618 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\List_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])); $self->semValue->setAttribute('kind', Expr\List_::KIND_LIST);
            $self->postprocessList($self->semValue);
            },
            619 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $end = count($self->semValue)-1; if ($self->semValue[$end]->value instanceof Expr\Error) array_pop($self->semValue);
            },
            620 => null,
            621 => static function ($self, $stackPos) {
                 /* do nothing -- prevent default action of $$=$self->semStack[$1]. See $551. */
            },
            622 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            623 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            624 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            625 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            626 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            627 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            628 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-1)], true, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            629 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            630 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), true);
            },
            631 => static function ($self, $stackPos) {
                 /* Create an Error node now to remember the position. We'll later either report an error,
             or convert this into a null element, depending on whether this is a creation or destructuring context. */
          $attrs = $self->createEmptyElemAttributes($self->tokenPos);
          $self->semValue = new Node\ArrayItem(new Expr\Error($attrs), null, false, $attrs);
            },
            632 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            633 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            634 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            635 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)]);
            },
            636 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]); $attrs['rawValue'] = $self->semStack[$stackPos-(1-1)]; $self->semValue = new Node\InterpolatedStringPart($self->semStack[$stackPos-(1-1)], $attrs);
            },
            637 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            638 => null,
            639 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            640 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            641 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            642 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            643 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            644 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            645 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            646 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\String_($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            647 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            648 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString('-' . $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            649 => null,
        ];
    }
}
